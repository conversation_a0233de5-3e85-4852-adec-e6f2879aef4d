<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.10</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.aizuda</groupId>
    <artifactId>snail-job</artifactId>
    <version>${revision}</version>
    <name>snail-job</name>
    <description>snail-job</description>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>1.5.0</revision>
        <skipTests>true</skipTests>

        <netty-all.version>4.1.119.Final</netty-all.version>
        <hutool.version>5.8.32</hutool.version>
        <mybatis-spring.version>3.0.3</mybatis-spring.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <tinylog.version>1.3.6</tinylog.version>
        <tinylog2.version>2.6.2</tinylog2.version>
        <logtube.version>0.45.0</logtube.version>
        <commons-logging.version>1.3.4</commons-logging.version>
        <guava.version>33.3.0-jre</guava.version>
        <guava-retrying.version>2.0.0</guava-retrying.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <pekko.version>1.2.0-M1</pekko.version>
        <scala.version>2.13.9</scala.version>
        <java-jwt.version>4.4.0</java-jwt.version>
        <perf4j.version>0.9.16</perf4j.version>
        <aviator.version>5.3.3</aviator.version>
        <hessian.version>4.0.66</hessian.version>
        <QLExpress.version>3.3.4</QLExpress.version>

        <!--    grpc & protobuf    -->
        <grpc-java.version>1.71.0</grpc-java.version>
        <proto-google-common-protos.version>2.54.1</proto-google-common-protos.version>
        <protobuf-java.version>3.25.6</protobuf-java.version>

        <!--   non-springboot database connectors  -->
        <mariadb.version>3.1.4</mariadb.version>
        <kingbase.version>8.6.0</kingbase.version>
        <dameng.version>8.1.3.62</dameng.version>

        <!--   server-ui build tool     -->
        <node.version>v20.19.0</node.version>
        <pnpm.version>10.6.3</pnpm.version>

        <!--    plugins    -->
        <central-publishing-maven-plugin.version>0.5.0</central-publishing-maven-plugin.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--      SnailJob common      -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-common-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-common-log</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-common-client-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-common-server-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--      SnailJob datasource      -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-datasource-template</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-mariadb-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-mysql-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-postgres-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-oracle-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-sqlserver-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-dm8-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-kingbase-datasource</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--      SnailJob client      -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-retry-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-job-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--     MyBatis & Plus       -->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- >= 3.5.9 PaginationInnerInterceptor 需要单独引入 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!--    utils        -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>${guava-retrying.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>${hessian.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>QLExpress</artifactId>
                <version>${QLExpress.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-actor-typed_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-actor-testkit-typed_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>
            <dependency>
                <groupId>org.perf4j</groupId>
                <artifactId>perf4j</artifactId>
                <version>${perf4j.version}</version>
            </dependency>

            <!--     logging       -->
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons-logging.version}</version>
                <optional>true</optional>
            </dependency>

            <!--  gRPC  -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>${grpc-java.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-api</artifactId>
                <version>${grpc-java.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc-java.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc-java.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-util</artifactId>
                <version>${grpc-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.api.grpc</groupId>
                <artifactId>proto-google-common-protos</artifactId>
                <version>${proto-google-common-protos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>snail-job-common</module>
        <module>snail-job-server</module>
        <module>snail-job-client-starter</module>
        <module>snail-job-datasource</module>
        <module>snail-job-client</module>
    </modules>
    <packaging>pom</packaging>

    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>

    <scm>
        <url>https://gitee.com/aizuda/snail-job</url>
        <connection>https://gitee.com/aizuda/snail-job</connection>
        <developerConnection>https://gitee.com/aizuda/</developerConnection>
    </scm>

    <developers>
        <developer>
            <name>opensnail</name>
            <email><EMAIL></email>
            <url>https://gitee.com/opensnail</url>
        </developer>
    </developers>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>oss</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${maven-gpg-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <!-- Prevent `gpg` from using pinentry programs -->
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                                <configuration>
                                    <doclint>none</doclint>
                                    <javadocExecutable>${java.home}/bin/javadoc</javadocExecutable>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.central</groupId>
                        <artifactId>central-publishing-maven-plugin</artifactId>
                        <version>${central-publishing-maven-plugin.version}</version>
                        <extensions>true</extensions>
                        <configuration>
                            <publishingServerId>ossrh</publishingServerId>
                            <autoPublish>false</autoPublish>
                        </configuration>
                    </plugin>
                </plugins>
            </build>

            <distributionManagement>
                <snapshotRepository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
                <repository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
            </distributionManagement>

        </profile>
    </profiles>
</project>
