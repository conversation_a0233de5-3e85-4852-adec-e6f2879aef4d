package com.aizuda.snailjob.client.job.core.handler.add;

import com.aizuda.snailjob.client.job.core.enums.AllocationAlgorithmEnum;
import com.aizuda.snailjob.common.core.enums.JobTaskTypeEnum;

/**
 * <AUTHOR>
 * @date 2024-10-19 12:25:49
 * @since sj_1.2.0
 */
public class ShardingAddHandler extends AddHandler<ShardingAddHandler> {

    public ShardingAddHandler() {
        this(JobTaskTypeEnum.SHARDING);
    }

    public ShardingAddHandler(JobTaskTypeEnum taskType) {
        super(taskType);
        setRouteKey(AllocationAlgorithmEnum.ROUND);
        setR(this);
    }

    @Override
    public ShardingAddHandler addShardingArgs(String... shardingValue) {
        return super.addShardingArgs(shardingValue);
    }

    @Override
    public ShardingAddHandler setParallelNum(Integer parallelNum) {
        return super.setParallelNum(parallelNum);
    }
}
