package com.aizuda.snailjob.server.web.controller;

import com.aizuda.snailjob.server.web.annotation.LoginRequired;
import com.aizuda.snailjob.server.web.model.base.PageResult;
import com.aizuda.snailjob.server.web.model.request.LoginLogQueryVO;
import com.aizuda.snailjob.server.web.model.response.LoginLogResponseVO;
import com.aizuda.snailjob.server.web.service.LoginLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 登录日志控制器
 */
@RestController
@RequestMapping("/api/log/login")
@RequiredArgsConstructor
public class LoginLogController {

    private final LoginLogService loginLogService;

    /**
     * 分页查询登录日志
     *
     * @param queryVO 查询参数
     * @return 分页结果
     */
    @GetMapping("/list")
    @LoginRequired
    public PageResult<List<LoginLogResponseVO>> getLoginLogPage(LoginLogQueryVO queryVO) {
        return loginLogService.getLoginLogPage(queryVO);
    }
} 