package com.aizuda.snailjob.server.web.service.impl;

import com.aizuda.snailjob.server.web.entity.LoginLog;
import com.aizuda.snailjob.server.web.mapper.LoginLogMapper;
import com.aizuda.snailjob.server.web.model.base.PageResult;
import com.aizuda.snailjob.server.web.model.request.LoginLogQueryVO;
import com.aizuda.snailjob.server.web.model.response.LoginLogResponseVO;
import com.aizuda.snailjob.server.web.service.LoginLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 登录日志服务实现类
 */
@Service
@RequiredArgsConstructor
public class LoginLogServiceImpl implements LoginLogService {

    private final LoginLogMapper loginLogMapper;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public PageResult<List<LoginLogResponseVO>> getLoginLogPage(LoginLogQueryVO queryVO) {
        // 构建分页对象
        PageDTO<LoginLog> pageDTO = new PageDTO<>(queryVO.getCurrentPage(), queryVO.getPageSize());
        
        // 构建查询条件
        LambdaQueryWrapper<LoginLog> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(queryVO.getKhid())) {
            queryWrapper.eq(LoginLog::getKhid, queryVO.getKhid());
        }
        if (StringUtils.hasText(queryVO.getNsrsbh())) {
            queryWrapper.eq(LoginLog::getNsrsbh, queryVO.getNsrsbh());
        }
        if (StringUtils.hasText(queryVO.getStartTime())) {
            // 转换时间戳为日期字符串
            String startDateTime = sdf.format(new Date(Long.parseLong(queryVO.getStartTime())));
            queryWrapper.ge(LoginLog::getCreateTime, startDateTime);
        }
        if (StringUtils.hasText(queryVO.getEndTime())) {
            String endDateTime = sdf.format(new Date(Long.parseLong(queryVO.getEndTime())));
            queryWrapper.le(LoginLog::getCreateTime, endDateTime);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(LoginLog::getCreateTime);
        
        // 执行查询
        IPage<LoginLog> loginLogPage = loginLogMapper.selectPage(pageDTO, queryWrapper);
        
        // 转换为VO对象
        List<LoginLogResponseVO> loginLogVOList = loginLogPage.getRecords().stream()
                .map(loginLog -> {
                    LoginLogResponseVO vo = new LoginLogResponseVO();
                    BeanUtils.copyProperties(loginLog, vo);
                    return vo;
                })
                .collect(Collectors.toList());
        
        // 构建返回结果
        PageResult<List<LoginLogResponseVO>> pageResult = new PageResult<>(pageDTO, loginLogVOList);
        pageResult.setTotal(loginLogPage.getTotal());
        return pageResult;
    }
} 