package com.aizuda.snailjob.server.web.service.impl;

import com.aizuda.snailjob.server.web.entity.BusinessLogRecord;
import com.aizuda.snailjob.server.web.mapper.BusinessLogRecordMapper;
import com.aizuda.snailjob.server.web.model.base.PageResult;
import com.aizuda.snailjob.server.web.model.request.BusinessLogQueryVO;
import com.aizuda.snailjob.server.web.model.response.BusinessLogResponseVO;
import com.aizuda.snailjob.server.web.service.BusinessLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务日志服务实现类
 */
@Service
@RequiredArgsConstructor
public class BusinessLogServiceImpl implements BusinessLogService {

    private final BusinessLogRecordMapper businessLogRecordMapper;
    
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public PageResult<List<BusinessLogResponseVO>> getBusinessLogPage(BusinessLogQueryVO queryVO) {
        // 构建分页对象
        PageDTO<BusinessLogRecord> pageDTO = new PageDTO<>(queryVO.getCurrentPage(), queryVO.getPageSize());
        
        // 构建查询条件
        LambdaQueryWrapper<BusinessLogRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(queryVO.getEtaxNsrsbh())) {
            queryWrapper.eq(BusinessLogRecord::getEtaxNsrsbh, queryVO.getEtaxNsrsbh());
        }
        if (StringUtils.hasText(queryVO.getEtaxUsername())) {
            queryWrapper.eq(BusinessLogRecord::getEtaxUsername, queryVO.getEtaxUsername());
        }
        if (StringUtils.hasText(queryVO.getLsh())) {
            queryWrapper.like(BusinessLogRecord::getLsh, queryVO.getLsh());
        }
        if (StringUtils.hasText(queryVO.getStatus())) {
            queryWrapper.eq(BusinessLogRecord::getStatus, queryVO.getStatus());
        }
        if (StringUtils.hasText(queryVO.getStartTime())) {
            // 转换时间戳为日期字符串
            String startDateTime = sdf.format(new Date(Long.parseLong(queryVO.getStartTime())));
            queryWrapper.ge(BusinessLogRecord::getCreateTime, startDateTime);
        }
        if (StringUtils.hasText(queryVO.getEndTime())) {
            String endDateTime = sdf.format(new Date(Long.parseLong(queryVO.getEndTime())));
            queryWrapper.le(BusinessLogRecord::getCreateTime, endDateTime);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(BusinessLogRecord::getCreateTime);
        
        // 执行查询
        IPage<BusinessLogRecord> businessLogPage = businessLogRecordMapper.selectPage(pageDTO, queryWrapper);
        
        // 转换为VO对象
        List<BusinessLogResponseVO> businessLogVOList = businessLogPage.getRecords().stream()
                .map(businessLog -> {
                    BusinessLogResponseVO vo = new BusinessLogResponseVO();
                    BeanUtils.copyProperties(businessLog, vo);
                    
                    // 格式化时间
                    if (businessLog.getCreateTime() != null) {
                        vo.setCreateTime(businessLog.getCreateTime().format(dtf));
                    }
                    if (businessLog.getUpdateTime() != null) {
                        vo.setUpdateTime(businessLog.getUpdateTime().format(dtf));
                    }
                    
                    // 设置状态描述
                    if ("0".equals(businessLog.getStatus())) {
                        vo.setStatusDesc("导出中");
                    } else if ("1".equals(businessLog.getStatus())) {
                        vo.setStatusDesc("导出成功");
                    } else {
                        vo.setStatusDesc("未知状态");
                    }
                    
                    return vo;
                })
                .collect(Collectors.toList());
        
        // 构建返回结果
        PageResult<List<BusinessLogResponseVO>> pageResult = new PageResult<>(pageDTO, businessLogVOList);
        pageResult.setTotal(businessLogPage.getTotal());
        return pageResult;
    }
}
