package com.aizuda.snailjob.server.web.model.request;

import lombok.Data;

/**
 * 业务日志查询参数
 */
@Data
public class BusinessLogQueryVO {
    
    /**
     * 纳税人识别号
     */
    private String etaxNsrsbh;
    
    /**
     * 电局账户
     */
    private String etaxUsername;
    
    /**
     * 流水号
     */
    private String lsh;
    
    /**
     * 状态 0导出中 1导出成功
     */
    private String status;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 当前页
     */
    private Integer currentPage = 1;
    
    /**
     * 每页显示条数
     */
    private Integer pageSize = 10;
}
