package com.aizuda.snailjob.server.web.service;

import com.aizuda.snailjob.server.web.model.base.PageResult;
import com.aizuda.snailjob.server.web.model.request.BusinessLogQueryVO;
import com.aizuda.snailjob.server.web.model.response.BusinessLogResponseVO;

import java.util.List;

/**
 * 业务日志服务接口
 */
public interface BusinessLogService {

    /**
     * 分页查询业务日志
     *
     * @param queryVO 查询参数
     * @return 分页结果
     */
    PageResult<List<BusinessLogResponseVO>> getBusinessLogPage(BusinessLogQueryVO queryVO);

    /**
     * 根据流水号和纳税人识别号删除业务日志
     *
     * @param lsh 流水号
     * @param nsrsbh 纳税人识别号
     * @return 删除是否成功
     */
    Boolean deleteByLshAndNsrsbh(String lsh, String nsrsbh);
}
