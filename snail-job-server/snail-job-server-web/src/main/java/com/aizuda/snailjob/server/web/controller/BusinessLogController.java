package com.aizuda.snailjob.server.web.controller;

import com.aizuda.snailjob.server.web.annotation.LoginRequired;
import com.aizuda.snailjob.server.web.model.base.PageResult;
import com.aizuda.snailjob.server.web.model.request.BusinessLogQueryVO;
import com.aizuda.snailjob.server.web.model.response.BusinessLogResponseVO;
import com.aizuda.snailjob.server.web.service.BusinessLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 业务日志控制器
 */
@RestController
@RequestMapping("/api/log/business")
@RequiredArgsConstructor
public class BusinessLogController {

    private final BusinessLogService businessLogService;

    /**
     * 分页查询业务日志
     *
     * @param queryVO 查询参数
     * @return 分页结果
     */
    @GetMapping("/list")
    @LoginRequired
    public PageResult<List<BusinessLogResponseVO>> getBusinessLogPage(BusinessLogQueryVO queryVO) {
        return businessLogService.getBusinessLogPage(queryVO);
    }
}
