package com.aizuda.snailjob.server.web.model.response;

import lombok.Data;

/**
 * 业务日志响应对象
 */
@Data
public class BusinessLogResponseVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 纳税人识别号
     */
    private String etaxNsrsbh;
    
    /**
     * 电局账户
     */
    private String etaxUsername;
    
    /**
     * 流水号
     */
    private String lsh;
    
    /**
     * 状态 0导出中 1导出成功
     */
    private String status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 当前请求参数
     */
    private String jsonData;
    
    /**
     * 消息体
     */
    private String message;
    
    /**
     * 返回代码
     */
    private String retCode;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;
}
