server:
  port: 8080
  servlet:
    context-path: /snail-job

spring:
  main:
    banner-mode: off
  application:
    name: snail_java
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    active: dev



mybatis-plus:
  typeAliasesPackage: com.aizuda.snailjob.template.datasource.persistence.po
  global-config:
    db-config:
      where-strategy: NOT_EMPTY
      capital-mode: false
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true

logging:
  config: classpath:logback-boot.xml
#    level:
#    ## 方便调试 SQL
#        com.aizuda.snailjob.template.datasource.persistence.mapper: debug

snail-job:
  retry-pull-page-size: 1000 # 拉取重试数据的每批次的大小
  job-pull-page-size: 1000 # 拉取重试数据的每批次的大小
  server-port: 17888  # 服务器端口
  log-storage: 7 # 日志保存时间(单位: day)
  rpc-type: grpc
