spring:
  datasource:
    name: snail_job
    ## mysql
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**************:23306/snail_job?useSSL=false&characterEncoding=utf8&useUnicode=true
    username: root
    password: Ahhx@123
    ## postgres
    #    driver-class-name: org.postgresql.Driver
    #    url: *******************************************************************************************************************************************
    #    username: root
    #    password: root
    ## Oracle
    #    driver-class-name: oracle.jdbc.OracleDriver
    #    url: *****************************************
    #    username: snail_job
    #    password: SnailJob
    ## SQL Server
    #    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    #    url: **********************************************************************************************************************
    #    username: SA
    #    password: Snail<PERSON><PERSON>@24
    ## mariadb
    #    driver-class-name: org.mariadb.jdbc.Driver
    #    url: *******************************************************************************************
    #    username: root
    #    password: root
    ## dm8
    #    driver-class-name: dm.jdbc.driver.DmDriver
    #    url: jdbc:dm://127.0.0.1:5236
    #    username: SYSDBA
    #    password: SYSDBA001
    # kingbase
    #    driver-class-name: com.kingbase8.Driver
    #    url: *************************************
    #    username: root
    #    password: root
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      connection-timeout: 30000
      minimum-idle: 5
      maximum-pool-size: 100
      auto-commit: true
      idle-timeout: 30000
      pool-name: snail_job
      max-lifetime: 1800000
  web:
    resources:
      static-locations: classpath:admin/
