<configuration>

    <property name="log.base" value="snail-job-server"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{256} - %msg%n
            </Pattern>
        </encoder>
    </appender>

    <appender name="fileInfo"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./data/log/${log.base}/info/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>./data/log/${log.base}/info/info.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{256} - %msg%n</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="fileWarn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./data/log/${log.base}/warn/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>./data/log/${log.base}/warn/warn.%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{256} - %msg%n</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="fileError" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./data/log/${log.base}/error/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>./data/log/${log.base}/error/error.%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{256} - %msg%n</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="asyncInfo" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>100</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="fileInfo"/>
    </appender>

    <appender name="asyncWarn" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>100</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="fileWarn"/>
    </appender>

    <appender name="asyncError" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>100</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="fileError"/>
    </appender>

    <!-- SnailJob appender -->
    <appender name="snailJobLogServerAppender"
              class="com.aizuda.snailjob.server.common.appender.SnailJobServerLogbackAppender">
    </appender>

    <!-- 控制台输出日志级别 -->
    <root level="info">
        <appender-ref ref="stdout"/>
        <appender-ref ref="asyncInfo"/>
        <appender-ref ref="asyncWarn"/>
        <appender-ref ref="asyncError"/>
        <appender-ref ref="snailJobLogServerAppender"/>
    </root>
</configuration>
