import{_ as he,N as Se}from"./table-header-operation.vue_vue_type_script_setup_true_lang-CwAtVujA.js";import{_ as Te}from"./delete-alert-Cep_Tkqr.js";import{d as Z,a0 as te,r as G,a1 as W,z as Ne,A as ke,a as we,$ as a,i as Ce,an as De,n as Ue,q as Re,c as V,o as $,w as n,f as t,I as $e,C as Le,h as e,ad as ue,ae as E,aT as me,D as ye,E as xe,b as ie,a3 as se,a4 as pe,aH as re,aG as Ie,B as ee,g as U,t as R,a9 as ce,aU as Oe,aV as Ve,aW as Be,Q as fe,Z as K,aa as x,aX as ge,aK as Me,aY as _e,aZ as be,L as Ge,af as Ae,ag as H,G as Fe,a_ as le,a$ as je,ah as ze,ai as Ee}from"./index-DOt4wG7_.js";import{a as Pe,b as qe,c as He,d as de,e as Ke,g as We}from"./notify-C7xEafHT.js";import{u as Ze,a as Je}from"./table-BeU6nStB.js";import{f as Qe}from"./retry-scene-2DvULWmk.js";import{f as Xe}from"./workflow-CN6tzeNt.js";import{h as Ye}from"./job-Dl7uKq5c.js";import{_ as ve}from"./select-group.vue_vue_type_script_setup_true_lang-B2k8O5Ow.js";import{c as et,d as tt,_ as at,a as nt,b as ot}from"./Grid-B1tTyPaW.js";import{a as lt}from"./search-form-DjBMObUw.js";import{_ as it}from"./FormItemGridItem-DE5kWDnB.js";import{_ as st,a as rt}from"./DescriptionsItem-C9YMzXr_.js";import{_ as ut}from"./status-switch.vue_vue_type_script_setup_true_lang-DCMb3kln.js";import{u as pt}from"./auth-uZCUSXTn.js";import"./group-B-7eCC7I.js";import"./CollapseItem-B_nLIGZ5.js";const ft=Z({name:"NotifyConfigOperateDrawer",__name:"notify-config-operate-drawer",props:te({operateType:{},rowData:{}},{visible:{type:Boolean,default:!1},visibleModifiers:{},retryNotifyStatusDisable:{type:Boolean,default:!0},retryNotifyStatusDisableModifiers:{},retrySceneDisable:{type:Boolean,default:!0},retrySceneDisableModifiers:{}}),emits:te(["update:value","submitted"],["update:visible","update:retryNotifyStatusDisable","update:retrySceneDisable"]),setup(N,{emit:D}){const p=G([]),c=G([]),g=G([]),k=G([]),v=N,_=D,b=W(N,"visible"),h=W(N,"retryNotifyStatusDisable"),i=W(N,"retrySceneDisable"),f=G([]),{formRef:B,validate:P,restoreValidation:C}=Ne(),{defaultRequiredRule:L}=ke(),ae=we(()=>({add:a("page.notifyConfig.addNotifyConfig"),edit:a("page.notifyConfig.editNotifyConfig")})[v.operateType]);Ce(()=>{De(()=>{ne()})});async function ne(){const d=await Pe();p.value=d.data}const l=Ue(I());function I(){return{groupName:null,recipientIds:[],systemTaskType:null,notifyName:"",notifyStatus:1,notifyScene:null,notifyThreshold:16,rateLimiterStatus:1,rateLimiterThreshold:100,description:""}}const oe={groupName:L,systemTaskType:L,notifyName:L,notifyStatus:L,notifyScene:L,recipientIds:L,rateLimiterStatus:L,notifyThreshold:L};function J(){if(v.operateType==="add"){Object.assign(l,I()),i.value=!0,h.value=!0,f.value=[];return}v.operateType==="edit"&&v.rowData&&(Object.assign(l,v.rowData),s(l.systemTaskType),o(l.notifyScene))}function Q(){b.value=!1}async function X(){var d;if(await P(),v.operateType==="add"){const{groupName:r,recipientIds:m,systemTaskType:y,notifyName:T,notifyStatus:w,notifyScene:M,notifyThreshold:A,rateLimiterStatus:O,rateLimiterThreshold:F,description:j}=l,{error:q}=await qe({groupName:r,recipientIds:m,systemTaskType:y,notifyName:T,notifyStatus:w,notifyScene:M,notifyThreshold:A,rateLimiterStatus:O,rateLimiterThreshold:F,description:j});if(q)return}if(v.operateType==="edit"){const{id:r,groupName:m,recipientIds:y,notifyStatus:T,notifyName:w,systemTaskType:M,notifyScene:A,notifyThreshold:O,rateLimiterStatus:F,rateLimiterThreshold:j,description:q}=l,{error:Y}=await He({id:r,groupName:m,recipientIds:y,systemTaskType:M,notifyName:w,notifyStatus:T,notifyScene:A,notifyThreshold:O,rateLimiterStatus:F,rateLimiterThreshold:j,description:q});if(Y)return}(d=window.$message)==null||d.success(a("common.updateSuccess")),Q(),_("submitted")}async function s(d){var m,y;if(d===1){const T=await Qe({groupName:l.groupName});c.value=T.data,f.value=E(Oe)}else if(d===3){const T=await Ye({groupName:l.groupName});g.value=(m=T.data)==null?void 0:m.map(w=>(w.id=String(w.id),w)),f.value=E(Ve)}else if(d===4){const T=await Xe({groupName:l.groupName});k.value=(y=T.data)==null?void 0:y.map(w=>(w.id=String(w.id),w)),f.value=E(Be)}await o(l.notifyScene);let r=!1;f.value.map(T=>(T.value===l.notifyScene&&(r=!0),String(T.value))),r||(l.notifyScene=null)}async function o(d){i.value=!(l.systemTaskType===1&&(d===1||d===2||d===5||d===6)),d===7&&(l.notifyThreshold=0)}function S(d){J(),l.groupName=d,s(1),o(1)}return Re(b,()=>{b.value&&(J(),C())}),(d,r)=>{const m=Le,y=ue,T=ye,w=nt,M=xe,A=at,O=tt,F=Ie,j=et,q=$e,Y=ee;return $(),V(ce,{modelValue:b.value,"onUpdate:modelValue":r[10]||(r[10]=u=>b.value=u),title:ae.value,"min-size":480,onHandleSubmit:X},{footer:n(()=>[t(M,{size:16},{default:n(()=>[t(Y,{onClick:Q},{default:n(()=>[U(R(e(a)("common.cancel")),1)]),_:1}),t(Y,{type:"primary",onClick:X},{default:n(()=>[U(R(e(a)("common.save")),1)]),_:1})]),_:1})]),default:n(()=>[t(q,{ref_key:"formRef",ref:B,model:l,rules:oe},{default:n(()=>[t(m,{label:e(a)("page.notifyConfig.groupName"),path:"groupName"},{default:n(()=>[t(ve,{value:l.groupName,"onUpdate:value":r[0]||(r[0]=u=>l.groupName=u),"onUpdate:modelValue":S},null,8,["value"])]),_:1},8,["label"]),t(m,{label:e(a)("page.notifyConfig.systemTaskType"),path:"systemTaskType"},{default:n(()=>[t(y,{value:l.systemTaskType,"onUpdate:value":[r[1]||(r[1]=u=>l.systemTaskType=u),s],placeholder:e(a)("page.notifyConfig.form.systemTaskType"),options:e(E)(e(me))},null,8,["value","placeholder","options"])]),_:1},8,["label"]),t(m,{label:e(a)("page.notifyConfig.notifyScene"),path:"notifyScene"},{default:n(()=>[t(y,{value:l.notifyScene,"onUpdate:value":[r[2]||(r[2]=u=>l.notifyScene=u),o],placeholder:e(a)("page.notifyConfig.form.notifyScene"),options:f.value},null,8,["value","placeholder","options"])]),_:1},8,["label"]),t(m,{label:e(a)("page.notifyConfig.notifyRecipient"),path:"recipientIds"},{default:n(()=>[t(y,{value:l.recipientIds,"onUpdate:value":r[3]||(r[3]=u=>l.recipientIds=u),placeholder:e(a)("page.notifyConfig.form.notifyRecipient"),options:p.value,clearable:"",multiple:""},null,8,["value","placeholder","options"])]),_:1},8,["label"]),t(m,{label:e(a)("page.notifyConfig.notifyName"),path:"notifyName"},{default:n(()=>[t(T,{value:l.notifyName,"onUpdate:value":r[4]||(r[4]=u=>l.notifyName=u),placeholder:e(a)("page.notifyConfig.form.notifyName"),maxlength:32},null,8,["value","placeholder"])]),_:1},8,["label"]),t(j,{cols:"2 s:1 m:2",responsive:"screen","x-gap":"20"},{default:n(()=>[t(O,null,{default:n(()=>[t(m,{label:e(a)("page.notifyConfig.notifyStatus"),path:"notifyStatus"},{default:n(()=>[t(A,{value:l.notifyStatus,"onUpdate:value":r[5]||(r[5]=u=>l.notifyStatus=u),name:"notifyStatus"},{default:n(()=>[t(M,null,{default:n(()=>[($(!0),ie(se,null,pe(e(re),u=>($(),V(w,{key:u.value,value:u.value,label:e(a)(u.label)},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value"])]),_:1},8,["label"])]),_:1}),t(O,null,{default:n(()=>[t(m,{label:e(a)("page.notifyConfig.notifyThreshold"),path:"notifyThreshold"},{default:n(()=>[t(F,{value:l.notifyThreshold,"onUpdate:value":r[6]||(r[6]=u=>l.notifyThreshold=u),min:1,placeholder:e(a)("page.notifyConfig.form.notifyThreshold"),disabled:i.value},null,8,["value","placeholder","disabled"])]),_:1},8,["label"])]),_:1})]),_:1}),t(j,{cols:"2 s:1 m:2",responsive:"screen","x-gap":"20"},{default:n(()=>[t(O,null,{default:n(()=>[t(m,{label:e(a)("page.notifyConfig.rateLimiterStatus"),path:"rateLimiterStatus"},{default:n(()=>[t(A,{value:l.rateLimiterStatus,"onUpdate:value":r[7]||(r[7]=u=>l.rateLimiterStatus=u),name:"rateLimiterStatus",disabled:i.value},{default:n(()=>[t(M,null,{default:n(()=>[($(!0),ie(se,null,pe(e(re),u=>($(),V(w,{key:u.value,value:u.value,label:e(a)(u.label)},null,8,["value","label"]))),128))]),_:1})]),_:1},8,["value","disabled"])]),_:1},8,["label"])]),_:1}),t(O,null,{default:n(()=>[t(m,{label:e(a)("page.notifyConfig.rateLimiterThreshold"),path:"notifyThreshold"},{default:n(()=>[t(F,{value:l.rateLimiterThreshold,"onUpdate:value":r[8]||(r[8]=u=>l.rateLimiterThreshold=u),min:1,placeholder:e(a)("page.notifyConfig.form.notifyThreshold"),disabled:i.value},null,8,["value","placeholder","disabled"])]),_:1},8,["label"])]),_:1})]),_:1}),t(m,{label:e(a)("page.notifyConfig.description"),path:"description"},{default:n(()=>[t(T,{value:l.description,"onUpdate:value":r[9]||(r[9]=u=>l.description=u),type:"textarea",placeholder:e(a)("page.notifyConfig.form.description")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}}),dt=Z({name:"SystemTaskType",__name:"system-task-type",emits:["update:value"],setup(N,{emit:D}){const p=D,c=G(),g=k=>{p("update:value",k)};return(k,v)=>{const _=ue;return $(),V(_,{value:c.value,"onUpdate:value":[v[0]||(v[0]=b=>c.value=b),g],placeholder:e(a)("page.notifyConfig.systemTaskType"),options:e(E)(e(me))},null,8,["value","placeholder","options"])}}}),mt=Z({name:"NotifyConfigSearch",__name:"notify-config-search",props:{model:{required:!0},modelModifiers:{}},emits:te(["reset","search"],["update:model"]),setup(N,{emit:D}){const p=D,c=W(N,"model");function g(){p("reset")}function k(){p("search")}return(v,_)=>{const b=ye,h=it,i=ve,f=dt,B=ue,P=lt;return $(),V(P,{"btn-span":"12 s:24 m:24 1:24 xl:24",model:c.value,onSearch:k,onReset:g},{default:n(()=>[t(h,{span:"24 s:12 m:6",label:e(a)("page.notifyConfig.notifyName"),path:"notifyName",class:"pr-24px"},{default:n(()=>[t(b,{value:c.value.notifyName,"onUpdate:value":_[0]||(_[0]=C=>c.value.notifyName=C),placeholder:e(a)("page.notifyConfig.form.notifyName"),clearable:""},null,8,["value","placeholder"])]),_:1},8,["label"]),t(h,{span:"24 s:12 m:6",label:e(a)("page.notifyConfig.groupName"),path:"groupName",class:"pr-24px"},{default:n(()=>[t(i,{value:c.value.groupName,"onUpdate:value":_[1]||(_[1]=C=>c.value.groupName=C),clearable:""},null,8,["value"])]),_:1},8,["label"]),t(h,{span:"24 s:12 m:6",label:e(a)("page.notifyConfig.systemTaskType"),path:"systemTaskType",class:"pr-24px"},{default:n(()=>[t(f,{value:c.value.systemTaskType,"onUpdate:value":_[2]||(_[2]=C=>c.value.systemTaskType=C),clearable:""},null,8,["value"])]),_:1},8,["label"]),t(h,{span:"24 s:12 m:6",label:e(a)("page.notifyConfig.notifyStatus"),path:"notifyStatus",class:"pr-24px"},{default:n(()=>[t(B,{value:c.value.notifyStatus,"onUpdate:value":_[3]||(_[3]=C=>c.value.notifyStatus=C),placeholder:e(a)("page.notifyConfig.notifyStatus"),options:e(E)(e(re)),clearable:""},null,8,["value","placeholder","options"])]),_:1},8,["label"])]),_:1},8,["model"])}}}),yt=Z({name:"NotifyConfigDetailDrawer",__name:"notify-config-detail-drawer",props:te({rowData:{}},{visible:{type:Boolean,default:!1},visibleModifiers:{}}),emits:["update:visible"],setup(N){const D=W(N,"visible");return(p,c)=>{const g=rt,k=K,v=st,_=ce;return $(),V(_,{modelValue:D.value,"onUpdate:modelValue":c[0]||(c[0]=b=>D.value=b),title:e(a)("page.groupConfig.detail")},{default:n(()=>[t(v,{"label-placement":"top",bordered:"",column:2},{default:n(()=>{var b,h;return[t(g,{label:e(a)("page.notifyConfig.notifyName"),span:2},{default:n(()=>{var i;return[U(R((i=p.rowData)==null?void 0:i.notifyName),1)]}),_:1},8,["label"]),t(g,{label:e(a)("page.groupConfig.groupName"),span:2},{default:n(()=>{var i;return[U(R((i=p.rowData)==null?void 0:i.groupName),1)]}),_:1},8,["label"]),t(g,{label:e(a)("page.notifyConfig.systemTaskType"),span:1},{default:n(()=>{var i;return[t(k,{type:e(x)((i=p.rowData)==null?void 0:i.systemTaskType)},{default:n(()=>{var f;return[U(R(e(a)(e(ge)[(f=p.rowData)==null?void 0:f.systemTaskType])),1)]}),_:1},8,["type"])]}),_:1},8,["label"]),t(g,{label:e(a)("page.notifyConfig.notifyStatus"),span:1},{default:n(()=>{var i;return[t(k,{type:e(x)((i=p.rowData)==null?void 0:i.notifyStatus)},{default:n(()=>{var f;return[U(R(e(a)(e(Me)[(f=p.rowData)==null?void 0:f.notifyStatus])),1)]}),_:1},8,["type"])]}),_:1},8,["label"]),((b=p.rowData)==null?void 0:b.systemTaskType)===1?($(),V(g,{key:0,label:e(a)("page.notifyConfig.notifyScene"),span:1},{default:n(()=>{var i;return[t(k,{type:e(x)((i=p.rowData)==null?void 0:i.notifyScene)},{default:n(()=>{var f;return[U(R(e(a)(e(_e)[(f=p.rowData)==null?void 0:f.notifyScene])),1)]}),_:1},8,["type"])]}),_:1},8,["label"])):fe("",!0),((h=p.rowData)==null?void 0:h.systemTaskType)===3?($(),V(g,{key:1,label:e(a)("page.notifyConfig.notifyScene"),span:1},{default:n(()=>{var i;return[t(k,{type:e(x)((i=p.rowData)==null?void 0:i.notifyScene)},{default:n(()=>{var f;return[U(R(e(a)(e(be)[(f=p.rowData)==null?void 0:f.notifyScene])),1)]}),_:1},8,["type"])]}),_:1},8,["label"])):fe("",!0),t(g,{label:e(a)("page.notifyConfig.notifyThreshold"),span:1},{default:n(()=>{var i;return[t(k,{type:e(x)((i=p.rowData)==null?void 0:i.notifyThreshold)},{default:n(()=>{var f;return[U(R((f=p.rowData)==null?void 0:f.notifyThreshold),1)]}),_:1},8,["type"])]}),_:1},8,["label"]),t(g,{label:e(a)("common.createDt"),span:2},{default:n(()=>{var i;return[U(R((i=p.rowData)==null?void 0:i.createDt),1)]}),_:1},8,["label"]),t(g,{label:e(a)("page.notifyConfig.description"),span:2},{default:n(()=>{var i;return[U(R((i=p.rowData)==null?void 0:i.description),1)]}),_:1},8,["label"])]}),_:1})]),_:1},8,["modelValue","title"])}}}),ct={class:"min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"};function z(N){return typeof N=="function"||Object.prototype.toString.call(N)==="[object Object]"&&!Ee(N)}const It=Z({name:"notify_config",__name:"index",setup(N){const{hasAuth:D}=pt(),p=Ge(),c=G(),{bool:g,setTrue:k}=Ae(!1),{columns:v,columnChecks:_,data:b,getData:h,loading:i,mobilePagination:f,searchParams:B,resetSearchParams:P}=Ze({apiFn:Ke,apiParams:{page:1,size:10,groupName:null,notifyStatus:null,notifyScene:null,notifyName:null,systemTaskType:null},columns:()=>[{type:"selection",align:"center",width:48},{key:"id",title:a("common.index"),align:"center",width:64},{key:"notifyName",title:a("page.notifyConfig.notifyName"),align:"left",width:120,render:s=>{function o(){c.value=s||null,k()}return t(ee,{text:!0,tag:"a",type:"primary",onClick:o,class:"ws-normal"},{default:()=>[s.notifyName]})}},{key:"groupName",title:a("page.notifyConfig.groupName"),align:"left",width:120},{key:"systemTaskType",title:a("page.notifyConfig.systemTaskType"),align:"left",width:120,render:s=>{if(s.systemTaskType===null)return null;const o=a(ge[s.systemTaskType]);return t(K,{type:x(s.systemTaskType)},z(o)?o:{default:()=>[o]})}},{key:"notifyStatus",title:a("page.notifyConfig.notifyStatus"),align:"left",width:120,render:s=>{const o=async(S,d)=>{var m;const{error:r}=await We(s.id,S);r||(s.notifyStatus=S,(m=window.$message)==null||m.success(a("common.updateSuccess"))),d(!r)};return t(ut,{value:s.notifyStatus,"onUpdate:value":S=>s.notifyStatus=S,onSubmitted:o},null)}},{key:"notifyName",title:a("page.notifyConfig.notifyName"),align:"left",width:120},{key:"notifyScene",title:a("page.notifyConfig.notifyScene"),align:"left",width:160,render:s=>{if(s.notifyScene===null)return null;if(s.systemTaskType===1){const o=a(_e[s.notifyScene]);return t(K,{type:x(s.notifyScene)},{default:()=>[t(le,{class:"w-136px"},z(o)?o:{default:()=>[o]})]})}if(s.systemTaskType===3){const o=a(be[s.notifyScene]);return t(K,{type:x(s.notifyScene)},{default:()=>[t(le,{class:"w-136px"},z(o)?o:{default:()=>[o]})]})}if(s.systemTaskType===4){const o=a(je[s.notifyScene]);return t(K,{type:x(s.notifyScene)},{default:()=>[t(le,{class:"w-136px"},z(o)?o:{default:()=>[o]})]})}return null}},{key:"notifyThreshold",title:a("page.notifyConfig.notifyThreshold"),align:"left",width:120},{key:"createDt",title:a("common.createDt"),align:"left",width:120},{key:"description",title:a("page.notifyConfig.description"),align:"left",width:120},{key:"operate",title:a("common.operate"),align:"center",width:130,fixed:"right",render:s=>{let o;return t("div",{class:"flex-center gap-8px"},[t(ee,{type:"primary",ghost:!0,text:!0,size:"small",onClick:()=>X(s.id)},z(o=a("common.edit"))?o:{default:()=>[o]}),D("R_ADMIN")?t(se,null,[t(ze,{vertical:!0},null),t(Se,{onPositiveClick:()=>Q(s.id)},{default:()=>a("common.confirmDelete"),trigger:()=>{let S;return t("span",null,[t(ee,{type:"error",text:!0,ghost:!0,size:"small"},z(S=a("common.delete"))?S:{default:()=>[S]})])}})]):""])}}]}),{drawerVisible:C,operateType:L,editingData:ae,handleAdd:ne,handleEdit:l,checkedRowKeys:I,onBatchDeleted:oe}=Je(b,h);async function J(){const{error:s}=await de(I.value);s||oe()}async function Q(s){var S;const{error:o}=await de([s]);o||((S=window.$message)==null||S.success(a("common.deleteSuccess")),h())}function X(s){l(s)}return(s,o)=>{const S=Te,d=he,r=ot,m=Fe;return $(),ie("div",ct,[t(mt,{model:e(B),"onUpdate:model":o[0]||(o[0]=y=>H(B)?B.value=y:null),onReset:e(P),onSearch:e(h)},null,8,["model","onReset","onSearch"]),t(S),t(m,{title:e(a)("page.notifyConfig.title"),bordered:!1,size:"small",class:"sm:flex-1-hidden card-wrapper","header-class":"view-card-header"},{"header-extra":n(()=>[t(d,{columns:e(_),"onUpdate:columns":o[1]||(o[1]=y=>H(_)?_.value=y:null),"disabled-delete":e(I).length===0,loading:e(i),"show-delete":e(D)("R_ADMIN"),onAdd:e(ne),onDelete:J,onRefresh:e(h)},null,8,["columns","disabled-delete","loading","show-delete","onAdd","onRefresh"])]),default:n(()=>[t(r,{"checked-row-keys":e(I),"onUpdate:checkedRowKeys":o[2]||(o[2]=y=>H(I)?I.value=y:null),columns:e(v),data:e(b),"flex-height":!e(p).isMobile,"scroll-x":962,loading:e(i),remote:"","row-key":y=>y.id,pagination:e(f),class:"sm:h-full"},null,8,["checked-row-keys","columns","data","flex-height","loading","row-key","pagination"]),t(ft,{visible:e(C),"onUpdate:visible":o[3]||(o[3]=y=>H(C)?C.value=y:null),"operate-type":e(L),"row-data":e(ae),onSubmitted:e(h)},null,8,["visible","operate-type","row-data","onSubmitted"]),t(yt,{visible:e(g),"onUpdate:visible":o[4]||(o[4]=y=>H(g)?g.value=y:null),"row-data":c.value},null,8,["visible","row-data"])]),_:1},8,["title"])])}}});export{It as default};
