import{d as q,a as M,m as se,b as U,o as k,e as a,i as ge,n as ce,p as we,r as O,q as le,c as J,s as re,v as z,x as D,h as l,t as G,T as _e,y as xe,z as ye,A as Xe,w,f as p,C as Ce,D as Se,$ as V,E as Te,F as be,G as de,B as Ye,_ as Be,g as pe,H as Me,I as Ie,J as ze,K as oe,L as ke,M as Ae,N as Ne,O as We,P as Le,Q as Pe,R as Re,S as He,U as $e,V as De,W as Oe,X as Ee,Y as Fe}from"./index-DOt4wG7_.js";const Ze={class:"absolute-lt z-1 size-full overflow-hidden"},Ve={class:"absolute -right-300px -top-900px lt-sm:-right-100px lt-sm:-top-1170px"},Ge={height:"1337",width:"1337"},Qe={id:"linearGradient-2",x1:"0.79",y1:"0.62",x2:"0.21",y2:"0.86"},Ue=["stop-color"],Je=["stop-color"],qe={class:"absolute -bottom-400px -left-200px lt-sm:-bottom-760px lt-sm:-left-100px"},je={height:"896",width:"967.8852157128662"},Ke={id:"linearGradient-3",x1:"0.5",y1:"0",x2:"0.5",y2:"1"},et=["stop-color"],tt=["stop-color"],ot=q({name:"WaveBg",__name:"wave-bg",props:{themeColor:{}},setup(c){const C=c,_=M(()=>se(C.themeColor,200)),s=M(()=>se(C.themeColor,500));return(A,r)=>(k(),U("div",Ze,[a("div",Ve,[(k(),U("svg",Ge,[a("defs",null,[r[0]||(r[0]=a("path",{id:"path-1",opacity:"1","fill-rule":"evenodd",d:"M1337,668.5 C1337,1037.455193874239 1037.455193874239,1337 668.5,1337 C523.6725684305388,1337 337,1236 370.50000000000006,1094 C434.03835568300906,824.6732385973953 6.906089672974592e-14,892.6277623047779 0,668.5000000000001 C0,299.5448061257611 299.5448061257609,1.1368683772161603e-13 668.4999999999999,0 C1037.455193874239,0 1337,299.544806125761 1337,668.5Z"},null,-1)),a("linearGradient",Qe,[a("stop",{offset:"0","stop-color":_.value,"stop-opacity":"1"},null,8,Ue),a("stop",{offset:"1","stop-color":s.value,"stop-opacity":"1"},null,8,Je)])]),r[1]||(r[1]=a("g",{opacity:"1"},[a("use",{"xlink:href":"#path-1",fill:"url(#linearGradient-2)","fill-opacity":"1"})],-1))]))]),a("div",qe,[(k(),U("svg",je,[a("defs",null,[r[2]||(r[2]=a("path",{id:"path-2",opacity:"1","fill-rule":"evenodd",d:"M896,448 C1142.6325445712241,465.5747656464056 695.2579309733121,896 448,896 C200.74206902668806,896 5.684341886080802e-14,695.2579309733121 0,448.0000000000001 C0,200.74206902668806 200.74206902668791,5.684341886080802e-14 447.99999999999994,0 C695.2579309733121,0 475,418 896,448Z"},null,-1)),a("linearGradient",Ke,[a("stop",{offset:"0","stop-color":s.value,"stop-opacity":"1"},null,8,et),a("stop",{offset:"1","stop-color":_.value,"stop-opacity":"1"},null,8,tt)])]),r[3]||(r[3]=a("g",{opacity:"1"},[a("use",{"xlink:href":"#path-2",fill:"url(#linearGradient-3)","fill-opacity":"1"})],-1))]))])]))}}),st="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAELklEQVRYR+2YW2wUZRTH//9vtlCoF9IoIklT3PqgPGi326hoetuaGEhIr9SgCYkkgt2WGOQVCca+GavWdr0GjD4YhG3RB3hply1LQA1tEQIxEXapGI2pEkys9LIzx2ylYWfY6e5sF0oi+7hzzvl+3/9855xvhrjNf7zN+XAHcL4Z+n8o6JWTeYt++W25S596AIZy6TB+n3yo+Nchlk8vmIIVowdXU9c3Q1gDSilBlQwjgBAYFGDvdF58/4milqvZwDpOcXWsb5Uh8hmBqkwXFMhlCN8aX5LXNbRy/T+Z+iXsHAFWRXs3QGQPyLucLDJrK5DgUXdTsxPfjAEro8E3Ce50EtxsKxPTwCPH3U2jTmJkBJgWTnAMxDeGMEoa0xQ+LJQnCD4HYFkCyAC3RdwN3U7gMkpxRTTYrMD91sCJIgCxV5R6O1Jcfy7VwonqLoj9/CqB2kF341qncGkBvRe+ureAWpRgoalCBecMFzcdK24YymZRJz5zprgq1tsJwXYL3CVZGvdGHmwZc7JQtra2gE+f712ep2QUYP714DJhaJrXLqXZQszlZwtYdSHoB9ljVk/ePVrSZFL0ZkAlxzQBVseCT8WhZhRThtFB8plk9Zi/qCi8cv0fNxvKFrDy4oF11NXXIFy2EII4iBcG3Y03VLZT8OqRd5aFPduvOEpxRayvXolxAKB2g6NgEhobBlc1HHYKY7WvHf5wtVAPgegIlbbZ9seUZ7AyFnwewi9pGoUyDmhrB931kfnC1ZwOeKlLP8GZJi6QLSFP2yep4toXSbT3ZQAfX3O6omt8Nhd9r/aHQAUMOQywYBZo5uZD2ThQ2rbPCjlnH6yI9rUryE5DU75ctJaake46Be4DuDjF8dFBNA94/AdtiySVxIlpMlTS8td801o70vMigM9huTda2lhcKHVHPO2HZv/P6LIwX7hk/+qzPSvUJGMkrg8AQYTkroRdXMlE+HH/twsG6BsOdJHYZlaO/lBZ6weOiiSXqs3Gqj0TeAxx+T75DIpgwjC0onD51pQD4JaluPrkR/cpFT9DcoVp84LOgTL/DjtBbglgou+puHwB8lEznPxJw1XSX77VtgizBvQNBw4RMqB7xt4Lc3c8lQKJaQHoO4R8ydz0/7MWoCXk8c85MrMC9J3qaafw/WtQlwXST+F3BnAeYB4obgJ1BJIuG+YtiKAjVOZ/Pd1ZdwzoG+4uBtSPpjaRbhXLcwF3hzytb2TilgVgT5BkYybBrTYC+Rvg5nRpdTRJrIs8+VPXPQXj2i4ItxC4O2NQQUQnN4U9rRcz9nH64p4ceM2lziX5Y4s3KHCdUHwE77ecMkMEp6BwhIa2Z6DslZRvfulgHafYLuCas58WLp2aLCFUga70qxOFU6dPFL2W1feYeaU43Y5z/TxnCuYabMEuC043ckdBp4pZ7f8FE5psOI1g6fwAAAAASUVORK5CYII=",at=["width","height"],nt=["width","height"],it=["width","height"],lt=a("div",{class:"loading-gif_"},[a("span"),a("span"),a("span"),a("span"),a("span")],-1),rt=[lt],ct={class:"auth-control_"},dt={class:"range-text"},pt=a("div",null,null,-1),ut=a("div",null,null,-1),ht=a("div",null,null,-1),mt=[pt,ut,ht],ft=q({__name:"App",props:{type:{type:String,default:"modal"},canvasWidth:{type:Number,default:310},canvasHeight:{type:Number,default:160},show:{type:Boolean,default:!1},puzzleScale:{type:Number,default:1},sliderSize:{type:Number,default:50},range:{type:Number,default:10},zIndex:{type:Number,default:999},imgs:{type:Array,default:null},successText:{type:String,default:"验证通过！"},failText:{type:String,default:"验证失败，请重试"},sliderText:{type:String,default:"拖动滑块完成拼图"},className:{type:String,default:""}},emits:["success","fail","close","reset"],setup(c,{expose:C,emit:_}){const s=c;ge(()=>{document.addEventListener("mousemove",b,!1),document.addEventListener("mouseup",x,!1),document.addEventListener("touchmove",b,{passive:!1}),document.addEventListener("touchend",x,!1),t.isInside=s.type==="inside",s.show&&(!t.isInside&&document.body.classList.add("vue-puzzle-overflow"),F())}),we(()=>{t.timer1&&clearTimeout(t.timer1),document.removeEventListener("mousemove",b,!1),document.removeEventListener("mouseup",x,!1),document.removeEventListener("touchmove",b,!1),document.removeEventListener("touchend",x,!1)});const A=O(),r=O(),f=O(),I=O(),t=ce({isInside:!1,mouseDown:!1,startWidth:50,startX:0,newX:0,pinX:0,pinY:0,loading:!1,isCanSlide:!1,error:!1,infoBoxShow:!1,infoText:"",infoBoxFail:!1,timer1:void 0,closeDown:!1,isSuccess:!1,imgIndex:-1,isSubmting:!1});le(()=>s.show,o=>{o?(!t.isInside&&document.body.classList.add("vue-puzzle-overflow"),F()):(t.isSubmting=!1,t.isSuccess=!1,t.infoBoxShow=!1,document.body.classList.remove("vue-puzzle-overflow"))}),le(()=>s.type,o=>{o==="inside"?(t.isInside=!0,document.body.classList.remove("vue-puzzle-overflow")):t.isInside=!1});const S=M(()=>{const o=t.startWidth+t.newX-t.startX;return o<u.value?u.value:o>s.canvasWidth?s.canvasWidth:o}),d=M(()=>Math.round(Math.max(Math.min(s.puzzleScale,2),.2)*52.5+6)),u=M(()=>Math.max(Math.min(Math.round(s.sliderSize),Math.round(s.canvasWidth*.5)),10)),g=M(()=>s.zIndex!==999?`z-index:${s.zIndex}`:""),m=()=>{!t.mouseDown&&!t.isSubmting&&(t.timer1&&clearTimeout(t.timer1),_("close"))},T=()=>{t.isInside||(t.closeDown=!0)},N=()=>{t.closeDown&&m(),t.closeDown=!1},W=o=>{var n;t.isCanSlide&&(t.mouseDown=!0,t.startWidth=((n=A.value)==null?void 0:n.clientWidth)||0,t.newX=o.clientX!==void 0?o.clientX:o.changedTouches[0].clientX,t.startX=o.clientX!==void 0?o.clientX:o.changedTouches[0].clientX)},b=o=>{t.mouseDown&&(o.preventDefault(),t.newX=o.clientX!==void 0?o.clientX:o.changedTouches[0].clientX)},x=()=>{t.mouseDown&&(t.mouseDown=!1,ue())},P=(o=!1)=>{var n;if(t.loading&&!o)return;t.loading=!0,t.isCanSlide=!1;const h=r.value,v=f.value,y=I.value,e=h==null?void 0:h.getContext("2d",{willReadFrequently:!0}),Y=v==null?void 0:v.getContext("2d"),Q=y==null?void 0:y.getContext("2d");if(!e||!Y||!Q){console.error("not found ctx / ctx2 / ctx3");return}const me=navigator.userAgent.indexOf("Firefox")>=0&&navigator.userAgent.indexOf("Windows")>=0,B=document.createElement("img");if(e.fillStyle="rgba(255,255,255,1)",Q.fillStyle="rgba(255,255,255,1)",e.clearRect(0,0,s.canvasWidth,s.canvasHeight),Y.clearRect(0,0,s.canvasWidth,s.canvasHeight),t.pinX=i(d.value+20,s.canvasWidth-d.value-10),t.pinY=i(20,s.canvasHeight-d.value-10),B.crossOrigin="anonymous",B.onload=()=>{const[X,K,ee,te]=j(B),ae=Math.random(),ne=Math.random(),ie=Math.random(),fe=Math.random(),Z=ae<.33?-1:ae<.66?0:1,H=ne<.33?-1:ne<.66?0:1,$=ie<.33?-1:ie<.66?0:1;let L=fe<.6?1:0;Z===H&&H===$&&$===L&&L===0&&(L=1),e.save(),R(e,Z,H,$,L),e.closePath(),me?(e.clip(),e.save(),e.shadowOffsetX=0,e.shadowOffsetY=0,e.shadowColor="#000",e.shadowBlur=3,e.fill(),e.restore()):(e.shadowOffsetX=0,e.shadowOffsetY=0,e.shadowColor="#000",e.shadowBlur=3,e.fill(),e.clip()),e.drawImage(B,X,K,ee,te),Q.fillRect(0,0,s.canvasWidth,s.canvasHeight),Q.drawImage(B,X,K,ee,te),e.globalCompositeOperation="source-atop",R(e,Z,H,$,L),e.arc(t.pinX+Math.ceil(d.value/2),t.pinY+Math.ceil(d.value/2),d.value*1.2,0,Math.PI*2,!0),e.closePath(),e.shadowColor="rgba(255, 255, 255, .8)",e.shadowOffsetX=-1,e.shadowOffsetY=-1,e.shadowBlur=Math.min(Math.ceil(8*s.puzzleScale),12),e.fillStyle="#ffffaa",e.fill();const ve=e.getImageData(t.pinX-3,t.pinY-20,t.pinX+d.value+5,t.pinY+d.value+5);Y.putImageData(ve,0,t.pinY-20),e.restore(),e.clearRect(0,0,s.canvasWidth,s.canvasHeight),e.save(),R(e,Z,H,$,L),e.globalAlpha=.8,e.fillStyle="#ffffff",e.fill(),e.restore(),e.save(),e.globalCompositeOperation="source-atop",R(e,Z,H,$,L),e.arc(t.pinX+Math.ceil(d.value/2),t.pinY+Math.ceil(d.value/2),d.value*1.2,0,Math.PI*2,!0),e.shadowColor="#000",e.shadowOffsetX=2,e.shadowOffsetY=2,e.shadowBlur=16,e.fill(),e.restore(),e.save(),e.globalCompositeOperation="destination-over",e.drawImage(B,X,K,ee,te),e.restore(),t.loading=!1,t.isCanSlide=!0},B.onerror=()=>{P(!0)},!o&&(n=s.imgs)!=null&&n.length){let X=i(0,s.imgs.length-1);X===t.imgIndex&&(X===s.imgs.length-1?X=0:X++),t.imgIndex=X,B.src=s.imgs[X]}else B.src=E()},i=(o,n)=>Math.ceil(Math.random()*(n-o)+o),j=o=>{const n=o.width/o.height,h=s.canvasWidth/s.canvasHeight;let v=0,y=0,e=0,Y=0;return n>h?(Y=s.canvasHeight,e=n*Y,y=0,v=(s.canvasWidth-e)/2):(e=s.canvasWidth,Y=e/n,v=0,y=(s.canvasHeight-Y)/2),[v,y,e,Y]},R=(o,n,h,v,y)=>{const e=Math.ceil(15*s.puzzleScale);o.beginPath(),o.moveTo(t.pinX,t.pinY),o.lineTo(t.pinX+e,t.pinY),o.arcTo(t.pinX+e,t.pinY+n*e/2,t.pinX+e+e/2,t.pinY+n*e/2,e/2),o.arcTo(t.pinX+e+e,t.pinY+n*e/2,t.pinX+e+e,t.pinY,e/2),o.lineTo(t.pinX+e+e+e,t.pinY),o.lineTo(t.pinX+e+e+e,t.pinY+e),o.arcTo(t.pinX+e+e+e+h*e/2,t.pinY+e,t.pinX+e+e+e+h*e/2,t.pinY+e+e/2,e/2),o.arcTo(t.pinX+e+e+e+h*e/2,t.pinY+e+e,t.pinX+e+e+e,t.pinY+e+e,e/2),o.lineTo(t.pinX+e+e+e,t.pinY+e+e+e),o.lineTo(t.pinX+e+e,t.pinY+e+e+e),o.arcTo(t.pinX+e+e,t.pinY+e+e+e+v*e/2,t.pinX+e+e/2,t.pinY+e+e+e+v*e/2,e/2),o.arcTo(t.pinX+e,t.pinY+e+e+e+v*e/2,t.pinX+e,t.pinY+e+e+e,e/2),o.lineTo(t.pinX,t.pinY+e+e+e),o.lineTo(t.pinX,t.pinY+e+e),o.arcTo(t.pinX+y*e/2,t.pinY+e+e,t.pinX+y*e/2,t.pinY+e+e/2,e/2),o.arcTo(t.pinX+y*e/2,t.pinY+e,t.pinX,t.pinY+e,e/2),o.lineTo(t.pinX,t.pinY)},E=()=>{const o=document.createElement("canvas"),n=o.getContext("2d");if(!n)return console.error("not found ctx"),"";o.width=s.canvasWidth,o.height=s.canvasHeight,n.fillStyle=`rgb(${i(100,255)},${i(100,255)},${i(100,255)})`,n.fillRect(0,0,s.canvasWidth,s.canvasHeight);for(let h=0;h<12;h++)if(n.fillStyle=`rgb(${i(100,255)},${i(100,255)},${i(100,255)})`,n.strokeStyle=`rgb(${i(100,255)},${i(100,255)},${i(100,255)})`,i(0,2)>1)n.save(),n.rotate(i(-90,90)*Math.PI/180),n.fillRect(i(-20,o.width-20),i(-20,o.height-20),i(10,o.width/2+10),i(10,o.height/2+10)),n.restore();else{n.beginPath();const v=i(-Math.PI,Math.PI);n.arc(i(0,o.width),i(0,o.height),i(10,o.height/2+10),v,v+Math.PI*1.5),n.closePath(),n.fill()}return o.toDataURL("image/png")},ue=()=>{t.isSubmting=!0;const o=S.value-u.value-(d.value-u.value)*((S.value-u.value)/(s.canvasWidth-u.value)),n=t.pinX-3-o;Math.abs(n)<s.range?(t.infoText=s.successText,t.infoBoxFail=!1,t.infoBoxShow=!0,t.isCanSlide=!1,t.isSuccess=!0,t.timer1&&clearTimeout(t.timer1),t.timer1=setTimeout(()=>{t.isSubmting=!1,_("success",n,{deviation:n,offsetX:o,pinX:t.pinX-3})},800)):(t.infoText=s.failText,t.infoBoxFail=!0,t.infoBoxShow=!0,t.isCanSlide=!1,_("fail",n,{deviation:n,offsetX:o,pinX:t.pinX-3}),t.timer1&&clearTimeout(t.timer1),t.timer1=setTimeout(()=>{t.isSubmting=!1,F()},800))},he=()=>{t.infoBoxFail=!1,t.infoBoxShow=!1,t.isCanSlide=!1,t.isSuccess=!1,t.startWidth=u.value,t.startX=0,t.newX=0},F=o=>{t.isSubmting||(he(),P(),o&&_("reset"))};return C({reset:F}),(o,n)=>(k(),J(_e,{to:"body",disabled:t.isInside},[a("div",{class:D(["vue-puzzle-vcode",{inside_:t.isInside,show_:c.show},c.className]),style:z(l(g)),onMousedown:T,onMouseup:N,onTouchstartPassive:T,onTouchend:N},[a("div",{class:"vue-auth-box_",onMousedown:n[3]||(n[3]=re(()=>{},["stop"])),onTouchstart:n[4]||(n[4]=re(()=>{},["stop"]))},[a("div",{class:"auth-body_",style:z(`width:${c.canvasWidth}px;height: ${c.canvasHeight}px`)},[a("canvas",{ref_key:"canvas1",ref:r,class:"auth-canvas1_",width:c.canvasWidth,height:c.canvasHeight},null,8,at),a("canvas",{ref_key:"canvas3",ref:I,class:D(["auth-canvas3_",{show:t.isSuccess}]),width:c.canvasWidth,height:c.canvasHeight},null,10,nt),a("canvas",{ref_key:"canvas2",ref:f,class:"auth-canvas2_",width:l(d),height:c.canvasHeight,style:z(`width:${l(d)}px;height:${c.canvasHeight}px;transform:translateX(${l(S)-l(u)-(l(d)-l(u))*((l(S)-l(u))/(c.canvasWidth-l(u)))}px)`)},null,12,it),a("div",{class:D(["loading-box_",{hide_:!t.loading}])},rt,2),a("div",{class:D(["info-box_",{show:t.infoBoxShow},{fail:t.infoBoxFail}])},G(t.infoText),3),a("div",{class:D(["flash_",{show:t.isSuccess}]),style:z(`transform: translateX(${t.isSuccess?`${c.canvasWidth+c.canvasHeight*.578}px`:`-${c.canvasHeight*.578}px`}) skew(-30deg, 0);`)},null,6),a("img",{class:"reset_",onClick:n[0]||(n[0]=h=>F(!0)),src:st})],4),a("div",ct,[a("div",{class:"range-box",style:z(`height:${l(u)}px;width:${c.canvasWidth}px`)},[a("div",dt,G(c.sliderText),1),a("div",{class:"range-slider",ref_key:"rangeSlider",ref:A,style:z(`width:${l(S)}px`)},[a("div",{class:D(["range-btn",{isDown:t.mouseDown}]),style:z(`width:${l(u)}px`),onMousedown:n[1]||(n[1]=h=>W(h)),onTouchstart:n[2]||(n[2]=h=>W(h))},mt,38)],4)],4)])],32)],38)],8,["disabled"]))}}),vt=q({name:"PwdLogin",__name:"pwd-login",setup(c){const C=xe(),{formRef:_,validate:s}=ye(),{defaultRequiredRule:A}=Xe(),r=ce({userName:"",password:""}),f={userName:A,password:A};async function I(){const m=ze(r.password);await C.login(r.userName,m)}const t=O(!1),S=async()=>{await s(),I()},d=()=>{t.value=!1},u=()=>{I()},g={padding:0};return(m,T)=>{const N=Se,W=Ce,b=Ye,x=Be,P=de,i=be,j=Te,R=Ie;return k(),J(R,{ref_key:"formRef",ref:_,model:r,rules:f,size:"large","show-label":!1,onKeyup:Me(I,["enter"])},{default:w(()=>[p(W,{path:"userName"},{default:w(()=>[p(N,{value:r.userName,"onUpdate:value":T[0]||(T[0]=E=>r.userName=E),placeholder:l(V)("page.login.common.userNamePlaceholder")},null,8,["value","placeholder"])]),_:1}),p(W,{path:"password"},{default:w(()=>[p(N,{value:r.password,"onUpdate:value":T[1]||(T[1]=E=>r.password=E),type:"password","show-password-on":"click",placeholder:l(V)("page.login.common.passwordPlaceholder")},null,8,["value","placeholder"])]),_:1}),p(j,{vertical:"",size:24},{default:w(()=>[p(i,{show:t.value,row:"",style:g},{trigger:w(()=>[p(b,{type:"primary",size:"large",round:"",block:"",loading:l(C).loginLoading,onClick:S},{default:w(()=>[pe(G(l(V)("page.login.common.login")),1)]),_:1},8,["loading"])]),default:w(()=>[p(P,{title:l(V)("page.login.common.codeTip"),"header-style":{padding:"10px 24px"}},{"header-extra":w(()=>[p(b,{text:"",onClick:d},{icon:w(()=>[p(x,{"local-icon":"close"})]),_:1})]),default:w(()=>[p(l(ft),{type:"inside",show:"",onSuccess:u,onClose:d})]),_:1},8,["title"])]),_:1},8,["show"])]),_:1})]),_:1},8,["model"])}}}),gt={VITE_APP_VERSION:"1.5.0"},wt={class:"w-400px lt-sm:w-300px"},_t={class:"flex-y-center justify-between"},xt={class:"flex text-28px text-primary font-500 lt-sm:text-22px"},yt={class:"mt-3px pl-12px text-16px color-#00000072 font-600"},Xt={class:"i-flex-col"},Ct={class:"pt-24px"},St={class:"pt-0px"},Tt={class:"pt-12px text-center"},Yt=q({name:"login",__name:"index",props:{module:{}},setup(c){const C=c,{VITE_APP_VERSION:_}=gt,s=O(`${oe.get("version")||_}`);(async()=>{const{data:g,error:m}=await Fe();if(!m&&g){s.value=g,oe.set("version",g);return}oe.remove("version")})();const r=ke(),f=Ae(),I={"pwd-login":{label:Ne["pwd-login"],component:vt}},t=M(()=>I[C.module||"pwd-login"]),S=M(()=>f.darkMode?se(f.themeColor,600):f.themeColor),d=M(()=>{const g="#ffffff",m=f.darkMode?.5:.2;return We(g,f.themeColor,m)}),u=g=>{window.open(g,"_blank")};return(g,m)=>{const T=ot,N=Le,W=Re,b=He,x=Oe,P=de;return k(),U("div",{class:"relative size-full flex-center overflow-hidden",style:z({backgroundColor:d.value})},[p(T,{"theme-color":S.value},null,8,["theme-color"]),p(P,{bordered:!1,class:"relative z-4 w-auto rd-12px"},{default:w(()=>[a("div",wt,[a("header",_t,[p(N,{class:"fill-primary text-64px lt-sm:text-48px"}),a("h3",xt,[pe(G(l(V)("system.title"))+" ",1),a("span",yt,"v"+G(s.value),1)]),a("div",Xt,[p(W,{"theme-schema":l(f).themeScheme,"show-tooltip":!1,class:"text-20px lt-sm:text-18px",onSwitch:l(f).toggleThemeScheme},null,8,["theme-schema","onSwitch"]),l(f).header.multilingual.visible?(k(),J(b,{key:0,lang:l(r).locale,"lang-options":l(r).localeOptions,"show-tooltip":!1,onChangeLang:l(r).changeLocale},null,8,["lang","lang-options","onChangeLang"])):Pe("",!0)])]),a("main",Ct,[a("div",St,[p($e,{name:l(f).page.animateMode,mode:"out-in",appear:""},{default:w(()=>[(k(),J(De(t.value.component)))]),_:1},8,["name"])]),a("div",Tt,[p(x,{"tooltip-content":"Mail",class:"color-#272636 dark:color-#f0f2f5",icon:"simple-icons:maildotru",onClick:m[0]||(m[0]=i=>u("mailto:<EMAIL>"))}),p(x,{class:"color-#c71d23","tooltip-content":"Gitee",icon:"simple-icons:gitee",onClick:m[1]||(m[1]=i=>u("https://gitee.com/aizuda/snail-job"))}),p(x,{"tooltip-content":"Github",class:"color-#010409 dark:color-#e6edf3",icon:"simple-icons:github",onClick:m[2]||(m[2]=i=>u("https://github.com/aizuda/snail-job"))})]),p(Ee)])])]),_:1})],4)}}});export{Yt as default};
