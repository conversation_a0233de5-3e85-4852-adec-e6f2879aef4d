import{aP as y,d as f,a0 as z,a1 as R,c as C,o as b,w as u,f as n,D as T,aQ as B,L as N,r as _,i as P,b as D,h as M,B as U,e as L,G as q}from"./index-DOt4wG7_.js";import{_ as G}from"./datetime-range.vue_vue_type_script_setup_true_lang-BJ-Gr1AY.js";import{a as I}from"./search-form-DjBMObUw.js";import{_ as V}from"./FormItemGridItem-DE5kWDnB.js";import{b as $}from"./Grid-B1tTyPaW.js";import"./CollapseItem-B_nLIGZ5.js";function A(g){return y({url:"/api/log/login/list",method:"get",params:g})}const E=f({name:"LogLoginSearch",__name:"log-login-search",props:{model:{required:!0},modelModifiers:{}},emits:z(["reset","search"],["update:model"]),setup(g,{emit:v}){const h=v,e=R(g,"model");function m(){h("reset")}function p(){h("search")}return(a,s)=>{const l=T,c=V;return b(),C(I,{model:e.value,onSearch:p,onReset:m,"btn-span":"24 s:12 m:4"},{default:u(()=>[n(c,{span:"24 s:12 m:6",label:"纳税人识别号",path:"nsrsbh",class:"pr-24px whitespace-nowrap","label-style":"margin-right: 20px;"},{default:u(()=>[n(l,{value:e.value.nsrsbh,"onUpdate:value":s[0]||(s[0]=d=>e.value.nsrsbh=d),placeholder:"请输入纳税人识别号",clearable:""},null,8,["value"])]),_:1}),n(c,{span:"24 s:12 m:14",label:"时间范围",path:"timeRange",class:"pr-24px","label-style":"margin-right: 12px;"},{default:u(()=>[n(G,{value:e.value.timeRange,"onUpdate:value":s[1]||(s[1]=d=>e.value.timeRange=d),class:"w-full"},null,8,["value"])]),_:1})]),_:1},8,["model"])}}}),F={class:"min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"},W=f({name:"log-login",__name:"index",setup(g){const v=B(),h=N(),e=_({khid:"",nsrsbh:"",timeRange:null}),m=_(!1),p=_([]),a=_({page:1,pageSize:10,showSizePicker:!0,pageSizes:[10,20,30,40],itemCount:0,onChange:t=>{a.value.page=t},onUpdatePageSize:t=>{a.value.pageSize=t,a.value.page=1}}),s=[{title:"纳税人识别号",key:"nsrsbh",width:180},{title:"返回码",key:"retcode",width:100},{title:"返回信息",key:"retmsg",width:200},{title:"创建时间",key:"createTime",width:180},{title:"更新时间",key:"updateTime",width:180}];async function l(){var t,o;m.value=!0;try{const i={page:a.value.page,pageSize:a.value.pageSize,khid:e.value.khid,nsrsbh:e.value.nsrsbh,startTime:(t=e.value.timeRange)!=null&&t[0]?new Date(e.value.timeRange[0]).getTime():void 0,endTime:(o=e.value.timeRange)!=null&&o[1]?new Date(e.value.timeRange[1]).getTime():void 0};console.log("请求参数:",i);const{data:r}=await A(i);console.log("接口返回数据:",r),r&&(p.value=r.data,a.value.itemCount=r.total,console.log("更新后的表格数据:",p.value),console.log("更新后的分页数据:",a.value))}catch(i){console.error("获取数据失败:",i),v.error("获取数据失败")}finally{m.value=!1}}function c(){a.value.page=1,l()}function d(){e.value={khid:"",nsrsbh:"",timeRange:null},c()}function w(t){a.value.page=t,l()}function x(t){a.value.pageSize=t,a.value.page=1,l()}return P(()=>{l()}),(t,o)=>{const i=U,r=$,S=q;return b(),D("div",F,[n(E,{model:e.value,"onUpdate:model":o[0]||(o[0]=k=>e.value=k),onReset:d,onSearch:c},null,8,["model"]),n(S,{title:"登录日志",bordered:!1,size:"small",class:"sm:flex-1-hidden card-wrapper","header-class":"view-card-header"},{"header-extra":u(()=>[n(i,{circle:"",secondary:"",onClick:l},{icon:u(()=>o[1]||(o[1]=[L("i",{class:"i-material-symbols:refresh"},null,-1)])),_:1})]),default:u(()=>[n(r,{columns:s,data:p.value,loading:m.value,"flex-height":!M(h).isMobile,"scroll-x":962,remote:"",pagination:a.value,class:"sm:h-full","onUpdate:page":w,"onUpdate:pageSize":x},null,8,["data","loading","flex-height","pagination"])]),_:1})])}}});export{W as default};
