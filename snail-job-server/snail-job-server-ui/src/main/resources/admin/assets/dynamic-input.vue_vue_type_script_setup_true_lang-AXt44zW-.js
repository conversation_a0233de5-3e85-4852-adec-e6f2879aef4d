import{d as C,a1 as g,c as r,o as v,w as o,f as u,D as G,H as p,s,g as M,Q as i,aG as V,ad as B,e as b}from"./index-DOt4wG7_.js";import{_ as q}from"./DynamicInput-DhrW2Ba2.js";import{c as D,d as O}from"./Grid-B1tTyPaW.js";import{_ as T}from"./FormItemGridItem-DE5kWDnB.js";const R=C({__name:"dynamic-input",props:{value:{required:!0,default:()=>[]},valueModifiers:{},path:{required:!0},pathModifiers:{}},emits:["update:value","update:path"],setup(d){const e=g(d,"value"),m=g(d,"path"),h=()=>({key:"",value:"",type:"string"}),y=[{trigger:["input","blur"],validator(n,a){return!a&&a!==0?new Error("不能为空"):!0}}],w=[{label:"string",value:"string"},{label:"number",value:"number"},{label:"boolean",value:"boolean"}],N=[{label:"true",value:1},{label:"false",value:0}],U=n=>{e.value[n].type==="string"&&(e.value[n].value=""),e.value[n].type==="boolean"&&(e.value[n].value=0),e.value[n].type==="number"&&(e.value[n].value=0)};return(n,a)=>{const c=G,_=T,k=O,$=V,f=B,K=D,I=q;return v(),r(I,{value:e.value,"onUpdate:value":a[5]||(a[5]=l=>e.value=l),"item-style":"margin-bottom: 0;","on-create":h},{default:o(({index:l})=>[u(K,null,{default:o(()=>[u(_,{span:7,"ignore-path-change":"","show-label":!1,rule:y,path:`${m.value}[${l}].key`},{default:o(()=>[u(c,{value:e.value[l].key,"onUpdate:value":t=>e.value[l].key=t,placeholder:"key",onKeydown:a[0]||(a[0]=p(s(()=>{},["prevent"]),["enter"]))},null,8,["value","onUpdate:value"])]),_:2},1032,["path"]),u(k,{span:2,class:"h-34px text-center lh-34px"},{default:o(()=>a[6]||(a[6]=[M("=")])),_:1}),u(_,{span:7,"ignore-path-change":"","show-label":!1,rule:y,path:`${m.value}[${l}].value`},{default:o(()=>[e.value[l].type==="string"?(v(),r(c,{key:0,value:e.value[l].value,"onUpdate:value":t=>e.value[l].value=t,placeholder:"value",onKeydown:a[1]||(a[1]=p(s(()=>{},["prevent"]),["enter"]))},null,8,["value","onUpdate:value"])):i("",!0),e.value[l].type==="number"?(v(),r($,{key:1,value:e.value[l].value,"onUpdate:value":t=>e.value[l].value=t,class:"w-full",placeholder:"value",onKeydown:a[2]||(a[2]=p(s(()=>{},["prevent"]),["enter"]))},null,8,["value","onUpdate:value"])):i("",!0),e.value[l].type==="boolean"?(v(),r(f,{key:2,value:e.value[l].value,"onUpdate:value":t=>e.value[l].value=t,class:"w-full",options:N,placeholder:"value",onKeydown:a[3]||(a[3]=p(s(()=>{},["prevent"]),["enter"]))},null,8,["value","onUpdate:value"])):i("",!0)]),_:2},1032,["path"]),u(_,{span:3,class:"ml-12px w-115px","ignore-path-change":"","show-label":!1,path:`${m.value}[${l}].type`},{default:o(()=>[a[7]||(a[7]=b("div",{class:"h-34px lh-34px"},"(",-1)),u(f,{value:e.value[l].type,"onUpdate:value":[t=>e.value[l].type=t,t=>U(l)],class:"mx-3px",options:w,placeholder:"字段类型",onKeydown:a[4]||(a[4]=p(s(()=>{},["prevent"]),["enter"]))},null,8,["value","onUpdate:value"]),a[8]||(a[8]=b("div",{class:"h-34px lh-34px"},")",-1))]),_:2},1032,["path"])]),_:2},1024)]),_:1},8,["value"])}}});export{R as _};
