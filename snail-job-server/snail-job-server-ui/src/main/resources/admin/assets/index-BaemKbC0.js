import{d as k,k as _,c2 as h,r as u,i as y,q as S,cB as C,c as $,o as x,h as B,cC as J,$ as r}from"./index-DOt4wG7_.js";import{u as O,_ as I}from"./workflow.vue_vue_type_style_index_0_lang-CsofyIkA.js";import{h as R,i as T}from"./workflow-CN6tzeNt.js";import"./job-task-list-table.vue_vue_type_script_setup_true_lang-Dpp6NAh9.js";import"./table-BeU6nStB.js";import"./job-Dl7uKq5c.js";import"./Grid-B1tTyPaW.js";import"./detail-drawer-CmYZhcLa.js";import"./DescriptionsItem-C9YMzXr_.js";import"./log-drawer-E5RMkPe-.js";import"./CollapseItem-B_nLIGZ5.js";import"./dynamic-input.vue_vue_type_script_setup_true_lang-AXt44zW-.js";import"./DynamicInput-DhrW2Ba2.js";import"./FormItemGridItem-DE5kWDnB.js";import"./code-mirror-DjqUikfg.js";import"./cron-input.vue_vue_type_style_index_0_lang-DB3KHq3E.js";import"./notify-C7xEafHT.js";import"./group-B-7eCC7I.js";const X=k({name:"workflow_form_edit",__name:"index",setup(V){const i=O(),f=_(),m=h(),c=u(!1),p=String(f.query.id);let l={};const t=u({}),d=async()=>{c.value=!0;const{data:o,error:e}=await R(p);e||(t.value=o,l=J(o)),c.value=!1},a=u(!1);y(()=>{i.clear(),i.setType(0),i.setId(p),d()}),S(()=>t.value,o=>{a.value=v(o,l)},{deep:!0});const w=async()=>{var e;const{error:o}=await T(t.value);o||((e=window.$message)==null||e.info(r("common.updateSuccess")),Object.assign(l,t.value),a.value=!1,m.push({path:"/workflow/task"}))};function v(o,e){const n=JSON.stringify(o),s=JSON.stringify({...o,...e});return n!==s}const g=()=>{m.push("/workflow/task")};return C((o,e,n)=>{var s;a.value?(s=window.$dialog)==null||s.warning({title:r("common.warning"),content:"有未保存的数据，是否确认离开？",positiveText:r("common.confirm"),negativeText:r("common.cancel"),onPositiveClick:()=>{n()}}):n()}),(o,e)=>(x(),$(B(I),{modelValue:t.value,"onUpdate:modelValue":e[0]||(e[0]=n=>t.value=n),spinning:c.value,updated:a.value,onSave:w,onCancel:g},null,8,["modelValue","spinning","updated"]))}});export{X as default};
