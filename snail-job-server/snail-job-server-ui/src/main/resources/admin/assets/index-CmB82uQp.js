import{_ as le,N as A}from"./table-header-operation.vue_vue_type_script_setup_true_lang-CwAtVujA.js";import{d as z,a0 as q,r as T,a1 as K,q as ne,c as E,o as P,w as s,f as e,h as a,$ as t,ad as se,ae as re,av as ue,b as H,a3 as V,at as ie,au as ce,g as S,t as D,Z as U,aa as I,aw as Q,ax as Z,ay as pe,l as me,L as de,af as F,az as fe,Q as be,ag as C,G as ge,B as L,a7 as he,_ as _e,aA as ve,aB as G,ah as O,ai as ye}from"./index-DOt4wG7_.js";import{_ as Be,f as W,a as ke,b as we,c as je,d as Se}from"./job-task-list-table.vue_vue_type_script_setup_true_lang-Dpp6NAh9.js";import{u as De,a as Ne}from"./table-BeU6nStB.js";import{a as xe}from"./search-form-DjBMObUw.js";import{_ as Re}from"./select-group.vue_vue_type_script_setup_true_lang-B2k8O5Ow.js";import{_ as Te}from"./datetime-range.vue_vue_type_script_setup_true_lang-BJ-Gr1AY.js";import{f as $e}from"./job-Dl7uKq5c.js";import{_ as Je}from"./AutoComplete-CXqFIFil.js";import{_ as Ce}from"./FormItemGridItem-DE5kWDnB.js";import{_ as Le}from"./log-drawer-E5RMkPe-.js";import{_ as Ue}from"./detail-drawer-CmYZhcLa.js";import{_ as Ie,a as Pe}from"./DescriptionsItem-C9YMzXr_.js";import{b as Me}from"./Grid-B1tTyPaW.js";import"./CollapseItem-B_nLIGZ5.js";import"./group-B-7eCC7I.js";const Ae=z({name:"JobBatchSearch",__name:"job-batch-search",props:{model:{required:!0},modelModifiers:{}},emits:q(["reset","search"],["update:model"]),setup(y,{emit:$}){const B=T(!1),_=$,k=T([]),i=K(y,"model"),v=T(i.value.jobName);function r(){v.value="",_("reset")}function b(){_("search")}async function f(){const u=await $e({keywords:v.value,groupName:i.value.groupName});k.value=u.data}function w(u){i.value.jobId=u}ne(()=>v.value,u=>{u.length!==0?(f(),i.value.jobName=u):(B.value=!1,i.value.jobId=null,i.value.jobName=null)});function J(u){return u.map(c=>({value:c.id,label:c.jobName}))}function N(u){return[u.label,`(${u.value})`]}return(u,c)=>{const j=Ce,x=Je,n=se,p=xe;return P(),E(p,{"btn-span":"12 s:24 m:10 l:12 xl:16",model:i.value,onSearch:b,onReset:r},{default:s(()=>[e(j,{span:"24 s:12 m:8",label:a(t)("page.jobBatch.groupName"),path:"groupName",class:"pr-24px"},{default:s(()=>[e(Re,{value:i.value.groupName,"onUpdate:value":c[0]||(c[0]=g=>i.value.groupName=g),clearable:""},null,8,["value"])]),_:1},8,["label"]),e(j,{span:"24 s:12 m:8",label:a(t)("page.jobBatch.jobName"),path:"jobName",class:"pr-24px"},{default:s(()=>[e(x,{value:v.value,"onUpdate:value":c[1]||(c[1]=g=>v.value=g),placeholder:a(t)("page.jobBatch.form.jobName"),options:J(k.value),"empty-visible":B.value,clearable:"",filterable:"","render-label":N,onSelect:w},null,8,["value","placeholder","options","empty-visible"])]),_:1},8,["label"]),e(j,{span:"24 s:12 m:8",label:a(t)("page.jobBatch.taskBatchStatus"),path:"taskBatchStatus",class:"pr-24px"},{default:s(()=>[e(n,{value:i.value.taskBatchStatus,"onUpdate:value":c[2]||(c[2]=g=>i.value.taskBatchStatus=g),multiple:"","max-tag-count":"responsive",placeholder:a(t)("common.taskBatchStatus.form"),options:a(re)(a(ue)).filter(g=>![98,99].includes(g.value)),clearable:""},null,8,["value","placeholder","options"])]),_:1},8,["label"]),e(j,{span:"24 s:24 m:14 l:12 xl:8",label:a(t)("page.common.createTime"),path:"datetimeRange",class:"pr-24px"},{default:s(()=>[e(Te,{value:i.value.datetimeRange,"onUpdate:value":c[3]||(c[3]=g=>i.value.datetimeRange=g)},null,8,["value"])]),_:1},8,["label"])]),_:1},8,["model"])}}}),Oe=z({name:"JobBatchDetailDrawer",__name:"job-batch-detail-drawer",props:q({rowData:{default:null},log:{type:Boolean,default:!1}},{visible:{type:Boolean,default:!1},visibleModifiers:{}}),emits:["update:visible"],setup(y){const $=y,B=K(y,"visible"),_=T(),k=T(!1);async function i(r){k.value=!0,_.value=r}async function v(){var b;const{error:r}=await W($.rowData.id);r||(b=window.$message)==null||b.success(t("common.operateSuccess"))}return(r,b)=>{const f=Pe,w=U,J=Ie,N=ce,u=Be,c=ie,j=Ue,x=Le;return P(),H(V,null,[e(j,{modelValue:B.value,"onUpdate:modelValue":b[0]||(b[0]=n=>B.value=n),title:a(t)("page.jobBatch.detail"),width:["50%","90%"]},{default:s(()=>[e(c,{type:"segment",animated:"","default-value":r.log?1:0},{default:s(()=>[e(N,{name:0,tab:a(t)("page.log.info")},{default:s(()=>[e(J,{class:"pt-16px","label-placement":"top",bordered:"",column:2},{default:s(()=>[e(f,{label:a(t)("page.jobBatch.groupName")},{default:s(()=>{var n;return[S(D((n=r.rowData)==null?void 0:n.groupName),1)]}),_:1},8,["label"]),e(f,{label:a(t)("page.jobBatch.jobName")},{default:s(()=>{var n;return[S(D((n=r.rowData)==null?void 0:n.jobName),1)]}),_:1},8,["label"]),e(f,{label:a(t)("page.jobBatch.taskBatchStatus")},{default:s(()=>{var n;return[e(w,{type:a(I)((n=r.rowData)==null?void 0:n.taskBatchStatus)},{default:s(()=>{var p;return[S(D(a(t)(a(Q)[(p=r.rowData)==null?void 0:p.taskBatchStatus])),1)]}),_:1},8,["type"])]}),_:1},8,["label"]),e(f,{label:a(t)("page.jobBatch.executionAt")},{default:s(()=>{var n;return[S(D((n=r.rowData)==null?void 0:n.executionAt),1)]}),_:1},8,["label"]),e(f,{label:a(t)("page.jobBatch.operationReason")},{default:s(()=>{var n;return[e(w,{type:a(I)((n=r.rowData)==null?void 0:n.operationReason)},{default:s(()=>{var p;return[S(D(a(t)(a(Z)[(p=r.rowData)==null?void 0:p.operationReason])),1)]}),_:1},8,["type"])]}),_:1},8,["label"]),e(f,{label:a(t)("page.jobBatch.executorType")},{default:s(()=>{var n;return[e(w,{type:a(I)((n=r.rowData)==null?void 0:n.executorType)},{default:s(()=>{var p;return[S(D(a(t)(a(pe)[(p=r.rowData)==null?void 0:p.executorType])),1)]}),_:1},8,["type"])]}),_:1},8,["label"]),e(f,{label:a(t)("page.jobBatch.executorInfo"),span:2},{default:s(()=>{var n;return[S(D((n=r.rowData)==null?void 0:n.executorInfo),1)]}),_:1},8,["label"]),e(f,{label:a(t)("common.createDt"),span:2},{default:s(()=>{var n;return[S(D((n=r.rowData)==null?void 0:n.createDt),1)]}),_:1},8,["label"])]),_:1})]),_:1},8,["tab"]),e(N,{name:1,tab:a(t)("page.log.title"),"display-directive":"if"},{default:s(()=>[e(u,{"row-data":r.rowData,onShowLog:i,onRetry:v},null,8,["row-data"])]),_:1},8,["tab"])]),_:1},8,["default-value"])]),_:1},8,["modelValue","title"]),e(x,{show:k.value,"onUpdate:show":b[1]||(b[1]=n=>k.value=n),title:a(t)("page.log.title"),"task-data":_.value},null,8,["show","title","task-data"])],64)}}}),Ve=me(Oe,[["__scopeId","data-v-452afae8"]]),ze={class:"min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"};function R(y){return typeof y=="function"||Object.prototype.toString.call(y)==="[object Object]"&&!ye(y)}const nt=z({name:"job_batch",__name:"index",setup(y){const $=de(),B=T(),{bool:_,setTrue:k}=F(!1),{bool:i,setBool:v}=F(!1),r=history.state.jobName,b=history.state.jobId,f=history.state.taskBatchStatus?[history.state.taskBatchStatus]:[],{columnChecks:w,columns:J,data:N,getData:u,loading:c,mobilePagination:j,searchParams:x,resetSearchParams:n}=De({apiFn:we,apiParams:{page:1,size:10,groupName:null,jobName:null,taskBatchStatus:null,jobId:null,datetimeRange:fe()},searchParams:{jobId:b,jobName:r,taskBatchStatus:f},columns:()=>[{type:"selection",width:30},{key:"id",align:"center",width:60,title:()=>e("div",{class:"flex-center"},[e("span",null,[t("page.jobBatch.jobTask.id")]),e(he,{trigger:"hover"},{trigger:()=>e("span",{class:"mb-2px ml-5px text-16px"},[e(_e,{icon:"ant-design:info-circle-outlined"},null)]),default:()=>e("span",null,[t("common.idDetailTip")])})]),render:o=>{function l(){B.value=o,v(!1),k()}return e(L,{text:!0,tag:"a",type:"primary",onClick:l,class:"ws-normal"},{default:()=>[o.id]})}},{key:"groupName",title:t("page.jobBatch.groupName"),align:"left",width:120},{key:"taskType",title:t("page.jobBatch.taskType"),align:"center",width:120,render:o=>{if(o.taskType===null)return null;const l={1:"info",2:"success",3:"error",4:"primary",5:"warning"},m=t(ve[o.taskType]);return e(U,{type:l[o.taskType]},R(m)?m:{default:()=>[m]})}},{key:"jobName",title:t("page.jobBatch.jobName"),align:"center",width:120},{key:"executionAt",title:t("page.jobBatch.executionAt"),align:"center",width:120},{key:"duration",title:t("page.jobBatch.duration"),align:"center",width:120,render:o=>o.taskBatchStatus===3?Math.round(G(o.updateDt).diff(G(o.executionAt))/1e3):null},{key:"taskBatchStatus",title:t("page.jobBatch.taskBatchStatus"),align:"center",width:120,render:o=>{if(o.taskBatchStatus===null)return null;const l=t(Q[o.taskBatchStatus]);return e(U,{type:{1:"info",2:"info",3:"info",4:"error",5:"error",6:"error"}[o.taskBatchStatus]},R(l)?l:{default:()=>[l]})}},{key:"operationReason",title:t("page.jobBatch.operationReason"),align:"center",width:120,render:o=>{if(o.operationReason===null)return null;const l=t(Z[o.operationReason]);return e(U,{type:I(o.operationReason)},R(l)?l:{default:()=>[l]})}},{key:"createDt",title:t("common.createDt"),align:"center",width:120},{key:"operate",title:t("common.operate"),align:"center",width:160,render:o=>{let l;const m=()=>o.taskBatchStatus===1||o.taskBatchStatus===2?e(V,null,[e(O,{vertical:!0},null),e(A,{onPositiveClick:()=>oe(o.id)},{default:()=>t("common.confirmStop"),trigger:()=>{let d;return e(L,{type:"error",text:!0,ghost:!0,size:"small"},R(d=t("common.stop"))?d:{default:()=>[d]})}})]):null,M=()=>o.taskBatchStatus===4||o.taskBatchStatus===5||o.taskBatchStatus===6?e(V,null,[e(O,{vertical:!0},null),e(A,{onPositiveClick:()=>ae(o.id)},{default:()=>t("common.confirmRetry"),trigger:()=>{let d;return e(L,{type:"error",text:!0,ghost:!0,size:"small"},R(d=t("common.retry"))?d:{default:()=>[d]})}})]):null;return e("div",{class:"flex-center gap-8px"},[e(L,{type:"primary",text:!0,ghost:!0,size:"small",onClick:()=>te(o)},R(l=t("common.log"))?l:{default:()=>[l]}),m(),M(),e(O,{vertical:!0},null),e(A,{onPositiveClick:()=>Y(o.id)},{default:()=>t("common.confirmDelete"),trigger:()=>{let d;return e(L,{type:"error",text:!0,ghost:!0,size:"small"},R(d=t("common.delete"))?d:{default:()=>[d]})}})])}}]}),{checkedRowKeys:p,onDeleted:g,onBatchDeleted:X}=Ne(N,u);async function Y(o){const{error:l}=await je(o);l||g()}async function ee(){const{error:o}=await ke(p.value);o||X()}function te(o){B.value=o,v(!0),k()}async function ae(o){var m;const{error:l}=await W(o);l||((m=window.$message)==null||m.success(t("common.operateSuccess")),u())}async function oe(o){var m;const{error:l}=await Se(o);l||((m=window.$message)==null||m.success(t("common.operateSuccess")),u())}return(o,l)=>{const m=le,M=Me,d=ge;return P(),H("div",ze,[e(Ae,{model:a(x),"onUpdate:model":l[0]||(l[0]=h=>C(x)?x.value=h:null),onReset:a(n),onSearch:a(u)},null,8,["model","onReset","onSearch"]),e(d,{title:a(t)("page.jobBatch.title"),bordered:!1,size:"small",class:"sm:flex-1-hidden card-wrapper","header-class":"view-card-header"},{"header-extra":s(()=>[e(m,{columns:a(w),"onUpdate:columns":l[1]||(l[1]=h=>C(w)?w.value=h:null),"disabled-delete":a(p).length===0,loading:a(c),"show-add":!1,onDelete:ee,onRefresh:a(u)},null,8,["columns","disabled-delete","loading","onRefresh"])]),default:s(()=>[e(M,{"checked-row-keys":a(p),"onUpdate:checkedRowKeys":l[2]||(l[2]=h=>C(p)?p.value=h:null),columns:a(J),data:a(N),"flex-height":!a($).isMobile,"scroll-x":1200,loading:a(c),remote:"","row-key":h=>h.id,pagination:a(j),class:"sm:h-full"},null,8,["checked-row-keys","columns","data","flex-height","loading","row-key","pagination"])]),_:1},8,["title"]),a(_)?(P(),E(Ve,{key:0,visible:a(_),"onUpdate:visible":l[3]||(l[3]=h=>C(_)?_.value=h:null),log:a(i),"onUpdate:log":l[4]||(l[4]=h=>C(i)?i.value=h:null),"row-data":B.value},null,8,["visible","log","row-data"])):be("",!0)])}}});export{nt as default};
