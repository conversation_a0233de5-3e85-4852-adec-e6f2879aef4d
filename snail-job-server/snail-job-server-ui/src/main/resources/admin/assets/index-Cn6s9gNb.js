import{d as i,c2 as p,i as c,r as l,c as u,o as f,h as d,$ as w}from"./index-DOt4wG7_.js";import{u as k,_}from"./workflow.vue_vue_type_style_index_0_lang-CsofyIkA.js";import{e as v}from"./workflow-CN6tzeNt.js";import"./job-task-list-table.vue_vue_type_script_setup_true_lang-Dpp6NAh9.js";import"./table-BeU6nStB.js";import"./job-Dl7uKq5c.js";import"./Grid-B1tTyPaW.js";import"./detail-drawer-CmYZhcLa.js";import"./DescriptionsItem-C9YMzXr_.js";import"./log-drawer-E5RMkPe-.js";import"./CollapseItem-B_nLIGZ5.js";import"./dynamic-input.vue_vue_type_script_setup_true_lang-AXt44zW-.js";import"./DynamicInput-DhrW2Ba2.js";import"./FormItemGridItem-DE5kWDnB.js";import"./code-mirror-DjqUikfg.js";import"./cron-input.vue_vue_type_style_index_0_lang-DB3KHq3E.js";import"./notify-C7xEafHT.js";import"./group-B-7eCC7I.js";const j=i({name:"workflow_form_add",__name:"index",setup(x){const t=k(),r=p();c(()=>{t.clear(),t.setType(0)});const e=l({workflowName:`WF-${new Date().getTime()}`,workflowStatus:1,blockStrategy:1,description:void 0,executorTimeout:60,wfContext:'{"init":""}'}),s=async()=>{var o;const{error:a}=await v(e.value);a||((o=window.$message)==null||o.info(w("common.addSuccess")),r.push("/workflow/task"))},m=()=>{r.push("/workflow/task")};return(a,o)=>(f(),u(d(_),{modelValue:e.value,"onUpdate:modelValue":o[0]||(o[0]=n=>e.value=n),onSave:s,onCancel:m},null,8,["modelValue"]))}});export{j as default};
