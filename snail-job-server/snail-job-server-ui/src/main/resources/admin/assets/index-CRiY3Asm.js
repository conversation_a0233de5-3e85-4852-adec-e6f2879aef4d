import{_ as te,N as j}from"./table-header-operation.vue_vue_type_script_setup_true_lang-CwAtVujA.js";import{_ as oe,a as ae,d as ne}from"./download-PYGvkbZH.js";import{_ as le}from"./delete-alert-Cep_Tkqr.js";import{d as F,a0 as A,a1 as L,c as V,o as O,w as r,f as e,h as t,$ as a,D as re,ad as se,ae as ie,aH as ue,n as ce,q as pe,I as me,C as fe,E as de,B as b,g as W,t as z,aJ as we,d5 as ge,d7 as _e,l as ke,c2 as he,L as ye,u as ve,r as xe,af as be,b as Ne,ag as U,G as Se,aM as Ce,aa as De,Z as Te,ah as q,aO as We,ai as Re}from"./index-DOt4wG7_.js";import{n as $e,o as E,p as Ue,q as Be}from"./workflow-CN6tzeNt.js";import{u as Ie,a as Me}from"./table-BeU6nStB.js";import{_ as Pe}from"./status-switch.vue_vue_type_script_setup_true_lang-DCMb3kln.js";import{u as Ae}from"./auth-uZCUSXTn.js";import{a as ze}from"./search-form-DjBMObUw.js";import{_ as Fe}from"./select-group.vue_vue_type_script_setup_true_lang-B2k8O5Ow.js";import{_ as Oe}from"./FormItemGridItem-DE5kWDnB.js";import{_ as je}from"./dynamic-input.vue_vue_type_script_setup_true_lang-AXt44zW-.js";import{b as qe}from"./Grid-B1tTyPaW.js";import"./Progress-DxKiN1qb.js";import"./CollapseItem-B_nLIGZ5.js";import"./group-B-7eCC7I.js";import"./DynamicInput-DhrW2Ba2.js";const Ee=F({name:"WorkflowSearch",__name:"workflow-search",props:{model:{required:!0},modelModifiers:{}},emits:A(["reset","search"],["update:model"]),setup(g,{emit:N}){const c=N,p=L(g,"model");function _(){c("reset")}function u(){c("search")}return(v,d)=>{const h=Oe,x=re,l=se,s=ze;return O(),V(s,{model:p.value,onSearch:u,onReset:_},{default:r(()=>[e(h,{span:"24 s:12 m:6",label:t(a)("page.workflow.groupName"),path:"groupName",class:"pr-24px"},{default:r(()=>[e(Fe,{value:p.value.groupName,"onUpdate:value":d[0]||(d[0]=i=>p.value.groupName=i),clearable:""},null,8,["value"])]),_:1},8,["label"]),e(h,{span:"24 s:12 m:6",label:t(a)("page.workflow.workflowName"),path:"workflowName",class:"pr-24px","label-width":100},{default:r(()=>[e(x,{value:p.value.workflowName,"onUpdate:value":d[1]||(d[1]=i=>p.value.workflowName=i),placeholder:t(a)("page.workflow.form.workflowName"),clearable:""},null,8,["value","placeholder"])]),_:1},8,["label"]),e(h,{span:"24 s:12 m:6",label:t(a)("page.workflow.workflowStatus"),path:"workflowStatus",class:"pr-24px"},{default:r(()=>[e(l,{value:p.value.workflowStatus,"onUpdate:value":d[2]||(d[2]=i=>p.value.workflowStatus=i),placeholder:t(a)("page.workflow.form.workflowStatus"),options:t(ie)(t(ue)),clearable:""},null,8,["value","placeholder","options"])]),_:1},8,["label"])]),_:1},8,["model"])}}}),Le=F({name:"WorkflowTriggerModal",__name:"workflow-trigger-modal",props:A({rowData:{}},{visible:{type:Boolean,default:!1},visibleModifiers:{}}),emits:A(["submitted"],["update:visible"]),setup(g,{emit:N}){const c=g,p=N,_=L(g,"visible"),u=ce(v());function v(){var l;return{workflowId:(l=c.rowData)==null?void 0:l.id,tmpWfContext:"",wfContexts:[]}}function d(){const l=c.rowData;if(!l){Object.assign(u,v());return}(l==null?void 0:l.wfContext)&&(u.wfContexts=ge(l==null?void 0:l.wfContext)||[])}function h(){_.value=!1}pe(_,()=>{_.value&&d()});async function x(){var i,S;const l=JSON.stringify(_e(u.wfContexts)||{}),{error:s}=await $e({workflowId:(i=c.rowData)==null?void 0:i.id,tmpWfContext:l});s||((S=window.$message)==null||S.success(a("common.executeSuccess")),h(),p("submitted"))}return(l,s)=>{const i=je,S=fe,y=me,R=b,m=de,I=we;return O(),V(I,{show:_.value,"onUpdate:show":s[1]||(s[1]=C=>_.value=C),class:"max-w-90% w-600px",preset:"card",title:"执行工作流",bordered:!1},{footer:r(()=>[e(m,{justify:"end",size:16},{default:r(()=>[e(R,{onClick:h},{default:r(()=>[W(z(t(a)("common.cancel")),1)]),_:1}),e(R,{type:"primary",onClick:x},{default:r(()=>s[2]||(s[2]=[W("执行")])),_:1})]),_:1})]),default:r(()=>[e(y,{model:u},{default:r(()=>{var C;return[e(S,{path:"wfContexts",label:"工作流上下文","show-feedback":!((C=u.wfContexts)!=null&&C.length)},{default:r(()=>[e(i,{value:u.wfContexts,"onUpdate:value":s[0]||(s[0]=M=>u.wfContexts=M),path:"wfContexts"},null,8,["value"])]),_:1},8,["show-feedback"])]}),_:1},8,["model"])]),_:1},8,["show"])}}}),Ve=ke(Le,[["__scopeId","data-v-6d715661"]]),Ge={class:"min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"};function B(g){return typeof g=="function"||Object.prototype.toString.call(g)==="[object Object]"&&!Re(g)}const ct=F({name:"workflow_task",__name:"index",setup(g){const{hasAuth:N}=Ae(),c=he(),p=ye(),{routerPushByKey:_}=ve(),u=xe(),{bool:v,setTrue:d}=be(!1),{columns:h,columnChecks:x,data:l,getData:s,loading:i,mobilePagination:S,searchParams:y,resetSearchParams:R}=Ie({apiFn:Ue,apiParams:{page:1,size:10,workflowName:null,groupName:null,workflowStatus:null},columns:()=>[{type:"selection",align:"center",width:48},{key:"id",title:a("common.index"),align:"center",width:120},{key:"workflowName",title:a("page.workflow.workflowName"),align:"left",minWidth:120,render:o=>{function n(){J(o.id)}return e(b,{text:!0,tag:"a",type:"primary",onClick:n,class:"ws-normal"},{default:()=>[o.workflowName]})}},{key:"groupName",title:a("page.workflow.groupName"),align:"left",minWidth:120},{key:"nextTriggerAt",title:a("page.workflow.nextTriggerAt"),align:"left",minWidth:120},{key:"workflowStatus",title:a("page.workflow.workflowStatus"),align:"left",minWidth:120,render:o=>{const n=async(f,D)=>{var w;const{error:T}=await Be(o.id);T||(o.workflowStatus=f,(w=window.$message)==null||w.success(a("common.updateSuccess"))),D(!T)};return e(Pe,{value:o.workflowStatus,"onUpdate:value":f=>o.workflowStatus=f,onSubmitted:n},null)}},{key:"triggerType",title:a("page.workflow.triggerType"),align:"left",minWidth:120,render:o=>{if(!o.triggerType)return null;const n=a(Ce[o.triggerType]);return e(Te,{type:De(o.triggerType)},B(n)?n:{default:()=>[n]})}},{key:"triggerInterval",title:a("page.workflow.triggerInterval"),align:"left",minWidth:120},{key:"executorTimeout",title:a("page.workflow.executorTimeout"),align:"left",minWidth:120},{key:"updateDt",title:a("page.workflow.updateDt"),align:"left",minWidth:120},{key:"operate",title:a("common.operate"),align:"center",fixed:"right",width:200,render:o=>{let n,f;const D=[{label:a("common.copy"),key:"copy",click:()=>Z(o.id)},{type:"divider",key:"d2"},{label:a("common.batchList"),key:"batchList",click:()=>ee(o.id)},{type:"divider",key:"d2"},{type:"render",key:"delete",render:()=>e("div",{class:"flex-center"},[e(j,{onPositiveClick:()=>G(o.id)},{default:()=>a("common.confirmDelete"),trigger:()=>{let w;return e(b,{quaternary:!0,size:"small"},B(w=a("common.delete"))?w:{default:()=>[w]})}})])}],T=w=>{const $=D.filter(P=>P.key===w)[0].click;$&&$()};return e("div",{class:"flex-center gap-8px"},[e(b,{text:!0,type:"warning",ghost:!0,size:"small",onClick:()=>K(o.id)},B(n=a("common.edit"))?n:{default:()=>[n]}),e(q,{vertical:!0},null),e(b,{type:"error",text:!0,ghost:!0,size:"small",onClick:()=>Q(o)},B(f=a("common.execute"))?f:{default:()=>[f]}),e(q,{vertical:!0},null),e(We,{trigger:"click","show-arrow":!1,options:D,size:"small","on-select":T},{default:()=>e(b,{text:!0,type:"primary",ghost:!0,size:"small"},{default:()=>[W("更多")]})})])}}]}),{checkedRowKeys:m,onBatchDeleted:I,onDeleted:C}=Me(l,s);async function M(){const{error:o}=await E(m.value);o||I()}async function G(o){const{error:n}=await E([o]);n||C()}function K(o){c.push({path:"/workflow/form/edit",query:{id:o}})}function H(){c.push({path:"/workflow/form/add"})}function J(o){c.push({path:"/workflow/form/detail",query:{id:o}})}function Z(o){c.push({path:"/workflow/form/copy",query:{id:o}})}async function Q(o){u.value=o,d()}function X(){return{workflowIds:m.value,groupName:y.groupName,workflowName:y.workflowName,workflowStatus:y.workflowStatus}}function Y(){ne("/workflow/export",X(),a("page.workflow.title"))}function ee(o){const n=l.value.find(f=>f.id===o);_("workflow_batch",{state:{workflowId:o,workflowName:n.workflowName}})}return(o,n)=>{const f=le,D=oe,T=ae,w=te,$=qe,P=Se;return O(),Ne("div",Ge,[e(Ee,{model:t(y),"onUpdate:model":n[0]||(n[0]=k=>U(y)?y.value=k:null),onReset:t(R),onSearch:t(s)},null,8,["model","onReset","onSearch"]),e(f),e(P,{title:t(a)("page.workflow.title"),bordered:!1,size:"small",class:"sm:flex-1-hidden card-wrapper","header-class":"view-card-header"},{"header-extra":r(()=>[e(w,{columns:t(x),"onUpdate:columns":n[1]||(n[1]=k=>U(x)?x.value=k:null),"disabled-delete":t(m).length===0,loading:t(i),"show-delete":t(N)("R_ADMIN"),onAdd:H,onDelete:M,onRefresh:t(s)},{addAfter:r(()=>[e(D,{action:"/workflow/import",accept:"application/json",onRefresh:t(s)},null,8,["onRefresh"]),e(t(j),{onPositiveClick:Y},{trigger:r(()=>[e(t(b),{size:"small",ghost:"",type:"primary",disabled:t(m).length===0&&t(N)("R_USER")},{icon:r(()=>[e(T,{class:"text-icon"})]),default:r(()=>[W(" "+z(t(a)("common.export")),1)]),_:1},8,["disabled"])]),default:r(()=>[W(z(t(m).length===0?t(a)("common.exportAll"):t(a)("common.exportPar",{num:t(m).length})),1)]),_:1})]),_:1},8,["columns","disabled-delete","loading","show-delete","onRefresh"])]),default:r(()=>[e($,{"checked-row-keys":t(m),"onUpdate:checkedRowKeys":n[2]||(n[2]=k=>U(m)?m.value=k:null),columns:t(h),data:t(l),"flex-height":!t(p).isMobile,"scroll-x":1300,loading:t(i),remote:"","row-key":k=>k.id,pagination:t(S),class:"sm:h-full"},null,8,["checked-row-keys","columns","data","flex-height","loading","row-key","pagination"])]),_:1},8,["title"]),e(Ve,{visible:t(v),"onUpdate:visible":n[3]||(n[3]=k=>U(v)?v.value=k:null),"row-data":u.value},null,8,["visible","row-data"])])}}});export{ct as default};
