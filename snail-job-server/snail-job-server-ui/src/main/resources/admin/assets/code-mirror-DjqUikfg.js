import{aS as gu,b as La,o as _a,e as bu,d as Na,r as Qi,de as yu,a as Ct,q as nr,i as xu,an as Su,p as wu,aj as Qu,M as ku,af as Pu,f as pi,h as Vt,B as $u,w as Eo,aJ as vu,ag as Zu,l as Cu}from"./index-DOt4wG7_.js";const Tu={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"};function Ru(n,e){return _a(),La("svg",Tu,e[0]||(e[0]=[bu("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 4H4m0 0v4m0-4l5 5m7-5h4m0 0v4m0-4l-5 5M8 20H4m0 0v-4m0 4l5-5m7 5h4m0 0v-4m0 4l-5-5"},null,-1)]))}const Au=gu({name:"flowbite-expand-outline",render:Ru});class B{lineAt(e){if(e<0||e>this.length)throw new RangeError(`Invalid position ${e} in document of length ${this.length}`);return this.lineInner(e,!1,1,0)}line(e){if(e<1||e>this.lines)throw new RangeError(`Invalid line number ${e} in ${this.lines}-line document`);return this.lineInner(e,!0,1,0)}replace(e,t,i){[e,t]=ii(this,e,t);let s=[];return this.decompose(0,e,s,2),i.length&&i.decompose(0,i.length,s,3),this.decompose(t,this.length,s,1),Fe.from(s,this.length-(t-e)+i.length)}append(e){return this.replace(this.length,this.length,e)}slice(e,t=this.length){[e,t]=ii(this,e,t);let i=[];return this.decompose(e,t,i,0),Fe.from(i,t-e)}eq(e){if(e==this)return!0;if(e.length!=this.length||e.lines!=this.lines)return!1;let t=this.scanIdentical(e,1),i=this.length-this.scanIdentical(e,-1),s=new Ti(this),r=new Ti(e);for(let o=t,l=t;;){if(s.next(o),r.next(o),o=0,s.lineBreak!=r.lineBreak||s.done!=r.done||s.value!=r.value)return!1;if(l+=s.value.length,s.done||l>=i)return!0}}iter(e=1){return new Ti(this,e)}iterRange(e,t=this.length){return new Ia(this,e,t)}iterLines(e,t){let i;if(e==null)i=this.iter();else{t==null&&(t=this.lines+1);let s=this.line(e).from;i=this.iterRange(s,Math.max(s,t==this.lines+1?this.length:t<=1?0:this.line(t-1).to))}return new Ga(i)}toString(){return this.sliceString(0)}toJSON(){let e=[];return this.flatten(e),e}constructor(){}static of(e){if(e.length==0)throw new RangeError("A document must have at least one line");return e.length==1&&!e[0]?B.empty:e.length<=32?new H(e):Fe.from(H.split(e,[]))}}class H extends B{constructor(e,t=Mu(e)){super(),this.text=e,this.length=t}get lines(){return this.text.length}get children(){return null}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.text[r],l=s+o.length;if((t?i:l)>=e)return new Wu(s,l,i,o);s=l+1,i++}}decompose(e,t,i,s){let r=e<=0&&t>=this.length?this:new H(Vo(this.text,e,t),Math.min(t,this.length)-Math.max(0,e));if(s&1){let o=i.pop(),l=Rn(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new H(l,o.length+r.length));else{let a=l.length>>1;i.push(new H(l.slice(0,a)),new H(l.slice(a)))}}else i.push(r)}replace(e,t,i){if(!(i instanceof H))return super.replace(e,t,i);[e,t]=ii(this,e,t);let s=Rn(this.text,Rn(i.text,Vo(this.text,0,e)),t),r=this.length+i.length-(t-e);return s.length<=32?new H(s,r):Fe.from(H.split(s,[]),r)}sliceString(e,t=this.length,i=`
`){[e,t]=ii(this,e,t);let s="";for(let r=0,o=0;r<=t&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>e&&o&&(s+=i),e<a&&t>r&&(s+=l.slice(Math.max(0,e-r),t-r)),r=a+1}return s}flatten(e){for(let t of this.text)e.push(t)}scanIdentical(){return 0}static split(e,t){let i=[],s=-1;for(let r of e)i.push(r),s+=r.length+1,i.length==32&&(t.push(new H(i,s)),i=[],s=-1);return s>-1&&t.push(new H(i,s)),t}}class Fe extends B{constructor(e,t){super(),this.children=e,this.length=t,this.lines=0;for(let i of e)this.lines+=i.lines}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.children[r],l=s+o.length,a=i+o.lines-1;if((t?a:l)>=e)return o.lineInner(e,t,i,s);s=l+1,i=a+1}}decompose(e,t,i,s){for(let r=0,o=0;o<=t&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(e<=a&&t>=o){let h=s&((o<=e?1:0)|(a>=t?2:0));o>=e&&a<=t&&!h?i.push(l):l.decompose(e-o,t-o,i,h)}o=a+1}}replace(e,t,i){if([e,t]=ii(this,e,t),i.lines<this.lines)for(let s=0,r=0;s<this.children.length;s++){let o=this.children[s],l=r+o.length;if(e>=r&&t<=l){let a=o.replace(e-r,t-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>4&&a.lines>h>>6){let c=this.children.slice();return c[s]=a,new Fe(c,this.length-(t-e)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(e,t,i)}sliceString(e,t=this.length,i=`
`){[e,t]=ii(this,e,t);let s="";for(let r=0,o=0;r<this.children.length&&o<=t;r++){let l=this.children[r],a=o+l.length;o>e&&r&&(s+=i),e<a&&t>o&&(s+=l.sliceString(e-o,t-o,i)),o=a+1}return s}flatten(e){for(let t of this.children)t.flatten(e)}scanIdentical(e,t){if(!(e instanceof Fe))return 0;let i=0,[s,r,o,l]=t>0?[0,0,this.children.length,e.children.length]:[this.children.length-1,e.children.length-1,-1,-1];for(;;s+=t,r+=t){if(s==o||r==l)return i;let a=this.children[s],h=e.children[r];if(a!=h)return i+a.scanIdentical(h,t);i+=a.length+1}}static from(e,t=e.reduce((i,s)=>i+s.length+1,-1)){let i=0;for(let d of e)i+=d.lines;if(i<32){let d=[];for(let O of e)O.flatten(d);return new H(d,t)}let s=Math.max(32,i>>5),r=s<<1,o=s>>1,l=[],a=0,h=-1,c=[];function f(d){let O;if(d.lines>r&&d instanceof Fe)for(let m of d.children)f(m);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof H&&a&&(O=c[c.length-1])instanceof H&&d.lines+O.lines<=32?(a+=d.lines,h+=d.length+1,c[c.length-1]=new H(O.text.concat(d.text),O.length+1+d.length)):(a+d.lines>s&&u(),a+=d.lines,h+=d.length+1,c.push(d))}function u(){a!=0&&(l.push(c.length==1?c[0]:Fe.from(c,h)),h=-1,a=c.length=0)}for(let d of e)f(d);return u(),l.length==1?l[0]:new Fe(l,t)}}B.empty=new H([""],0);function Mu(n){let e=-1;for(let t of n)e+=t.length+1;return e}function Rn(n,e,t=0,i=1e9){for(let s=0,r=0,o=!0;r<n.length&&s<=i;r++){let l=n[r],a=s+l.length;a>=t&&(a>i&&(l=l.slice(0,i-s)),s<t&&(l=l.slice(t-s)),o?(e[e.length-1]+=l,o=!1):e.push(l)),s=a+1}return e}function Vo(n,e,t){return Rn(n,[""],e,t)}class Ti{constructor(e,t=1){this.dir=t,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[e],this.offsets=[t>0?1:(e instanceof H?e.text.length:e.children.length)<<1]}nextInner(e,t){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,s=this.nodes[i],r=this.offsets[i],o=r>>1,l=s instanceof H?s.text.length:s.children.length;if(o==(t>0?l:0)){if(i==0)return this.done=!0,this.value="",this;t>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((r&1)==(t>0?0:1)){if(this.offsets[i]+=t,e==0)return this.lineBreak=!0,this.value=`
`,this;e--}else if(s instanceof H){let a=s.text[o+(t<0?-1:0)];if(this.offsets[i]+=t,a.length>Math.max(0,e))return this.value=e==0?a:t>0?a.slice(e):a.slice(0,a.length-e),this;e-=a.length}else{let a=s.children[o+(t<0?-1:0)];e>a.length?(e-=a.length,this.offsets[i]+=t):(t<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(t>0?1:(a instanceof H?a.text.length:a.children.length)<<1))}}}next(e=0){return e<0&&(this.nextInner(-e,-this.dir),e=this.value.length),this.nextInner(e,this.dir)}}class Ia{constructor(e,t,i){this.value="",this.done=!1,this.cursor=new Ti(e,t>i?-1:1),this.pos=t>i?e.length:0,this.from=Math.min(t,i),this.to=Math.max(t,i)}nextInner(e,t){if(t<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;e+=Math.max(0,t<0?this.pos-this.to:this.from-this.pos);let i=t<0?this.pos-this.from:this.to-this.pos;e>i&&(e=i),i-=e;let{value:s}=this.cursor.next(e);return this.pos+=(s.length+e)*t,this.value=s.length<=i?s:t<0?s.slice(s.length-i):s.slice(0,i),this.done=!this.value,this}next(e=0){return e<0?e=Math.max(e,this.from-this.pos):e>0&&(e=Math.min(e,this.to-this.pos)),this.nextInner(e,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class Ga{constructor(e){this.inner=e,this.afterBreak=!0,this.value="",this.done=!1}next(e=0){let{done:t,lineBreak:i,value:s}=this.inner.next(e);return t&&this.afterBreak?(this.value="",this.afterBreak=!1):t?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=s,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(B.prototype[Symbol.iterator]=function(){return this.iter()},Ti.prototype[Symbol.iterator]=Ia.prototype[Symbol.iterator]=Ga.prototype[Symbol.iterator]=function(){return this});class Wu{constructor(e,t,i,s){this.from=e,this.to=t,this.number=i,this.text=s}get length(){return this.to-this.from}}function ii(n,e,t){return e=Math.max(0,Math.min(n.length,e)),[e,Math.max(e,Math.min(n.length,t))]}let Gt="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(n=>n?parseInt(n,36):1);for(let n=1;n<Gt.length;n++)Gt[n]+=Gt[n-1];function Xu(n){for(let e=1;e<Gt.length;e+=2)if(Gt[e]>n)return Gt[e-1]<=n;return!1}function Bo(n){return n>=127462&&n<=127487}const Uo=8205;function ue(n,e,t=!0,i=!0){return(t?Fa:Yu)(n,e,i)}function Fa(n,e,t){if(e==n.length)return e;e&&Ha(n.charCodeAt(e))&&Ka(n.charCodeAt(e-1))&&e--;let i=ae(n,e);for(e+=We(i);e<n.length;){let s=ae(n,e);if(i==Uo||s==Uo||t&&Xu(s))e+=We(s),i=s;else if(Bo(s)){let r=0,o=e-2;for(;o>=0&&Bo(ae(n,o));)r++,o-=2;if(r%2==0)break;e+=2}else break}return e}function Yu(n,e,t){for(;e>0;){let i=Fa(n,e-2,t);if(i<e)return i;e--}return 0}function Ha(n){return n>=56320&&n<57344}function Ka(n){return n>=55296&&n<56320}function ae(n,e){let t=n.charCodeAt(e);if(!Ka(t)||e+1==n.length)return t;let i=n.charCodeAt(e+1);return Ha(i)?(t-55296<<10)+(i-56320)+65536:t}function io(n){return n<=65535?String.fromCharCode(n):(n-=65536,String.fromCharCode((n>>10)+55296,(n&1023)+56320))}function We(n){return n<65536?1:2}const sr=/\r\n?|\n/;var ce=function(n){return n[n.Simple=0]="Simple",n[n.TrackDel=1]="TrackDel",n[n.TrackBefore=2]="TrackBefore",n[n.TrackAfter=3]="TrackAfter",n}(ce||(ce={}));class it{constructor(e){this.sections=e}get length(){let e=0;for(let t=0;t<this.sections.length;t+=2)e+=this.sections[t];return e}get newLength(){let e=0;for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t+1];e+=i<0?this.sections[t]:i}return e}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(e){for(let t=0,i=0,s=0;t<this.sections.length;){let r=this.sections[t++],o=this.sections[t++];o<0?(e(i,s,r),s+=r):s+=o,i+=r}}iterChangedRanges(e,t=!1){rr(this,e,t)}get invertedDesc(){let e=[];for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];s<0?e.push(i,s):e.push(s,i)}return new it(e)}composeDesc(e){return this.empty?e:e.empty?this:Ja(this,e)}mapDesc(e,t=!1){return e.empty?this:or(this,e,t)}mapPos(e,t=-1,i=ce.Simple){let s=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=s+l;if(a<0){if(h>e)return r+(e-s);r+=l}else{if(i!=ce.Simple&&h>=e&&(i==ce.TrackDel&&s<e&&h>e||i==ce.TrackBefore&&s<e||i==ce.TrackAfter&&h>e))return null;if(h>e||h==e&&t<0&&!l)return e==s||t<0?r:r+a;r+=a}s=h}if(e>s)throw new RangeError(`Position ${e} is out of range for changeset of length ${s}`);return r}touchesRange(e,t=e){for(let i=0,s=0;i<this.sections.length&&s<=t;){let r=this.sections[i++],o=this.sections[i++],l=s+r;if(o>=0&&s<=t&&l>=e)return s<e&&l>t?"cover":!0;s=l}return!1}toString(){let e="";for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];e+=(e?" ":"")+i+(s>=0?":"+s:"")}return e}toJSON(){return this.sections}static fromJSON(e){if(!Array.isArray(e)||e.length%2||e.some(t=>typeof t!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new it(e)}static create(e){return new it(e)}}class ne extends it{constructor(e,t){super(e),this.inserted=t}apply(e){if(this.length!=e.length)throw new RangeError("Applying change set to a document with the wrong length");return rr(this,(t,i,s,r,o)=>e=e.replace(s,s+(i-t),o),!1),e}mapDesc(e,t=!1){return or(this,e,t,!0)}invert(e){let t=this.sections.slice(),i=[];for(let s=0,r=0;s<t.length;s+=2){let o=t[s],l=t[s+1];if(l>=0){t[s]=l,t[s+1]=o;let a=s>>1;for(;i.length<a;)i.push(B.empty);i.push(o?e.slice(r,r+o):B.empty)}r+=o}return new ne(t,i)}compose(e){return this.empty?e:e.empty?this:Ja(this,e,!0)}map(e,t=!1){return e.empty?this:or(this,e,t,!0)}iterChanges(e,t=!1){rr(this,e,t)}get desc(){return it.create(this.sections)}filter(e){let t=[],i=[],s=[],r=new Yi(this);e:for(let o=0,l=0;;){let a=o==e.length?1e9:e[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break e;let c=Math.min(r.len,a-l);de(s,c,-1);let f=r.ins==-1?-1:r.off==0?r.ins:0;de(t,c,f),f>0&&dt(i,t,r.text),r.forward(c),l+=c}let h=e[o++];for(;l<h;){if(r.done)break e;let c=Math.min(r.len,h-l);de(t,c,-1),de(s,c,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(c),l+=c}}return{changes:new ne(t,i),filtered:it.create(s)}}toJSON(){let e=[];for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t],s=this.sections[t+1];s<0?e.push(i):s==0?e.push([i]):e.push([i].concat(this.inserted[t>>1].toJSON()))}return e}static of(e,t,i){let s=[],r=[],o=0,l=null;function a(c=!1){if(!c&&!s.length)return;o<t&&de(s,t-o,-1);let f=new ne(s,r);l=l?l.compose(f.map(l)):f,s=[],r=[],o=0}function h(c){if(Array.isArray(c))for(let f of c)h(f);else if(c instanceof ne){if(c.length!=t)throw new RangeError(`Mismatched change set length (got ${c.length}, expected ${t})`);a(),l=l?l.compose(c.map(l)):c}else{let{from:f,to:u=f,insert:d}=c;if(f>u||f<0||u>t)throw new RangeError(`Invalid change range ${f} to ${u} (in doc of length ${t})`);let O=d?typeof d=="string"?B.of(d.split(i||sr)):d:B.empty,m=O.length;if(f==u&&m==0)return;f<o&&a(),f>o&&de(s,f-o,-1),de(s,u-f,m),dt(r,s,O),o=u}}return h(e),a(!l),l}static empty(e){return new ne(e?[e,-1]:[],[])}static fromJSON(e){if(!Array.isArray(e))throw new RangeError("Invalid JSON representation of ChangeSet");let t=[],i=[];for(let s=0;s<e.length;s++){let r=e[s];if(typeof r=="number")t.push(r,-1);else{if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)t.push(r[0],0);else{for(;i.length<s;)i.push(B.empty);i[s]=B.of(r.slice(1)),t.push(r[0],i[s].length)}}}return new ne(t,i)}static createSet(e,t){return new ne(e,t)}}function de(n,e,t,i=!1){if(e==0&&t<=0)return;let s=n.length-2;s>=0&&t<=0&&t==n[s+1]?n[s]+=e:e==0&&n[s]==0?n[s+1]+=t:i?(n[s]+=e,n[s+1]+=t):n.push(e,t)}function dt(n,e,t){if(t.length==0)return;let i=e.length-2>>1;if(i<n.length)n[n.length-1]=n[n.length-1].append(t);else{for(;n.length<i;)n.push(B.empty);n.push(t)}}function rr(n,e,t){let i=n.inserted;for(let s=0,r=0,o=0;o<n.sections.length;){let l=n.sections[o++],a=n.sections[o++];if(a<0)s+=l,r+=l;else{let h=s,c=r,f=B.empty;for(;h+=l,c+=a,a&&i&&(f=f.append(i[o-2>>1])),!(t||o==n.sections.length||n.sections[o+1]<0);)l=n.sections[o++],a=n.sections[o++];e(s,h,r,c,f),s=h,r=c}}}function or(n,e,t,i=!1){let s=[],r=i?[]:null,o=new Yi(n),l=new Yi(e);for(let a=-1;;)if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);de(s,h,-1),o.forward(h),l.forward(h)}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!t))){let h=l.len;for(de(s,l.ins,-1);h;){let c=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=c&&(de(s,0,o.ins),r&&dt(r,s,o.text),a=o.i),o.forward(c),h-=c}l.next()}else if(o.ins>=0){let h=0,c=o.len;for(;c;)if(l.ins==-1){let f=Math.min(c,l.len);h+=f,c-=f,l.forward(f)}else if(l.ins==0&&l.len<c)c-=l.len,l.next();else break;de(s,h,a<o.i?o.ins:0),r&&a<o.i&&dt(r,s,o.text),a=o.i,o.forward(o.len-c)}else{if(o.done&&l.done)return r?ne.createSet(s,r):it.create(s);throw new Error("Mismatched change set lengths")}}function Ja(n,e,t=!1){let i=[],s=t?[]:null,r=new Yi(n),o=new Yi(e);for(let l=!1;;){if(r.done&&o.done)return s?ne.createSet(i,s):it.create(i);if(r.ins==0)de(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)de(i,0,o.ins,l),s&&dt(s,i,o.text),o.next();else{if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let c=o.ins==-1?-1:o.off?0:o.ins;de(i,a,c,l),s&&c&&dt(s,i,o.text)}else o.ins==-1?(de(i,r.off?0:r.len,a,l),s&&dt(s,i,r.textBit(a))):(de(i,r.off?0:r.len,o.off?0:o.ins,l),s&&!o.off&&dt(s,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a)}}}}class Yi{constructor(e){this.set=e,this.i=0,this.next()}next(){let{sections:e}=this.set;this.i<e.length?(this.len=e[this.i++],this.ins=e[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:e}=this.set,t=this.i-2>>1;return t>=e.length?B.empty:e[t]}textBit(e){let{inserted:t}=this.set,i=this.i-2>>1;return i>=t.length&&!e?B.empty:t[i].slice(this.off,e==null?void 0:this.off+e)}forward(e){e==this.len?this.next():(this.len-=e,this.off+=e)}forward2(e){this.ins==-1?this.forward(e):e==this.ins?this.next():(this.ins-=e,this.off+=e)}}class At{constructor(e,t,i){this.from=e,this.to=t,this.flags=i}get anchor(){return this.flags&32?this.to:this.from}get head(){return this.flags&32?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&8?-1:this.flags&16?1:0}get bidiLevel(){let e=this.flags&7;return e==7?null:e}get goalColumn(){let e=this.flags>>6;return e==16777215?void 0:e}map(e,t=-1){let i,s;return this.empty?i=s=e.mapPos(this.from,t):(i=e.mapPos(this.from,1),s=e.mapPos(this.to,-1)),i==this.from&&s==this.to?this:new At(i,s,this.flags)}extend(e,t=e){if(e<=this.anchor&&t>=this.anchor)return y.range(e,t);let i=Math.abs(e-this.anchor)>Math.abs(t-this.anchor)?e:t;return y.range(this.anchor,i)}eq(e,t=!1){return this.anchor==e.anchor&&this.head==e.head&&(!t||!this.empty||this.assoc==e.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(e){if(!e||typeof e.anchor!="number"||typeof e.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return y.range(e.anchor,e.head)}static create(e,t,i){return new At(e,t,i)}}class y{constructor(e,t){this.ranges=e,this.mainIndex=t}map(e,t=-1){return e.empty?this:y.create(this.ranges.map(i=>i.map(e,t)),this.mainIndex)}eq(e,t=!1){if(this.ranges.length!=e.ranges.length||this.mainIndex!=e.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(e.ranges[i],t))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new y([this.main],0)}addRange(e,t=!0){return y.create([e].concat(this.ranges),t?0:this.mainIndex+1)}replaceRange(e,t=this.mainIndex){let i=this.ranges.slice();return i[t]=e,y.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(e=>e.toJSON()),main:this.mainIndex}}static fromJSON(e){if(!e||!Array.isArray(e.ranges)||typeof e.main!="number"||e.main>=e.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new y(e.ranges.map(t=>At.fromJSON(t)),e.main)}static single(e,t=e){return new y([y.range(e,t)],0)}static create(e,t=0){if(e.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,s=0;s<e.length;s++){let r=e[s];if(r.empty?r.from<=i:r.from<i)return y.normalized(e.slice(),t);i=r.to}return new y(e,t)}static cursor(e,t=0,i,s){return At.create(e,e,(t==0?0:t<0?8:16)|(i==null?7:Math.min(6,i))|(s??16777215)<<6)}static range(e,t,i,s){let r=(i??16777215)<<6|(s==null?7:Math.min(6,s));return t<e?At.create(t,e,48|r):At.create(e,t,(t>e?8:0)|r)}static normalized(e,t=0){let i=e[t];e.sort((s,r)=>s.from-r.from),t=e.indexOf(i);for(let s=1;s<e.length;s++){let r=e[s],o=e[s-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);s<=t&&t--,e.splice(--s,2,r.anchor>r.head?y.range(a,l):y.range(l,a))}}return new y(e,t)}}function eh(n,e){for(let t of n.ranges)if(t.to>e)throw new RangeError("Selection points outside of document")}let no=0;class C{constructor(e,t,i,s,r){this.combine=e,this.compareInput=t,this.compare=i,this.isStatic=s,this.id=no++,this.default=e([]),this.extensions=typeof r=="function"?r(this):r}get reader(){return this}static define(e={}){return new C(e.combine||(t=>t),e.compareInput||((t,i)=>t===i),e.compare||(e.combine?(t,i)=>t===i:so),!!e.static,e.enables)}of(e){return new An([],this,0,e)}compute(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new An(e,this,1,t)}computeN(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new An(e,this,2,t)}from(e,t){return t||(t=i=>i),this.compute([e],i=>t(i.field(e)))}}function so(n,e){return n==e||n.length==e.length&&n.every((t,i)=>t===e[i])}class An{constructor(e,t,i,s){this.dependencies=e,this.facet=t,this.type=i,this.value=s,this.id=no++}dynamicSlot(e){var t;let i=this.value,s=this.facet.compareInput,r=this.id,o=e[r]>>1,l=this.type==2,a=!1,h=!1,c=[];for(let f of this.dependencies)f=="doc"?a=!0:f=="selection"?h=!0:((t=e[f.id])!==null&&t!==void 0?t:1)&1||c.push(e[f.id]);return{create(f){return f.values[o]=i(f),1},update(f,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||lr(f,c)){let d=i(f);if(l?!Lo(d,f.values[o],s):!s(d,f.values[o]))return f.values[o]=d,1}return 0},reconfigure:(f,u)=>{let d,O=u.config.address[r];if(O!=null){let m=Un(u,O);if(this.dependencies.every(g=>g instanceof C?u.facet(g)===f.facet(g):g instanceof ee?u.field(g,!1)==f.field(g,!1):!0)||(l?Lo(d=i(f),m,s):s(d=i(f),m)))return f.values[o]=m,0}else d=i(f);return f.values[o]=d,1}}}}function Lo(n,e,t){if(n.length!=e.length)return!1;for(let i=0;i<n.length;i++)if(!t(n[i],e[i]))return!1;return!0}function lr(n,e){let t=!1;for(let i of e)Ri(n,i)&1&&(t=!0);return t}function Du(n,e,t){let i=t.map(a=>n[a.id]),s=t.map(a=>a.type),r=i.filter(a=>!(a&1)),o=n[e.id]>>1;function l(a){let h=[];for(let c=0;c<i.length;c++){let f=Un(a,i[c]);if(s[c]==2)for(let u of f)h.push(u);else h.push(f)}return e.combine(h)}return{create(a){for(let h of i)Ri(a,h);return a.values[o]=l(a),1},update(a,h){if(!lr(a,r))return 0;let c=l(a);return e.compare(c,a.values[o])?0:(a.values[o]=c,1)},reconfigure(a,h){let c=lr(a,i),f=h.config.facets[e.id],u=h.facet(e);if(f&&!c&&so(t,f))return a.values[o]=u,0;let d=l(a);return e.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const _o=C.define({static:!0});class ee{constructor(e,t,i,s,r){this.id=e,this.createF=t,this.updateF=i,this.compareF=s,this.spec=r,this.provides=void 0}static define(e){let t=new ee(no++,e.create,e.update,e.compare||((i,s)=>i===s),e);return e.provide&&(t.provides=e.provide(t)),t}create(e){let t=e.facet(_o).find(i=>i.field==this);return((t==null?void 0:t.create)||this.createF)(e)}slot(e){let t=e[this.id]>>1;return{create:i=>(i.values[t]=this.create(i),1),update:(i,s)=>{let r=i.values[t],o=this.updateF(r,s);return this.compareF(r,o)?0:(i.values[t]=o,1)},reconfigure:(i,s)=>s.config.address[this.id]!=null?(i.values[t]=s.field(this),0):(i.values[t]=this.create(i),1)}}init(e){return[this,_o.of({field:this,create:e})]}get extension(){return this}}const Tt={lowest:4,low:3,default:2,high:1,highest:0};function mi(n){return e=>new th(e,n)}const Pt={highest:mi(Tt.highest),high:mi(Tt.high),default:mi(Tt.default),low:mi(Tt.low),lowest:mi(Tt.lowest)};class th{constructor(e,t){this.inner=e,this.prec=t}}class ni{of(e){return new ar(this,e)}reconfigure(e){return ni.reconfigure.of({compartment:this,extension:e})}get(e){return e.config.compartments.get(this)}}class ar{constructor(e,t){this.compartment=e,this.inner=t}}class Bn{constructor(e,t,i,s,r,o){for(this.base=e,this.compartments=t,this.dynamicSlots=i,this.address=s,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(e){let t=this.address[e.id];return t==null?e.default:this.staticValues[t>>1]}static resolve(e,t,i){let s=[],r=Object.create(null),o=new Map;for(let u of qu(e,t,o))u instanceof ee?s.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of s)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let c=i==null?void 0:i.config.facets;for(let u in r){let d=r[u],O=d[0].facet,m=c&&c[u]||[];if(d.every(g=>g.type==0))if(l[O.id]=a.length<<1|1,so(m,d))a.push(i.facet(O));else{let g=O.combine(d.map(b=>b.value));a.push(i&&O.compare(g,i.facet(O))?i.facet(O):g)}else{for(let g of d)g.type==0?(l[g.id]=a.length<<1|1,a.push(g.value)):(l[g.id]=h.length<<1,h.push(b=>g.dynamicSlot(b)));l[O.id]=h.length<<1,h.push(g=>Du(g,O,d))}}let f=h.map(u=>u(l));return new Bn(e,o,f,l,a,r)}}function qu(n,e,t){let i=[[],[],[],[],[]],s=new Map;function r(o,l){let a=s.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof ar&&t.delete(o.compartment)}if(s.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof ar){if(t.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=e.get(o.compartment)||o.inner;t.set(o.compartment,h),r(h,l)}else if(o instanceof th)r(o.inner,o.prec);else if(o instanceof ee)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof An)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,Tt.default);else{let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l)}}return r(n,Tt.default),i.reduce((o,l)=>o.concat(l))}function Ri(n,e){if(e&1)return 2;let t=e>>1,i=n.status[t];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;n.status[t]=4;let s=n.computeSlot(n,n.config.dynamicSlots[t]);return n.status[t]=2|s}function Un(n,e){return e&1?n.config.staticValues[e>>1]:n.values[e>>1]}const ih=C.define(),hr=C.define({combine:n=>n.some(e=>e),static:!0}),nh=C.define({combine:n=>n.length?n[0]:void 0,static:!0}),sh=C.define(),rh=C.define(),oh=C.define(),lh=C.define({combine:n=>n.length?n[0]:!1});class ht{constructor(e,t){this.type=e,this.value=t}static define(){return new zu}}class zu{of(e){return new ht(this,e)}}class ju{constructor(e){this.map=e}of(e){return new M(this,e)}}class M{constructor(e,t){this.type=e,this.value=t}map(e){let t=this.type.map(this.value,e);return t===void 0?void 0:t==this.value?this:new M(this.type,t)}is(e){return this.type==e}static define(e={}){return new ju(e.map||(t=>t))}static mapEffects(e,t){if(!e.length)return e;let i=[];for(let s of e){let r=s.map(t);r&&i.push(r)}return i}}M.reconfigure=M.define();M.appendConfig=M.define();class K{constructor(e,t,i,s,r,o){this.startState=e,this.changes=t,this.selection=i,this.effects=s,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&eh(i,t.newLength),r.some(l=>l.type==K.time)||(this.annotations=r.concat(K.time.of(Date.now())))}static create(e,t,i,s,r,o){return new K(e,t,i,s,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(e){for(let t of this.annotations)if(t.type==e)return t.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(e){let t=this.annotation(K.userEvent);return!!(t&&(t==e||t.length>e.length&&t.slice(0,e.length)==e&&t[e.length]=="."))}}K.time=ht.define();K.userEvent=ht.define();K.addToHistory=ht.define();K.remote=ht.define();function Eu(n,e){let t=[];for(let i=0,s=0;;){let r,o;if(i<n.length&&(s==e.length||e[s]>=n[i]))r=n[i++],o=n[i++];else if(s<e.length)r=e[s++],o=e[s++];else return t;!t.length||t[t.length-1]<r?t.push(r,o):t[t.length-1]<o&&(t[t.length-1]=o)}}function ah(n,e,t){var i;let s,r,o;return t?(s=e.changes,r=ne.empty(e.changes.length),o=n.changes.compose(e.changes)):(s=e.changes.map(n.changes),r=n.changes.mapDesc(e.changes,!0),o=n.changes.compose(s)),{changes:o,selection:e.selection?e.selection.map(r):(i=n.selection)===null||i===void 0?void 0:i.map(s),effects:M.mapEffects(n.effects,s).concat(M.mapEffects(e.effects,r)),annotations:n.annotations.length?n.annotations.concat(e.annotations):e.annotations,scrollIntoView:n.scrollIntoView||e.scrollIntoView}}function cr(n,e,t){let i=e.selection,s=Ft(e.annotations);return e.userEvent&&(s=s.concat(K.userEvent.of(e.userEvent))),{changes:e.changes instanceof ne?e.changes:ne.of(e.changes||[],t,n.facet(nh)),selection:i&&(i instanceof y?i:y.single(i.anchor,i.head)),effects:Ft(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function hh(n,e,t){let i=cr(n,e.length?e[0]:{},n.doc.length);e.length&&e[0].filter===!1&&(t=!1);for(let r=1;r<e.length;r++){e[r].filter===!1&&(t=!1);let o=!!e[r].sequential;i=ah(i,cr(n,e[r],o?i.changes.newLength:n.doc.length),o)}let s=K.create(n,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return Bu(t?Vu(s):s)}function Vu(n){let e=n.startState,t=!0;for(let s of e.facet(sh)){let r=s(n);if(r===!1){t=!1;break}Array.isArray(r)&&(t=t===!0?r:Eu(t,r))}if(t!==!0){let s,r;if(t===!1)r=n.changes.invertedDesc,s=ne.empty(e.doc.length);else{let o=n.changes.filter(t);s=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc}n=K.create(e,s,n.selection&&n.selection.map(r),M.mapEffects(n.effects,r),n.annotations,n.scrollIntoView)}let i=e.facet(rh);for(let s=i.length-1;s>=0;s--){let r=i[s](n);r instanceof K?n=r:Array.isArray(r)&&r.length==1&&r[0]instanceof K?n=r[0]:n=hh(e,Ft(r),!1)}return n}function Bu(n){let e=n.startState,t=e.facet(oh),i=n;for(let s=t.length-1;s>=0;s--){let r=t[s](n);r&&Object.keys(r).length&&(i=ah(i,cr(e,r,n.changes.newLength),!0))}return i==n?n:K.create(e,n.changes,n.selection,i.effects,i.annotations,i.scrollIntoView)}const Uu=[];function Ft(n){return n==null?Uu:Array.isArray(n)?n:[n]}var G=function(n){return n[n.Word=0]="Word",n[n.Space=1]="Space",n[n.Other=2]="Other",n}(G||(G={}));const Lu=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let fr;try{fr=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function _u(n){if(fr)return fr.test(n);for(let e=0;e<n.length;e++){let t=n[e];if(/\w/.test(t)||t>""&&(t.toUpperCase()!=t.toLowerCase()||Lu.test(t)))return!0}return!1}function Nu(n){return e=>{if(!/\S/.test(e))return G.Space;if(_u(e))return G.Word;for(let t=0;t<n.length;t++)if(e.indexOf(n[t])>-1)return G.Word;return G.Other}}class D{constructor(e,t,i,s,r,o){this.config=e,this.doc=t,this.selection=i,this.values=s,this.status=e.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)Ri(this,l<<1);this.computeSlot=null}field(e,t=!0){let i=this.config.address[e.id];if(i==null){if(t)throw new RangeError("Field is not present in this state");return}return Ri(this,i),Un(this,i)}update(...e){return hh(this,e,!0)}applyTransaction(e){let t=this.config,{base:i,compartments:s}=t;for(let l of e.effects)l.is(ni.reconfigure)?(t&&(s=new Map,t.compartments.forEach((a,h)=>s.set(h,a)),t=null),s.set(l.value.compartment,l.value.extension)):l.is(M.reconfigure)?(t=null,i=l.value):l.is(M.appendConfig)&&(t=null,i=Ft(i).concat(l.value));let r;t?r=e.startState.values.slice():(t=Bn.resolve(i,s,this),r=new D(t,this.doc,this.selection,t.dynamicSlots.map(()=>null),(a,h)=>h.reconfigure(a,this),null).values);let o=e.startState.facet(hr)?e.newSelection:e.newSelection.asSingle();new D(t,e.newDoc,o,r,(l,a)=>a.update(l,e),e)}replaceSelection(e){return typeof e=="string"&&(e=this.toText(e)),this.changeByRange(t=>({changes:{from:t.from,to:t.to,insert:e},range:y.cursor(t.from+e.length)}))}changeByRange(e){let t=this.selection,i=e(t.ranges[0]),s=this.changes(i.changes),r=[i.range],o=Ft(i.effects);for(let l=1;l<t.ranges.length;l++){let a=e(t.ranges[l]),h=this.changes(a.changes),c=h.map(s);for(let u=0;u<l;u++)r[u]=r[u].map(c);let f=s.mapDesc(h,!0);r.push(a.range.map(f)),s=s.compose(c),o=M.mapEffects(o,c).concat(M.mapEffects(Ft(a.effects),f))}return{changes:s,selection:y.create(r,t.mainIndex),effects:o}}changes(e=[]){return e instanceof ne?e:ne.of(e,this.doc.length,this.facet(D.lineSeparator))}toText(e){return B.of(e.split(this.facet(D.lineSeparator)||sr))}sliceDoc(e=0,t=this.doc.length){return this.doc.sliceString(e,t,this.lineBreak)}facet(e){let t=this.config.address[e.id];return t==null?e.default:(Ri(this,t),Un(this,t))}toJSON(e){let t={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(e)for(let i in e){let s=e[i];s instanceof ee&&this.config.address[s.id]!=null&&(t[i]=s.spec.toJSON(this.field(e[i]),this))}return t}static fromJSON(e,t={},i){if(!e||typeof e.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let s=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(e,r)){let o=i[r],l=e[r];s.push(o.init(a=>o.spec.fromJSON(l,a)))}}return D.create({doc:e.doc,selection:y.fromJSON(e.selection),extensions:t.extensions?s.concat([t.extensions]):s})}static create(e={}){let t=Bn.resolve(e.extensions||[],new Map),i=e.doc instanceof B?e.doc:B.of((e.doc||"").split(t.staticFacet(D.lineSeparator)||sr)),s=e.selection?e.selection instanceof y?e.selection:y.single(e.selection.anchor,e.selection.head):y.single(0);return eh(s,i.length),t.staticFacet(hr)||(s=s.asSingle()),new D(t,i,s,t.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(D.tabSize)}get lineBreak(){return this.facet(D.lineSeparator)||`
`}get readOnly(){return this.facet(lh)}phrase(e,...t){for(let i of this.facet(D.phrases))if(Object.prototype.hasOwnProperty.call(i,e)){e=i[e];break}return t.length&&(e=e.replace(/\$(\$|\d*)/g,(i,s)=>{if(s=="$")return"$";let r=+(s||1);return!r||r>t.length?i:t[r-1]})),e}languageDataAt(e,t,i=-1){let s=[];for(let r of this.facet(ih))for(let o of r(this,t,i))Object.prototype.hasOwnProperty.call(o,e)&&s.push(o[e]);return s}charCategorizer(e){return Nu(this.languageDataAt("wordChars",e).join(""))}wordAt(e){let{text:t,from:i,length:s}=this.doc.lineAt(e),r=this.charCategorizer(e),o=e-i,l=e-i;for(;o>0;){let a=ue(t,o,!1);if(r(t.slice(a,o))!=G.Word)break;o=a}for(;l<s;){let a=ue(t,l);if(r(t.slice(l,a))!=G.Word)break;l=a}return o==l?null:y.range(o+i,l+i)}}D.allowMultipleSelections=hr;D.tabSize=C.define({combine:n=>n.length?n[0]:4});D.lineSeparator=nh;D.readOnly=lh;D.phrases=C.define({compare(n,e){let t=Object.keys(n),i=Object.keys(e);return t.length==i.length&&t.every(s=>n[s]==e[s])}});D.languageData=ih;D.changeFilter=sh;D.transactionFilter=rh;D.transactionExtender=oh;ni.reconfigure=M.define();function Ve(n,e,t={}){let i={};for(let s of n)for(let r of Object.keys(s)){let o=s[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(t,r))i[r]=t[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let s in e)i[s]===void 0&&(i[s]=e[s]);return i}class Yt{eq(e){return this==e}range(e,t=e){return ur.create(e,t,this)}}Yt.prototype.startSide=Yt.prototype.endSide=0;Yt.prototype.point=!1;Yt.prototype.mapMode=ce.TrackDel;let ur=class ch{constructor(e,t,i){this.from=e,this.to=t,this.value=i}static create(e,t,i){return new ch(e,t,i)}};function dr(n,e){return n.from-e.from||n.value.startSide-e.value.startSide}class ro{constructor(e,t,i,s){this.from=e,this.to=t,this.value=i,this.maxPoint=s}get length(){return this.to[this.to.length-1]}findIndex(e,t,i,s=0){let r=i?this.to:this.from;for(let o=s,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-e||(i?this.value[a].endSide:this.value[a].startSide)-t;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1}}between(e,t,i,s){for(let r=this.findIndex(t,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(s(this.from[r]+e,this.to[r]+e,this.value[r])===!1)return!1}map(e,t){let i=[],s=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],c=this.from[a]+e,f=this.to[a]+e,u,d;if(c==f){let O=t.mapPos(c,h.startSide,h.mapMode);if(O==null||(u=d=O,h.startSide!=h.endSide&&(d=t.mapPos(c,h.endSide),d<u)))continue}else if(u=t.mapPos(c,h.startSide),d=t.mapPos(f,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),s.push(u-o),r.push(d-o))}return{mapped:i.length?new ro(s,r,i,l):null,pos:o}}}class E{constructor(e,t,i,s){this.chunkPos=e,this.chunk=t,this.nextLayer=i,this.maxPoint=s}static create(e,t,i,s){return new E(e,t,i,s)}get length(){let e=this.chunk.length-1;return e<0?0:Math.max(this.chunkEnd(e),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let e=this.nextLayer.size;for(let t of this.chunk)e+=t.value.length;return e}chunkEnd(e){return this.chunkPos[e]+this.chunk[e].length}update(e){let{add:t=[],sort:i=!1,filterFrom:s=0,filterTo:r=this.length}=e,o=e.filter;if(t.length==0&&!o)return this;if(i&&(t=t.slice().sort(dr)),this.isEmpty)return t.length?E.of(t):this;let l=new fh(this,null,-1).goto(0),a=0,h=[],c=new yt;for(;l.value||a<t.length;)if(a<t.length&&(l.from-t[a].from||l.startSide-t[a].value.startSide)>=0){let f=t[a++];c.addInner(f.from,f.to,f.value)||h.push(f)}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==t.length||this.chunkEnd(l.chunkIndex)<t[a].from)&&(!o||s>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&c.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||s>l.to||r<l.from||o(l.from,l.to,l.value))&&(c.addInner(l.from,l.to,l.value)||h.push(ur.create(l.from,l.to,l.value))),l.next());return c.finishInner(this.nextLayer.isEmpty&&!h.length?E.empty:this.nextLayer.update({add:h,filter:o,filterFrom:s,filterTo:r}))}map(e){if(e.empty||this.isEmpty)return this;let t=[],i=[],s=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=e.touchesRange(l,l+a.length);if(h===!1)s=Math.max(s,a.maxPoint),t.push(a),i.push(e.mapPos(l));else if(h===!0){let{mapped:c,pos:f}=a.map(l,e);c&&(s=Math.max(s,c.maxPoint),t.push(c),i.push(f))}}let r=this.nextLayer.map(e);return t.length==0?r:new E(i,t,r||E.empty,s)}between(e,t,i){if(!this.isEmpty){for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],o=this.chunk[s];if(t>=r&&e<=r+o.length&&o.between(r,e-r,t-r,i)===!1)return}this.nextLayer.between(e,t,i)}}iter(e=0){return Di.from([this]).goto(e)}get isEmpty(){return this.nextLayer==this}static iter(e,t=0){return Di.from(e).goto(t)}static compare(e,t,i,s,r=-1){let o=e.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),l=t.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),a=No(o,l,i),h=new gi(o,a,r),c=new gi(l,a,r);i.iterGaps((f,u,d)=>Io(h,f,c,u,d,s)),i.empty&&i.length==0&&Io(h,0,c,0,0,s)}static eq(e,t,i=0,s){s==null&&(s=999999999);let r=e.filter(c=>!c.isEmpty&&t.indexOf(c)<0),o=t.filter(c=>!c.isEmpty&&e.indexOf(c)<0);if(r.length!=o.length)return!1;if(!r.length)return!0;let l=No(r,o),a=new gi(r,l,0).goto(i),h=new gi(o,l,0).goto(i);for(;;){if(a.to!=h.to||!Or(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return!1;if(a.to>s)return!0;a.next(),h.next()}}static spans(e,t,i,s,r=-1){let o=new gi(e,null,r).goto(t),l=t,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point){let c=o.activeForPoint(o.to),f=o.pointFrom<t?c.length+1:o.point.startSide<0?c.length:Math.min(c.length,a);s.point(l,h,o.point,c,f,o.pointRank),a=Math.min(o.openEnd(h),c.length)}else h>l&&(s.span(l,h,o.active,a),a=o.openEnd(h));if(o.to>i)return a+(o.point&&o.to>i?1:0);l=o.to,o.next()}}static of(e,t=!1){let i=new yt;for(let s of e instanceof ur?[e]:t?Iu(e):e)i.add(s.from,s.to,s.value);return i.finish()}static join(e){if(!e.length)return E.empty;let t=e[e.length-1];for(let i=e.length-2;i>=0;i--)for(let s=e[i];s!=E.empty;s=s.nextLayer)t=new E(s.chunkPos,s.chunk,t,Math.max(s.maxPoint,t.maxPoint));return t}}E.empty=new E([],[],null,-1);function Iu(n){if(n.length>1)for(let e=n[0],t=1;t<n.length;t++){let i=n[t];if(dr(e,i)>0)return n.slice().sort(dr);e=i}return n}E.empty.nextLayer=E.empty;class yt{finishChunk(e){this.chunks.push(new ro(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,e&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(e,t,i){this.addInner(e,t,i)||(this.nextLayer||(this.nextLayer=new yt)).add(e,t,i)}addInner(e,t,i){let s=e-this.lastTo||i.startSide-this.last.endSide;if(s<=0&&(e-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return s<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=e),this.from.push(e-this.chunkStart),this.to.push(t-this.chunkStart),this.last=i,this.lastFrom=e,this.lastTo=t,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,t-e)),!0)}addChunk(e,t){if((e-this.lastTo||t.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,t.maxPoint),this.chunks.push(t),this.chunkPos.push(e);let i=t.value.length-1;return this.last=t.value[i],this.lastFrom=t.from[i]+e,this.lastTo=t.to[i]+e,!0}finish(){return this.finishInner(E.empty)}finishInner(e){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return e;let t=E.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(e):e,this.setMaxPoint);return this.from=null,t}}function No(n,e,t){let i=new Map;for(let r of n)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let s=new Set;for(let r of e)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(t?t.mapPos(l):l)==r.chunkPos[o]&&!(t!=null&&t.touchesRange(l,l+r.chunk[o].length))&&s.add(r.chunk[o])}return s}class fh{constructor(e,t,i,s=0){this.layer=e,this.skip=t,this.minPoint=i,this.rank=s}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(e,t=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(e,t,!1),this}gotoInner(e,t,i){for(;this.chunkIndex<this.layer.chunk.length;){let s=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(s)||this.layer.chunkEnd(this.chunkIndex)<e||s.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let s=this.layer.chunk[this.chunkIndex].findIndex(e-this.layer.chunkPos[this.chunkIndex],t,!0);(!i||this.rangeIndex<s)&&this.setRangeIndex(s)}this.next()}forward(e,t){(this.to-e||this.endSide-t)<0&&this.gotoInner(e,t,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let e=this.layer.chunkPos[this.chunkIndex],t=this.layer.chunk[this.chunkIndex],i=e+t.from[this.rangeIndex];if(this.from=i,this.to=e+t.to[this.rangeIndex],this.value=t.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(e){if(e==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=e}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(e){return this.from-e.from||this.startSide-e.startSide||this.rank-e.rank||this.to-e.to||this.endSide-e.endSide}}class Di{constructor(e){this.heap=e}static from(e,t=null,i=-1){let s=[];for(let r=0;r<e.length;r++)for(let o=e[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&s.push(new fh(o,t,i,r));return s.length==1?s[0]:new Di(s)}get startSide(){return this.value?this.value.startSide:0}goto(e,t=-1e9){for(let i of this.heap)i.goto(e,t);for(let i=this.heap.length>>1;i>=0;i--)Cs(this.heap,i);return this.next(),this}forward(e,t){for(let i of this.heap)i.forward(e,t);for(let i=this.heap.length>>1;i>=0;i--)Cs(this.heap,i);(this.to-e||this.value.endSide-t)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let e=this.heap[0];this.from=e.from,this.to=e.to,this.value=e.value,this.rank=e.rank,e.value&&e.next(),Cs(this.heap,0)}}}function Cs(n,e){for(let t=n[e];;){let i=(e<<1)+1;if(i>=n.length)break;let s=n[i];if(i+1<n.length&&s.compare(n[i+1])>=0&&(s=n[i+1],i++),t.compare(s)<0)break;n[i]=t,n[e]=s,e=i}}class gi{constructor(e,t,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Di.from(e,t,i)}goto(e,t=-1e9){return this.cursor.goto(e,t),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=e,this.endSide=t,this.openStart=-1,this.next(),this}forward(e,t){for(;this.minActive>-1&&(this.activeTo[this.minActive]-e||this.active[this.minActive].endSide-t)<0;)this.removeActive(this.minActive);this.cursor.forward(e,t)}removeActive(e){hn(this.active,e),hn(this.activeTo,e),hn(this.activeRank,e),this.minActive=Go(this.active,this.activeTo)}addActive(e){let t=0,{value:i,to:s,rank:r}=this.cursor;for(;t<this.activeRank.length&&(r-this.activeRank[t]||s-this.activeTo[t])>0;)t++;cn(this.active,t,i),cn(this.activeTo,t,s),cn(this.activeRank,t,r),e&&cn(e,t,this.cursor.from),this.minActive=Go(this.active,this.activeTo)}next(){let e=this.to,t=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let s=this.minActive;if(s>-1&&(this.activeTo[s]-this.cursor.from||this.active[s].endSide-this.cursor.startSide)<0){if(this.activeTo[s]>e){this.to=this.activeTo[s],this.endSide=this.active[s].endSide;break}this.removeActive(s),i&&hn(i,s)}else if(this.cursor.value)if(this.cursor.from>e){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let r=this.cursor.value;if(!r.point)this.addActive(i),this.cursor.next();else if(t&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(i){this.openStart=0;for(let s=i.length-1;s>=0&&i[s]<e;s--)this.openStart++}}activeForPoint(e){if(!this.active.length)return this.active;let t=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>e||this.activeTo[i]==e&&this.active[i].endSide>=this.point.endSide)&&t.push(this.active[i]);return t.reverse()}openEnd(e){let t=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>e;i--)t++;return t}}function Io(n,e,t,i,s,r){n.goto(e),t.goto(i);let o=i+s,l=i,a=i-e;for(;;){let h=n.to+a-t.to||n.endSide-t.endSide,c=h<0?n.to+a:t.to,f=Math.min(c,o);if(n.point||t.point?n.point&&t.point&&(n.point==t.point||n.point.eq(t.point))&&Or(n.activeForPoint(n.to),t.activeForPoint(t.to))||r.comparePoint(l,f,n.point,t.point):f>l&&!Or(n.active,t.active)&&r.compareRange(l,f,n.active,t.active),c>o)break;l=c,h<=0&&n.next(),h>=0&&t.next()}}function Or(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(n[t]!=e[t]&&!n[t].eq(e[t]))return!1;return!0}function hn(n,e){for(let t=e,i=n.length-1;t<i;t++)n[t]=n[t+1];n.pop()}function cn(n,e,t){for(let i=n.length-1;i>=e;i--)n[i+1]=n[i];n[e]=t}function Go(n,e){let t=-1,i=1e9;for(let s=0;s<e.length;s++)(e[s]-i||n[s].endSide-n[t].endSide)<0&&(t=s,i=e[s]);return t}function ci(n,e,t=n.length){let i=0;for(let s=0;s<t;)n.charCodeAt(s)==9?(i+=e-i%e,s++):(i++,s=ue(n,s));return i}function pr(n,e,t,i){for(let s=0,r=0;;){if(r>=e)return s;if(s==n.length)break;r+=n.charCodeAt(s)==9?t-r%t:1,s=ue(n,s)}return i===!0?-1:n.length}const mr="ͼ",Fo=typeof Symbol>"u"?"__"+mr:Symbol.for(mr),gr=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),Ho=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class xt{constructor(e,t){this.rules=[];let{finish:i}=t||{};function s(o){return/^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let c=[],f=/^@(\w+)\b/.exec(o[0]),u=f&&f[1]=="keyframes";if(f&&l==null)return a.push(o[0]+";");for(let d in l){let O=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(m=>o.map(g=>m.replace(/&/,g))).reduce((m,g)=>m.concat(g)),O,a);else if(O&&typeof O=="object"){if(!f)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(s(d),O,c,u)}else O!=null&&c.push(d.replace(/_.*/,"").replace(/[A-Z]/g,m=>"-"+m.toLowerCase())+": "+O+";")}(c.length||u)&&a.push((i&&!f&&!h?o.map(i):o).join(", ")+" {"+c.join(" ")+"}")}for(let o in e)r(s(o),e[o],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let e=Ho[Fo]||1;return Ho[Fo]=e+1,mr+e.toString(36)}static mount(e,t,i){let s=e[gr],r=i&&i.nonce;s?r&&s.setNonce(r):s=new Gu(e,r),s.mount(Array.isArray(t)?t:[t],e)}}let Ko=new Map;class Gu{constructor(e,t){let i=e.ownerDocument||e,s=i.defaultView;if(!e.head&&e.adoptedStyleSheets&&s.CSSStyleSheet){let r=Ko.get(i);if(r)return e[gr]=r;this.sheet=new s.CSSStyleSheet,Ko.set(i,this)}else this.styleTag=i.createElement("style"),t&&this.styleTag.setAttribute("nonce",t);this.modules=[],e[gr]=this}mount(e,t){let i=this.sheet,s=0,r=0;for(let o=0;o<e.length;o++){let l=e[o],a=this.modules.indexOf(l);if(a<r&&a>-1&&(this.modules.splice(a,1),r--,a=-1),a==-1){if(this.modules.splice(r++,0,l),i)for(let h=0;h<l.rules.length;h++)i.insertRule(l.rules[h],s++)}else{for(;r<a;)s+=this.modules[r++].rules.length;s+=l.rules.length,r++}}if(i)t.adoptedStyleSheets.indexOf(this.sheet)<0&&(t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets]);else{let o="";for(let a=0;a<this.modules.length;a++)o+=this.modules[a].getRules()+`
`;this.styleTag.textContent=o;let l=t.head||t;this.styleTag.parentNode!=l&&l.insertBefore(this.styleTag,l.firstChild)}}setNonce(e){this.styleTag&&this.styleTag.getAttribute("nonce")!=e&&this.styleTag.setAttribute("nonce",e)}}var St={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},qi={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Fu=typeof navigator<"u"&&/Mac/.test(navigator.platform),Hu=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var he=0;he<10;he++)St[48+he]=St[96+he]=String(he);for(var he=1;he<=24;he++)St[he+111]="F"+he;for(var he=65;he<=90;he++)St[he]=String.fromCharCode(he+32),qi[he]=String.fromCharCode(he);for(var Ts in St)qi.hasOwnProperty(Ts)||(qi[Ts]=St[Ts]);function Ku(n){var e=Fu&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||Hu&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?qi:St)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}function Ln(n){let e;return n.nodeType==11?e=n.getSelection?n:n.ownerDocument:e=n,e.getSelection()}function br(n,e){return e?n==e||n.contains(e.nodeType!=1?e.parentNode:e):!1}function Ju(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function Mn(n,e){if(!e.anchorNode)return!1;try{return br(n,e.anchorNode)}catch{return!1}}function si(n){return n.nodeType==3?qt(n,0,n.nodeValue.length).getClientRects():n.nodeType==1?n.getClientRects():[]}function Ai(n,e,t,i){return t?Jo(n,e,t,i,-1)||Jo(n,e,t,i,1):!1}function Dt(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e}function _n(n){return n.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(n.nodeName)}function Jo(n,e,t,i,s){for(;;){if(n==t&&e==i)return!0;if(e==(s<0?0:lt(n))){if(n.nodeName=="DIV")return!1;let r=n.parentNode;if(!r||r.nodeType!=1)return!1;e=Dt(n)+(s<0?0:1),n=r}else if(n.nodeType==1){if(n=n.childNodes[e+(s<0?-1:0)],n.nodeType==1&&n.contentEditable=="false")return!1;e=s<0?lt(n):0}else return!1}}function lt(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function Os(n,e){let t=e?n.left:n.right;return{left:t,right:t,top:n.top,bottom:n.bottom}}function ed(n){let e=n.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.innerWidth,top:0,bottom:n.innerHeight}}function uh(n,e){let t=e.width/n.offsetWidth,i=e.height/n.offsetHeight;return(t>.995&&t<1.005||!isFinite(t)||Math.abs(e.width-n.offsetWidth)<1)&&(t=1),(i>.995&&i<1.005||!isFinite(i)||Math.abs(e.height-n.offsetHeight)<1)&&(i=1),{scaleX:t,scaleY:i}}function td(n,e,t,i,s,r,o,l){let a=n.ownerDocument,h=a.defaultView||window;for(let c=n,f=!1;c&&!f;)if(c.nodeType==1){let u,d=c==a.body,O=1,m=1;if(d)u=ed(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(f=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let S=c.getBoundingClientRect();({scaleX:O,scaleY:m}=uh(c,S)),u={left:S.left,right:S.left+c.clientWidth*O,top:S.top,bottom:S.top+c.clientHeight*m}}let g=0,b=0;if(s=="nearest")e.top<u.top?(b=-(u.top-e.top+o),t>0&&e.bottom>u.bottom+b&&(b=e.bottom-u.bottom+b+o)):e.bottom>u.bottom&&(b=e.bottom-u.bottom+o,t<0&&e.top-b<u.top&&(b=-(u.top+b-e.top+o)));else{let S=e.bottom-e.top,P=u.bottom-u.top;b=(s=="center"&&S<=P?e.top+S/2-P/2:s=="start"||s=="center"&&t<0?e.top-o:e.bottom-P+o)-u.top}if(i=="nearest"?e.left<u.left?(g=-(u.left-e.left+r),t>0&&e.right>u.right+g&&(g=e.right-u.right+g+r)):e.right>u.right&&(g=e.right-u.right+r,t<0&&e.left<u.left+g&&(g=-(u.left+g-e.left+r))):g=(i=="center"?e.left+(e.right-e.left)/2-(u.right-u.left)/2:i=="start"==l?e.left-r:e.right-(u.right-u.left)+r)-u.left,g||b)if(d)h.scrollBy(g,b);else{let S=0,P=0;if(b){let Q=c.scrollTop;c.scrollTop+=b/m,P=(c.scrollTop-Q)*m}if(g){let Q=c.scrollLeft;c.scrollLeft+=g/O,S=(c.scrollLeft-Q)*O}e={left:e.left-S,top:e.top-P,right:e.right-S,bottom:e.bottom-P},S&&Math.abs(S-g)<1&&(i="nearest"),P&&Math.abs(P-b)<1&&(s="nearest")}if(d)break;c=c.assignedSlot||c.parentNode}else if(c.nodeType==11)c=c.host;else break}function id(n){let e=n.ownerDocument;for(let t=n.parentNode;t&&t!=e.body;)if(t.nodeType==1){if(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth)return t;t=t.assignedSlot||t.parentNode}else if(t.nodeType==11)t=t.host;else break;return null}class nd{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(e){return this.anchorNode==e.anchorNode&&this.anchorOffset==e.anchorOffset&&this.focusNode==e.focusNode&&this.focusOffset==e.focusOffset}setRange(e){let{anchorNode:t,focusNode:i}=e;this.set(t,Math.min(e.anchorOffset,t?lt(t):0),i,Math.min(e.focusOffset,i?lt(i):0))}set(e,t,i,s){this.anchorNode=e,this.anchorOffset=t,this.focusNode=i,this.focusOffset=s}}let Bt=null;function dh(n){if(n.setActive)return n.setActive();if(Bt)return n.focus(Bt);let e=[];for(let t=n;t&&(e.push(t,t.scrollTop,t.scrollLeft),t!=t.ownerDocument);t=t.parentNode);if(n.focus(Bt==null?{get preventScroll(){return Bt={preventScroll:!0},!0}}:void 0),!Bt){Bt=!1;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],r=e[t++];i.scrollTop!=s&&(i.scrollTop=s),i.scrollLeft!=r&&(i.scrollLeft=r)}}}let el;function qt(n,e,t=e){let i=el||(el=document.createRange());return i.setEnd(n,t),i.setStart(n,e),i}function Ht(n,e,t,i){let s={key:e,code:e,keyCode:t,which:t,cancelable:!0};i&&({altKey:s.altKey,ctrlKey:s.ctrlKey,shiftKey:s.shiftKey,metaKey:s.metaKey}=i);let r=new KeyboardEvent("keydown",s);r.synthetic=!0,n.dispatchEvent(r);let o=new KeyboardEvent("keyup",s);return o.synthetic=!0,n.dispatchEvent(o),r.defaultPrevented||o.defaultPrevented}function sd(n){for(;n;){if(n&&(n.nodeType==9||n.nodeType==11&&n.host))return n;n=n.assignedSlot||n.parentNode}return null}function Oh(n){for(;n.attributes.length;)n.removeAttributeNode(n.attributes[0])}function rd(n,e){let t=e.focusNode,i=e.focusOffset;if(!t||e.anchorNode!=t||e.anchorOffset!=i)return!1;for(i=Math.min(i,lt(t));;)if(i){if(t.nodeType!=1)return!1;let s=t.childNodes[i-1];s.contentEditable=="false"?i--:(t=s,i=lt(t))}else{if(t==n)return!0;i=Dt(t),t=t.parentNode}}function ph(n){return n.scrollTop>Math.max(1,n.scrollHeight-n.clientHeight-4)}function mh(n,e){for(let t=n,i=e;;){if(t.nodeType==3&&i>0)return{node:t,offset:i};if(t.nodeType==1&&i>0){if(t.contentEditable=="false")return null;t=t.childNodes[i-1],i=lt(t)}else if(t.parentNode&&!_n(t))i=Dt(t),t=t.parentNode;else return null}}function gh(n,e){for(let t=n,i=e;;){if(t.nodeType==3&&i<t.nodeValue.length)return{node:t,offset:i};if(t.nodeType==1&&i<t.childNodes.length){if(t.contentEditable=="false")return null;t=t.childNodes[i],i=0}else if(t.parentNode&&!_n(t))i=Dt(t)+1,t=t.parentNode;else return null}}class Oe{constructor(e,t,i=!0){this.node=e,this.offset=t,this.precise=i}static before(e,t){return new Oe(e.parentNode,Dt(e),t)}static after(e,t){return new Oe(e.parentNode,Dt(e)+1,t)}}const oo=[];class L{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(e){let t=this.posAtStart;for(let i of this.children){if(i==e)return t;t+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(e){return this.posBefore(e)+e.length}sync(e,t){if(this.flags&2){let i=this.dom,s=null,r;for(let o of this.children){if(o.flags&7){if(!o.dom&&(r=s?s.nextSibling:i.firstChild)){let l=L.get(r);(!l||!l.parent&&l.canReuseDOM(o))&&o.reuseDOM(r)}o.sync(e,t),o.flags&=-8}if(r=s?s.nextSibling:i.firstChild,t&&!t.written&&t.node==i&&r!=o.dom&&(t.written=!0),o.dom.parentNode==i)for(;r&&r!=o.dom;)r=tl(r);else i.insertBefore(o.dom,r);s=o.dom}for(r=s?s.nextSibling:i.firstChild,r&&t&&t.node==i&&(t.written=!0);r;)r=tl(r)}else if(this.flags&1)for(let i of this.children)i.flags&7&&(i.sync(e,t),i.flags&=-8)}reuseDOM(e){}localPosFromDOM(e,t){let i;if(e==this.dom)i=this.dom.childNodes[t];else{let s=lt(e)==0?0:t==0?-1:1;for(;;){let r=e.parentNode;if(r==this.dom)break;s==0&&r.firstChild!=r.lastChild&&(e==r.firstChild?s=-1:s=1),e=r}s<0?i=e:i=e.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!L.get(i);)i=i.nextSibling;if(!i)return this.length;for(let s=0,r=0;;s++){let o=this.children[s];if(o.dom==i)return r;r+=o.length+o.breakAfter}}domBoundsAround(e,t,i=0){let s=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,c=i;a<this.children.length;a++){let f=this.children[a],u=h+f.length;if(h<e&&u>t)return f.domBoundsAround(e,t,h);if(u>=e&&s==-1&&(s=a,r=h),h>t&&f.dom.parentNode==this.dom){o=a,l=c;break}c=u,h=u+f.breakAfter}return{from:r,to:l<0?i+this.length:l,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(e=!1){this.flags|=2,this.markParentsDirty(e)}markParentsDirty(e){for(let t=this.parent;t;t=t.parent){if(e&&(t.flags|=2),t.flags&1)return;t.flags|=1,e=!1}}setParent(e){this.parent!=e&&(this.parent=e,this.flags&7&&this.markParentsDirty(!0))}setDOM(e){this.dom!=e&&(this.dom&&(this.dom.cmView=null),this.dom=e,e.cmView=this)}get rootView(){for(let e=this;;){let t=e.parent;if(!t)return e;e=t}}replaceChildren(e,t,i=oo){this.markDirty();for(let s=e;s<t;s++){let r=this.children[s];r.parent==this&&i.indexOf(r)<0&&r.destroy()}this.children.splice(e,t-e,...i);for(let s=0;s<i.length;s++)i[s].setParent(this)}ignoreMutation(e){return!1}ignoreEvent(e){return!1}childCursor(e=this.length){return new bh(this.children,e,this.children.length)}childPos(e,t=1){return this.childCursor().findPos(e,t)}toString(){let e=this.constructor.name.replace("View","");return e+(this.children.length?"("+this.children.join()+")":this.length?"["+(e=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(e){return e.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(e,t,i,s,r,o){return!1}become(e){return!1}canReuseDOM(e){return e.constructor==this.constructor&&!((this.flags|e.flags)&8)}getSide(){return 0}destroy(){for(let e of this.children)e.parent==this&&e.destroy();this.parent=null}}L.prototype.breakAfter=0;function tl(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class bh{constructor(e,t,i){this.children=e,this.pos=t,this.i=i,this.off=0}findPos(e,t=1){for(;;){if(e>this.pos||e==this.pos&&(t>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=e-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function yh(n,e,t,i,s,r,o,l,a){let{children:h}=n,c=h.length?h[e]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(e==i&&c&&!o&&!u&&r.length<2&&c.merge(t,s,r.length?f:null,t==0,l,a))){if(i<h.length){let d=h[i];d&&(s<d.length||d.breakAfter&&(f!=null&&f.breakAfter))?(e==i&&(d=d.split(s),s=0),!u&&f&&d.merge(0,s,f,!0,0,a)?r[r.length-1]=d:((s||d.children.length&&!d.children[0].length)&&d.merge(0,s,null,!1,0,a),r.push(d))):d!=null&&d.breakAfter&&(f?f.breakAfter=1:o=1),i++}for(c&&(c.breakAfter=o,t>0&&(!o&&r.length&&c.merge(t,c.length,r[0],!1,l,0)?c.breakAfter=r.shift().breakAfter:(t<c.length||c.children.length&&c.children[c.children.length-1].length==0)&&c.merge(t,c.length,null,!1,l,0),e++));e<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[e].become(r[0]))e++,r.shift(),l=r.length?0:a;else break;!r.length&&e&&i<h.length&&!h[e-1].breakAfter&&h[i].merge(0,0,h[e-1],!1,l,a)&&e--,(e<i||r.length)&&n.replaceChildren(e,i,r)}}function xh(n,e,t,i,s,r){let o=n.childCursor(),{i:l,off:a}=o.findPos(t,1),{i:h,off:c}=o.findPos(e,-1),f=e-t;for(let u of i)f+=u.length;n.length+=f,yh(n,h,c,l,a,i,0,s,r)}let Re=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},yr=typeof document<"u"?document:{documentElement:{style:{}}};const xr=/Edge\/(\d+)/.exec(Re.userAgent),Sh=/MSIE \d/.test(Re.userAgent),Sr=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Re.userAgent),ps=!!(Sh||Sr||xr),il=!ps&&/gecko\/(\d+)/i.test(Re.userAgent),Rs=!ps&&/Chrome\/(\d+)/.exec(Re.userAgent),od="webkitFontSmoothing"in yr.documentElement.style,wh=!ps&&/Apple Computer/.test(Re.vendor),nl=wh&&(/Mobile\/\w+/.test(Re.userAgent)||Re.maxTouchPoints>2);var T={mac:nl||/Mac/.test(Re.platform),windows:/Win/.test(Re.platform),linux:/Linux|X11/.test(Re.platform),ie:ps,ie_version:Sh?yr.documentMode||6:Sr?+Sr[1]:xr?+xr[1]:0,gecko:il,gecko_version:il?+(/Firefox\/(\d+)/.exec(Re.userAgent)||[0,0])[1]:0,chrome:!!Rs,chrome_version:Rs?+Rs[1]:0,ios:nl,android:/Android\b/.test(Re.userAgent),safari:wh,webkit_version:od?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:yr.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const ld=256;class je extends L{constructor(e){super(),this.text=e}get length(){return this.text.length}createDOM(e){this.setDOM(e||document.createTextNode(this.text))}sync(e,t){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(t&&t.node==this.dom&&(t.written=!0),this.dom.nodeValue=this.text)}reuseDOM(e){e.nodeType==3&&this.createDOM(e)}merge(e,t,i){return this.flags&8||i&&(!(i instanceof je)||this.length-(t-e)+i.length>ld||i.flags&8)?!1:(this.text=this.text.slice(0,e)+(i?i.text:"")+this.text.slice(t),this.markDirty(),!0)}split(e){let t=new je(this.text.slice(e));return this.text=this.text.slice(0,e),this.markDirty(),t.flags|=this.flags&8,t}localPosFromDOM(e,t){return e==this.dom?t:t?this.text.length:0}domAtPos(e){return new Oe(this.dom,e)}domBoundsAround(e,t,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(e,t){return ad(this.dom,e,t)}}class at extends L{constructor(e,t=[],i=0){super(),this.mark=e,this.children=t,this.length=i;for(let s of t)s.setParent(this)}setAttrs(e){if(Oh(e),this.mark.class&&(e.className=this.mark.class),this.mark.attrs)for(let t in this.mark.attrs)e.setAttribute(t,this.mark.attrs[t]);return e}canReuseDOM(e){return super.canReuseDOM(e)&&!((this.flags|e.flags)&8)}reuseDOM(e){e.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(e),this.flags|=6)}sync(e,t){this.dom?this.flags&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(e,t)}merge(e,t,i,s,r,o){return i&&(!(i instanceof at&&i.mark.eq(this.mark))||e&&r<=0||t<this.length&&o<=0)?!1:(xh(this,e,t,i?i.children.slice():[],r-1,o-1),this.markDirty(),!0)}split(e){let t=[],i=0,s=-1,r=0;for(let l of this.children){let a=i+l.length;a>e&&t.push(i<e?l.split(e-i):l),s<0&&i>=e&&(s=r),i=a,r++}let o=this.length-e;return this.length=e,s>-1&&(this.children.length=s,this.markDirty()),new at(this.mark,t,o)}domAtPos(e){return Qh(this,e)}coordsAt(e,t){return Ph(this,e,t)}}function ad(n,e,t){let i=n.nodeValue.length;e>i&&(e=i);let s=e,r=e,o=0;e==0&&t<0||e==i&&t>=0?T.chrome||T.gecko||(e?(s--,o=1):r<i&&(r++,o=-1)):t<0?s--:r<i&&r++;let l=qt(n,s,r).getClientRects();if(!l.length)return null;let a=l[(o?o<0:t>=0)?0:l.length-1];return T.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?Os(a,o<0):a||null}class Ot extends L{static create(e,t,i){return new Ot(e,t,i)}constructor(e,t,i){super(),this.widget=e,this.length=t,this.side=i,this.prevWidget=null}split(e){let t=Ot.create(this.widget,this.length-e,this.side);return this.length-=e,t}sync(e){(!this.dom||!this.widget.updateDOM(this.dom,e))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(e)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(e,t,i,s,r,o){return i&&(!(i instanceof Ot)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}become(e){return e instanceof Ot&&e.side==this.side&&this.widget.constructor==e.widget.constructor?(this.widget.compare(e.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,this.length=e.length,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}get overrideDOMText(){if(this.length==0)return B.empty;let e=this;for(;e.parent;)e=e.parent;let{view:t}=e,i=t&&t.state.doc,s=this.posAtStart;return i?i.slice(s,s+this.length):B.empty}domAtPos(e){return(this.length?e==0:this.side>0)?Oe.before(this.dom):Oe.after(this.dom,e==this.length)}domBoundsAround(){return null}coordsAt(e,t){let i=this.widget.coordsAt(this.dom,e,t);if(i)return i;let s=this.dom.getClientRects(),r=null;if(!s.length)return null;let o=this.side?this.side<0:e>0;for(let l=o?s.length-1:0;r=s[l],!(e>0?l==0:l==s.length-1||r.top<r.bottom);l+=o?-1:1);return Os(r,!o)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class ri extends L{constructor(e){super(),this.side=e}get length(){return 0}merge(){return!1}become(e){return e instanceof ri&&e.side==this.side}split(){return new ri(this.side)}sync(){if(!this.dom){let e=document.createElement("img");e.className="cm-widgetBuffer",e.setAttribute("aria-hidden","true"),this.setDOM(e)}}getSide(){return this.side}domAtPos(e){return this.side>0?Oe.before(this.dom):Oe.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(e){return this.dom.getBoundingClientRect()}get overrideDOMText(){return B.empty}get isHidden(){return!0}}je.prototype.children=Ot.prototype.children=ri.prototype.children=oo;function Qh(n,e){let t=n.dom,{children:i}=n,s=0;for(let r=0;s<i.length;s++){let o=i[s],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(e>r&&e<l&&o.dom.parentNode==t)return o.domAtPos(e-r);if(e<=r)break;r=l}}for(let r=s;r>0;r--){let o=i[r-1];if(o.dom.parentNode==t)return o.domAtPos(o.length)}for(let r=s;r<i.length;r++){let o=i[r];if(o.dom.parentNode==t)return o.domAtPos(0)}return new Oe(t,0)}function kh(n,e,t){let i,{children:s}=n;t>0&&e instanceof at&&s.length&&(i=s[s.length-1])instanceof at&&i.mark.eq(e.mark)?kh(i,e.children[0],t-1):(s.push(e),e.setParent(n)),n.length+=e.length}function Ph(n,e,t){let i=null,s=-1,r=null,o=-1;function l(h,c){for(let f=0,u=0;f<h.children.length&&u<=c;f++){let d=h.children[f],O=u+d.length;O>=c&&(d.children.length?l(d,c-u):(!r||r.isHidden&&t>0)&&(O>c||u==O&&d.getSide()>0)?(r=d,o=c-u):(u<c||u==O&&d.getSide()<0&&!d.isHidden)&&(i=d,s=c-u)),u=O}}l(n,e);let a=(t<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?s:o),t):hd(n)}function hd(n){let e=n.dom.lastChild;if(!e)return n.dom.getBoundingClientRect();let t=si(e);return t[t.length-1]||null}function wr(n,e){for(let t in n)t=="class"&&e.class?e.class+=" "+n.class:t=="style"&&e.style?e.style+=";"+n.style:e[t]=n[t];return e}const sl=Object.create(null);function lo(n,e,t){if(n==e)return!0;n||(n=sl),e||(e=sl);let i=Object.keys(n),s=Object.keys(e);if(i.length-(t&&i.indexOf(t)>-1?1:0)!=s.length-(t&&s.indexOf(t)>-1?1:0))return!1;for(let r of i)if(r!=t&&(s.indexOf(r)==-1||n[r]!==e[r]))return!1;return!0}function Qr(n,e,t){let i=!1;if(e)for(let s in e)t&&s in t||(i=!0,s=="style"?n.style.cssText="":n.removeAttribute(s));if(t)for(let s in t)e&&e[s]==t[s]||(i=!0,s=="style"?n.style.cssText=t[s]:n.setAttribute(s,t[s]));return i}function cd(n){let e=Object.create(null);for(let t=0;t<n.attributes.length;t++){let i=n.attributes[t];e[i.name]=i.value}return e}class ie extends L{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(e,t,i,s,r,o){if(i){if(!(i instanceof ie))return!1;this.dom||i.transferDOM(this)}return s&&this.setDeco(i?i.attrs:null),xh(this,e,t,i?i.children.slice():[],r,o),!0}split(e){let t=new ie;if(t.breakAfter=this.breakAfter,this.length==0)return t;let{i,off:s}=this.childPos(e);s&&(t.append(this.children[i].split(s),0),this.children[i].merge(s,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)t.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=e,t}transferDOM(e){this.dom&&(this.markDirty(),e.setDOM(this.dom),e.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(e){lo(this.attrs,e)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=e)}append(e,t){kh(this,e,t)}addLineDeco(e){let t=e.spec.attributes,i=e.spec.class;t&&(this.attrs=wr(t,this.attrs||{})),i&&(this.attrs=wr({class:i},this.attrs||{}))}domAtPos(e){return Qh(this,e)}reuseDOM(e){e.nodeName=="DIV"&&(this.setDOM(e),this.flags|=6)}sync(e,t){var i;this.dom?this.flags&4&&(Oh(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(Qr(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(e,t);let s=this.dom.lastChild;for(;s&&L.get(s)instanceof at;)s=s.lastChild;if(!s||!this.length||s.nodeName!="BR"&&((i=L.get(s))===null||i===void 0?void 0:i.isEditable)==!1&&(!T.ios||!this.children.some(r=>r instanceof je))){let r=document.createElement("BR");r.cmIgnore=!0,this.dom.appendChild(r)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let e=0,t;for(let i of this.children){if(!(i instanceof je)||/[^ -~]/.test(i.text))return null;let s=si(i.dom);if(s.length!=1)return null;e+=s[0].width,t=s[0].height}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length,textHeight:t}:null}coordsAt(e,t){let i=Ph(this,e,t);if(!this.children.length&&i&&this.parent){let{heightOracle:s}=this.parent.view.viewState,r=i.bottom-i.top;if(Math.abs(r-s.lineHeight)<2&&s.textHeight<r){let o=(r-s.textHeight)/2;return{top:i.top+o,bottom:i.bottom-o,left:i.left,right:i.left}}}return i}become(e){return!1}covers(){return!0}static find(e,t){for(let i=0,s=0;i<e.children.length;i++){let r=e.children[i],o=s+r.length;if(o>=t){if(r instanceof ie)return r;if(o>t)break}s=o+r.breakAfter}return null}}class mt extends L{constructor(e,t,i){super(),this.widget=e,this.length=t,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(e,t,i,s,r,o){return i&&(!(i instanceof mt)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}domAtPos(e){return e==0?Oe.before(this.dom):Oe.after(this.dom,e==this.length)}split(e){let t=this.length-e;this.length=e;let i=new mt(this.widget,t,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return oo}sync(e){(!this.dom||!this.widget.updateDOM(this.dom,e))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(e)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):B.empty}domBoundsAround(){return null}become(e){return e instanceof mt&&e.widget.constructor==this.widget.constructor?(e.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,this.length=e.length,this.deco=e.deco,this.breakAfter=e.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(e,t){return this.widget.coordsAt(this.dom,e,t)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(e){let{startSide:t,endSide:i}=this.deco;return t==i?!1:e<0?t<0:i>0}}class st{eq(e){return!1}updateDOM(e,t){return!1}compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(e){return!0}coordsAt(e,t,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(e){}}var be=function(n){return n[n.Text=0]="Text",n[n.WidgetBefore=1]="WidgetBefore",n[n.WidgetAfter=2]="WidgetAfter",n[n.WidgetRange=3]="WidgetRange",n}(be||(be={}));class A extends Yt{constructor(e,t,i,s){super(),this.startSide=e,this.endSide=t,this.widget=i,this.spec=s}get heightRelevant(){return!1}static mark(e){return new Ki(e)}static widget(e){let t=Math.max(-1e4,Math.min(1e4,e.side||0)),i=!!e.block;return t+=i&&!e.inlineOrder?t>0?3e8:-4e8:t>0?1e8:-1e8,new wt(e,t,t,i,e.widget||null,!1)}static replace(e){let t=!!e.block,i,s;if(e.isBlockGap)i=-5e8,s=4e8;else{let{start:r,end:o}=$h(e,t);i=(r?t?-3e8:-1:5e8)-1,s=(o?t?2e8:1:-6e8)+1}return new wt(e,i,s,t,e.widget||null,!0)}static line(e){return new Ji(e)}static set(e,t=!1){return E.of(e,t)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}A.none=E.empty;class Ki extends A{constructor(e){let{start:t,end:i}=$h(e);super(t?-1:5e8,i?1:-6e8,null,e),this.tagName=e.tagName||"span",this.class=e.class||"",this.attrs=e.attributes||null}eq(e){var t,i;return this==e||e instanceof Ki&&this.tagName==e.tagName&&(this.class||((t=this.attrs)===null||t===void 0?void 0:t.class))==(e.class||((i=e.attrs)===null||i===void 0?void 0:i.class))&&lo(this.attrs,e.attrs,"class")}range(e,t=e){if(e>=t)throw new RangeError("Mark decorations may not be empty");return super.range(e,t)}}Ki.prototype.point=!1;class Ji extends A{constructor(e){super(-2e8,-2e8,null,e)}eq(e){return e instanceof Ji&&this.spec.class==e.spec.class&&lo(this.spec.attributes,e.spec.attributes)}range(e,t=e){if(t!=e)throw new RangeError("Line decoration ranges must be zero-length");return super.range(e,t)}}Ji.prototype.mapMode=ce.TrackBefore;Ji.prototype.point=!0;class wt extends A{constructor(e,t,i,s,r,o){super(t,i,r,e),this.block=s,this.isReplace=o,this.mapMode=s?t<=0?ce.TrackBefore:ce.TrackAfter:ce.TrackDel}get type(){return this.startSide!=this.endSide?be.WidgetRange:this.startSide<=0?be.WidgetBefore:be.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(e){return e instanceof wt&&fd(this.widget,e.widget)&&this.block==e.block&&this.startSide==e.startSide&&this.endSide==e.endSide}range(e,t=e){if(this.isReplace&&(e>t||e==t&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&t!=e)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(e,t)}}wt.prototype.point=!0;function $h(n,e=!1){let{inclusiveStart:t,inclusiveEnd:i}=n;return t==null&&(t=n.inclusive),i==null&&(i=n.inclusive),{start:t??e,end:i??e}}function fd(n,e){return n==e||!!(n&&e&&n.compare(e))}function kr(n,e,t,i=0){let s=t.length-1;s>=0&&t[s]+i>=n?t[s]=Math.max(t[s],e):t.push(n,e)}class Mi{constructor(e,t,i,s){this.doc=e,this.pos=t,this.end=i,this.disallowBlockEffectsFor=s,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=e.iter(),this.skip=t}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let e=this.content[this.content.length-1];return!(e.breakAfter||e instanceof mt&&e.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new ie),this.atCursorPos=!0),this.curLine}flushBuffer(e=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(fn(new ri(-1),e),e.length),this.pendingBuffer=0)}addBlockWidget(e){this.flushBuffer(),this.curLine=null,this.content.push(e)}finish(e){this.pendingBuffer&&e<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,!this.posCovered()&&!(e&&this.content.length&&this.content[this.content.length-1]instanceof mt)&&this.getLine()}buildText(e,t,i){for(;e>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,e--;continue}else this.text=r,this.textOff=0}let s=Math.min(this.text.length-this.textOff,e,512);this.flushBuffer(t.slice(t.length-i)),this.getLine().append(fn(new je(this.text.slice(this.textOff,this.textOff+s)),t),i),this.atCursorPos=!0,this.textOff+=s,e-=s,i=0}}span(e,t,i,s){this.buildText(t-e,i,s),this.pos=t,this.openStart<0&&(this.openStart=s)}point(e,t,i,s,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof wt){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(t>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=t-e;if(i instanceof wt)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new mt(i.widget||oi.block,l,i));else{let a=Ot.create(i.widget||oi.inline,l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=s.length&&(e<t||i.startSide>0),c=!a.isEditable&&(e<t||r>s.length||i.startSide<=0),f=this.getLine();this.pendingBuffer==2&&!h&&!a.isEditable&&(this.pendingBuffer=0),this.flushBuffer(s),h&&(f.append(fn(new ri(1),s),r),r=s.length+Math.max(0,r-s.length)),f.append(fn(a,s),r),this.atCursorPos=c,this.pendingBuffer=c?e<t||r>s.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=s.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=t),this.openStart<0&&(this.openStart=r)}static build(e,t,i,s,r){let o=new Mi(e,t,i,r);return o.openEnd=E.spans(s,t,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function fn(n,e){for(let t of e)n=new at(t,[n],n.length);return n}class oi extends st{constructor(e){super(),this.tag=e}eq(e){return e.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(e){return e.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}oi.inline=new oi("span");oi.block=new oi("div");var I=function(n){return n[n.LTR=0]="LTR",n[n.RTL=1]="RTL",n}(I||(I={}));const zt=I.LTR,ao=I.RTL;function vh(n){let e=[];for(let t=0;t<n.length;t++)e.push(1<<+n[t]);return e}const ud=vh("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),dd=vh("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),Pr=Object.create(null),Le=[];for(let n of["()","[]","{}"]){let e=n.charCodeAt(0),t=n.charCodeAt(1);Pr[e]=t,Pr[t]=-e}function Zh(n){return n<=247?ud[n]:1424<=n&&n<=1524?2:1536<=n&&n<=1785?dd[n-1536]:1774<=n&&n<=2220?4:8192<=n&&n<=8204?256:64336<=n&&n<=65023?4:1}const Od=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class pt{get dir(){return this.level%2?ao:zt}constructor(e,t,i){this.from=e,this.to=t,this.level=i}side(e,t){return this.dir==t==e?this.to:this.from}forward(e,t){return e==(this.dir==t)}static find(e,t,i,s){let r=-1;for(let o=0;o<e.length;o++){let l=e[o];if(l.from<=t&&l.to>=t){if(l.level==i)return o;(r<0||(s!=0?s<0?l.from<t:l.to>t:e[r].level>l.level))&&(r=o)}}if(r<0)throw new RangeError("Index out of range");return r}}function Ch(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],s=e[t];if(i.from!=s.from||i.to!=s.to||i.direction!=s.direction||!Ch(i.inner,s.inner))return!1}return!0}const U=[];function pd(n,e,t,i,s){for(let r=0;r<=i.length;r++){let o=r?i[r-1].to:e,l=r<i.length?i[r].from:t,a=r?256:s;for(let h=o,c=a,f=a;h<l;h++){let u=Zh(n.charCodeAt(h));u==512?u=c:u==8&&f==4&&(u=16),U[h]=u==4?2:u,u&7&&(f=u),c=u}for(let h=o,c=a,f=a;h<l;h++){let u=U[h];if(u==128)h<l-1&&c==U[h+1]&&c&24?u=U[h]=c:U[h]=256;else if(u==64){let d=h+1;for(;d<l&&U[d]==64;)d++;let O=h&&c==8||d<t&&U[d]==8?f==1?1:8:256;for(let m=h;m<d;m++)U[m]=O;h=d-1}else u==8&&f==1&&(U[h]=1);c=u,u&7&&(f=u)}}}function md(n,e,t,i,s){let r=s==1?2:1;for(let o=0,l=0,a=0;o<=i.length;o++){let h=o?i[o-1].to:e,c=o<i.length?i[o].from:t;for(let f=h,u,d,O;f<c;f++)if(d=Pr[u=n.charCodeAt(f)])if(d<0){for(let m=l-3;m>=0;m-=3)if(Le[m+1]==-d){let g=Le[m+2],b=g&2?s:g&4?g&1?r:s:0;b&&(U[f]=U[Le[m]]=b),l=m;break}}else{if(Le.length==189)break;Le[l++]=f,Le[l++]=u,Le[l++]=a}else if((O=U[f])==2||O==1){let m=O==s;a=m?0:1;for(let g=l-3;g>=0;g-=3){let b=Le[g+2];if(b&2)break;if(m)Le[g+2]|=2;else{if(b&4)break;Le[g+2]|=4}}}}}function gd(n,e,t,i){for(let s=0,r=i;s<=t.length;s++){let o=s?t[s-1].to:n,l=s<t.length?t[s].from:e;for(let a=o;a<l;){let h=U[a];if(h==256){let c=a+1;for(;;)if(c==l){if(s==t.length)break;c=t[s++].to,l=s<t.length?t[s].from:e}else if(U[c]==256)c++;else break;let f=r==1,u=(c<e?U[c]:i)==1,d=f==u?f?1:2:i;for(let O=c,m=s,g=m?t[m-1].to:n;O>a;)O==g&&(O=t[--m].from,g=m?t[m-1].to:n),U[--O]=d;a=c}else r=h,a++}}}function $r(n,e,t,i,s,r,o){let l=i%2?2:1;if(i%2==s%2)for(let a=e,h=0;a<t;){let c=!0,f=!1;if(h==r.length||a<r[h].from){let m=U[a];m!=l&&(c=!1,f=m==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,O=a;e:for(;;)if(h<r.length&&O==r[h].from){if(f)break e;let m=r[h];if(!c)for(let g=m.to,b=h+1;;){if(g==t)break e;if(b<r.length&&r[b].from==g)g=r[b++].to;else{if(U[g]==l)break e;break}}if(h++,u)u.push(m);else{m.from>a&&o.push(new pt(a,m.from,d));let g=m.direction==zt!=!(d%2);vr(n,g?i+1:i,s,m.inner,m.from,m.to,o),a=m.to}O=m.to}else{if(O==t||(c?U[O]!=l:U[O]==l))break;O++}u?$r(n,a,O,i+1,s,u,o):a<O&&o.push(new pt(a,O,d)),a=O}else for(let a=t,h=r.length;a>e;){let c=!0,f=!1;if(!h||a>r[h-1].to){let m=U[a-1];m!=l&&(c=!1,f=m==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,O=a;e:for(;;)if(h&&O==r[h-1].to){if(f)break e;let m=r[--h];if(!c)for(let g=m.from,b=h;;){if(g==e)break e;if(b&&r[b-1].to==g)g=r[--b].from;else{if(U[g-1]==l)break e;break}}if(u)u.push(m);else{m.to<a&&o.push(new pt(m.to,a,d));let g=m.direction==zt!=!(d%2);vr(n,g?i+1:i,s,m.inner,m.from,m.to,o),a=m.from}O=m.from}else{if(O==e||(c?U[O-1]!=l:U[O-1]==l))break;O--}u?$r(n,O,a,i+1,s,u,o):O<a&&o.push(new pt(O,a,d)),a=O}}function vr(n,e,t,i,s,r,o){let l=e%2?2:1;pd(n,s,r,i,l),md(n,s,r,i,l),gd(s,r,i,l),$r(n,s,r,e,t,i,o)}function bd(n,e,t){if(!n)return[new pt(0,0,e==ao?1:0)];if(e==zt&&!t.length&&!Od.test(n))return Th(n.length);if(t.length)for(;n.length>U.length;)U[U.length]=256;let i=[],s=e==zt?0:1;return vr(n,s,s,t,0,n.length,i),i}function Th(n){return[new pt(0,n,0)]}let Rh="";function yd(n,e,t,i,s){var r;let o=i.head-n.from,l=pt.find(e,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc),a=e[l],h=a.side(s,t);if(o==h){let u=l+=s?1:-1;if(u<0||u>=e.length)return null;a=e[l=u],o=a.side(!s,t),h=a.side(s,t)}let c=ue(n.text,o,a.forward(s,t));(c<a.from||c>a.to)&&(c=h),Rh=n.text.slice(Math.min(o,c),Math.max(o,c));let f=l==(s?e.length-1:0)?null:e[l+(s?1:-1)];return f&&c==h&&f.level+(s?0:1)<a.level?y.cursor(f.side(!s,t)+n.from,f.forward(s,t)?1:-1,f.level):y.cursor(c+n.from,a.forward(s,t)?-1:1,a.level)}function xd(n,e,t){for(let i=e;i<t;i++){let s=Zh(n.charCodeAt(i));if(s==1)return zt;if(s==2||s==4)return ao}return zt}const Ah=C.define(),Mh=C.define(),Wh=C.define(),Xh=C.define(),Zr=C.define(),Yh=C.define(),Dh=C.define(),qh=C.define({combine:n=>n.some(e=>e)}),zh=C.define({combine:n=>n.some(e=>e)}),jh=C.define();class Kt{constructor(e,t="nearest",i="nearest",s=5,r=5,o=!1){this.range=e,this.y=t,this.x=i,this.yMargin=s,this.xMargin=r,this.isSnapshot=o}map(e){return e.empty?this:new Kt(this.range.map(e),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(e){return this.range.to<=e.doc.length?this:new Kt(y.cursor(e.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const un=M.define({map:(n,e)=>n.map(e)});function ge(n,e,t){let i=n.facet(Xh);i.length?i[0](e):window.onerror?window.onerror(String(e),t,void 0,void 0,e):t?console.error(t+":",e):console.error(e)}const ms=C.define({combine:n=>n.length?n[0]:!0});let Sd=0;const ki=C.define();class F{constructor(e,t,i,s,r){this.id=e,this.create=t,this.domEventHandlers=i,this.domEventObservers=s,this.extension=r(this)}static define(e,t){const{eventHandlers:i,eventObservers:s,provide:r,decorations:o}=t||{};return new F(Sd++,e,i,s,l=>{let a=[ki.of(l)];return o&&a.push(zi.of(h=>{let c=h.plugin(l);return c?o(c):A.none})),r&&a.push(r(l)),a})}static fromClass(e,t){return F.define(i=>new e(i),t)}}class As{constructor(e){this.spec=e,this.mustUpdate=null,this.value=null}update(e){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(i){if(ge(t.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(e)}catch(t){ge(e.state,t,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(e){var t;if(!((t=this.value)===null||t===void 0)&&t.destroy)try{this.value.destroy()}catch(i){ge(e.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const Eh=C.define(),ho=C.define(),zi=C.define(),Vh=C.define(),co=C.define(),Bh=C.define();function rl(n,e){let t=n.state.facet(Bh);if(!t.length)return t;let i=t.map(r=>r instanceof Function?r(n):r),s=[];return E.spans(i,e.from,e.to,{point(){},span(r,o,l,a){let h=r-e.from,c=o-e.from,f=s;for(let u=l.length-1;u>=0;u--,a--){let d=l[u].spec.bidiIsolate,O;if(d==null&&(d=xd(e.text,h,c)),a>0&&f.length&&(O=f[f.length-1]).to==h&&O.direction==d)O.to=c,f=O.inner;else{let m={from:h,to:c,direction:d,inner:[]};f.push(m),f=m.inner}}}}),s}const Uh=C.define();function Lh(n){let e=0,t=0,i=0,s=0;for(let r of n.state.facet(Uh)){let o=r(n);o&&(o.left!=null&&(e=Math.max(e,o.left)),o.right!=null&&(t=Math.max(t,o.right)),o.top!=null&&(i=Math.max(i,o.top)),o.bottom!=null&&(s=Math.max(s,o.bottom)))}return{left:e,right:t,top:i,bottom:s}}const Pi=C.define();class Ye{constructor(e,t,i,s){this.fromA=e,this.toA=t,this.fromB=i,this.toB=s}join(e){return new Ye(Math.min(this.fromA,e.fromA),Math.max(this.toA,e.toA),Math.min(this.fromB,e.fromB),Math.max(this.toB,e.toB))}addToSet(e){let t=e.length,i=this;for(;t>0;t--){let s=e[t-1];if(!(s.fromA>i.toA)){if(s.toA<i.fromA)break;i=i.join(s),e.splice(t-1,1)}}return e.splice(t,0,i),e}static extendWithRanges(e,t){if(t.length==0)return e;let i=[];for(let s=0,r=0,o=0,l=0;;s++){let a=s==e.length?null:e[s],h=o-l,c=a?a.fromB:1e9;for(;r<t.length&&t[r]<c;){let f=t[r],u=t[r+1],d=Math.max(l,f),O=Math.min(c,u);if(d<=O&&new Ye(d+h,O+h,d,O).addToSet(i),u>c)break;r+=2}if(!a)return i;new Ye(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB}}}class Nn{constructor(e,t,i){this.view=e,this.state=t,this.transactions=i,this.flags=0,this.startState=e.state,this.changes=ne.empty(this.startState.doc.length);for(let r of i)this.changes=this.changes.compose(r.changes);let s=[];this.changes.iterChangedRanges((r,o,l,a)=>s.push(new Ye(r,o,l,a))),this.changedRanges=s}static create(e,t,i){return new Nn(e,t,i)}get viewportChanged(){return(this.flags&4)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&10)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(e=>e.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}class ol extends L{get length(){return this.view.state.doc.length}constructor(e){super(),this.view=e,this.decorations=[],this.dynamicDecorationMap=[],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(e.contentDOM),this.children=[new ie],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Ye(0,0,0,e.state.doc.length)],0,null)}update(e){var t;let i=e.changedRanges;this.minWidth>0&&i.length&&(i.every(({fromA:h,toA:c})=>c<this.minWidthFrom||h>this.minWidthTo)?(this.minWidthFrom=e.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=e.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0);let s=-1;this.view.inputState.composing>=0&&(!((t=this.domChanged)===null||t===void 0)&&t.newSel?s=this.domChanged.newSel.head:!Zd(e.changes,this.hasComposition)&&!e.selectionSet&&(s=e.state.selection.main.head));let r=s>-1?Qd(this.view,e.changes,s):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:h,to:c}=this.hasComposition;i=new Ye(h,c,e.changes.mapPos(h,-1),e.changes.mapPos(c,1)).addToSet(i.slice())}this.hasComposition=r?{from:r.range.fromB,to:r.range.toB}:null,(T.ie||T.chrome)&&!r&&e&&e.state.doc.lines!=e.startState.doc.lines&&(this.forceSelection=!0);let o=this.decorations,l=this.updateDeco(),a=$d(o,l,e.changes);return i=Ye.extendWithRanges(i,a),!(this.flags&7)&&i.length==0?!1:(this.updateInner(i,e.startState.doc.length,r),e.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(e,t,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(e,t,i);let{observer:s}=this.view;s.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let o=T.chrome||T.ios?{node:s.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,o),this.flags&=-8,o&&(o.written||s.selectionRange.focusNode!=o.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(o=>o.flags&=-9);let r=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let o of this.children)o instanceof mt&&o.widget instanceof ll&&r.push(o.dom);s.updateGaps(r)}updateChildren(e,t,i){let s=i?i.range.addToSet(e.slice()):e,r=this.childCursor(t);for(let o=s.length-1;;o--){let l=o>=0?s[o]:null;if(!l)break;let{fromA:a,toA:h,fromB:c,toB:f}=l,u,d,O,m;if(i&&i.range.fromB<f&&i.range.toB>c){let Q=Mi.build(this.view.state.doc,c,i.range.fromB,this.decorations,this.dynamicDecorationMap),x=Mi.build(this.view.state.doc,i.range.toB,f,this.decorations,this.dynamicDecorationMap);d=Q.breakAtStart,O=Q.openStart,m=x.openEnd;let Z=this.compositionView(i);x.breakAtStart?Z.breakAfter=1:x.content.length&&Z.merge(Z.length,Z.length,x.content[0],!1,x.openStart,0)&&(Z.breakAfter=x.content[0].breakAfter,x.content.shift()),Q.content.length&&Z.merge(0,0,Q.content[Q.content.length-1],!0,0,Q.openEnd)&&Q.content.pop(),u=Q.content.concat(Z).concat(x.content)}else({content:u,breakAtStart:d,openStart:O,openEnd:m}=Mi.build(this.view.state.doc,c,f,this.decorations,this.dynamicDecorationMap));let{i:g,off:b}=r.findPos(h,1),{i:S,off:P}=r.findPos(a,-1);yh(this,S,P,g,b,u,d,O,m)}i&&this.fixCompositionDOM(i)}compositionView(e){let t=new je(e.text.nodeValue);t.flags|=8;for(let{deco:s}of e.marks)t=new at(s,[t],t.length);let i=new ie;return i.append(t,0),i}fixCompositionDOM(e){let t=(r,o)=>{o.flags|=8|(o.children.some(a=>a.flags&7)?1:0),this.markedForComposition.add(o);let l=L.get(r);l&&l!=o&&(l.dom=null),o.setDOM(r)},i=this.childPos(e.range.fromB,1),s=this.children[i.i];t(e.line,s);for(let r=e.marks.length-1;r>=-1;r--)i=s.childPos(i.off,1),s=s.children[i.i],t(r>=0?e.marks[r].node:e.text,s)}updateSelection(e=!1,t=!1){(e||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let i=this.view.root.activeElement,s=i==this.dom,r=!s&&Mn(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(s||t||r))return;let o=this.forceSelection;this.forceSelection=!1;let l=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(l.anchor)),h=l.empty?a:this.moveToLine(this.domAtPos(l.head));if(T.gecko&&l.empty&&!this.hasComposition&&wd(a)){let f=document.createTextNode("");this.view.observer.ignore(()=>a.node.insertBefore(f,a.node.childNodes[a.offset]||null)),a=h=new Oe(f,0),o=!0}let c=this.view.observer.selectionRange;(o||!c.focusNode||(!Ai(a.node,a.offset,c.anchorNode,c.anchorOffset)||!Ai(h.node,h.offset,c.focusNode,c.focusOffset))&&!this.suppressWidgetCursorChange(c,l))&&(this.view.observer.ignore(()=>{T.android&&T.chrome&&this.dom.contains(c.focusNode)&&vd(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let f=Ln(this.view.root);if(f)if(l.empty){if(T.gecko){let u=kd(a.node,a.offset);if(u&&u!=3){let d=(u==1?mh:gh)(a.node,a.offset);d&&(a=new Oe(d.node,d.offset))}}f.collapse(a.node,a.offset),l.bidiLevel!=null&&f.caretBidiLevel!==void 0&&(f.caretBidiLevel=l.bidiLevel)}else if(f.extend){f.collapse(a.node,a.offset);try{f.extend(h.node,h.offset)}catch{}}else{let u=document.createRange();l.anchor>l.head&&([a,h]=[h,a]),u.setEnd(h.node,h.offset),u.setStart(a.node,a.offset),f.removeAllRanges(),f.addRange(u)}r&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())}),this.view.observer.setSelectionRange(a,h)),this.impreciseAnchor=a.precise?null:new Oe(c.anchorNode,c.anchorOffset),this.impreciseHead=h.precise?null:new Oe(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(e,t){return this.hasComposition&&t.empty&&Ai(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)&&this.posFromDOM(e.focusNode,e.focusOffset)==t.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:e}=this,t=e.state.selection.main,i=Ln(e.root),{anchorNode:s,anchorOffset:r}=e.observer.selectionRange;if(!i||!t.empty||!t.assoc||!i.modify)return;let o=ie.find(this,t.head);if(!o)return;let l=o.posAtStart;if(t.head==l||t.head==l+o.length)return;let a=this.coordsAt(t.head,-1),h=this.coordsAt(t.head,1);if(!a||!h||a.bottom>h.top)return;let c=this.domAtPos(t.head+t.assoc);i.collapse(c.node,c.offset),i.modify("move",t.assoc<0?"forward":"backward","lineboundary"),e.observer.readSelectionRange();let f=e.observer.selectionRange;e.docView.posFromDOM(f.anchorNode,f.anchorOffset)!=t.from&&i.collapse(s,r)}moveToLine(e){let t=this.dom,i;if(e.node!=t)return e;for(let s=e.offset;!i&&s<t.childNodes.length;s++){let r=L.get(t.childNodes[s]);r instanceof ie&&(i=r.domAtPos(0))}for(let s=e.offset-1;!i&&s>=0;s--){let r=L.get(t.childNodes[s]);r instanceof ie&&(i=r.domAtPos(r.length))}return i?new Oe(i.node,i.offset,!0):e}nearest(e){for(let t=e;t;){let i=L.get(t);if(i&&i.rootView==this)return i;t=t.parentNode}return null}posFromDOM(e,t){let i=this.nearest(e);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(e,t)+i.posAtStart}domAtPos(e){let{i:t,off:i}=this.childCursor().findPos(e,-1);for(;t<this.children.length-1;){let s=this.children[t];if(i<s.length||s instanceof ie)break;t++,i=0}return this.children[t].domAtPos(i)}coordsAt(e,t){let i=null,s=0;for(let r=this.length,o=this.children.length-1;o>=0;o--){let l=this.children[o],a=r-l.breakAfter,h=a-l.length;if(a<e)break;h<=e&&(h<e||l.covers(-1))&&(a>e||l.covers(1))&&(!i||l instanceof ie&&!(i instanceof ie&&t>=0))&&(i=l,s=h),r=h}return i?i.coordsAt(e-s,t):null}coordsForChar(e){let{i:t,off:i}=this.childPos(e,1),s=this.children[t];if(!(s instanceof ie))return null;for(;s.children.length;){let{i:l,off:a}=s.childPos(i,1);for(;;l++){if(l==s.children.length)return null;if((s=s.children[l]).length)break}i=a}if(!(s instanceof je))return null;let r=ue(s.text,i);if(r==i)return null;let o=qt(s.dom,i,r).getClientRects();for(let l=0;l<o.length;l++){let a=o[l];if(l==o.length-1||a.top<a.bottom&&a.left<a.right)return a}return null}measureVisibleLineHeights(e){let t=[],{from:i,to:s}=e,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==I.LTR;for(let h=0,c=0;c<this.children.length;c++){let f=this.children[c],u=h+f.length;if(u>s)break;if(h>=i){let d=f.dom.getBoundingClientRect();if(t.push(d.height),o){let O=f.dom.lastChild,m=O?si(O):[];if(m.length){let g=m[m.length-1],b=a?g.right-d.left:d.right-g.left;b>l&&(l=b,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u)}}}h=u+f.breakAfter}return t}textDirectionAt(e){let{i:t}=this.childPos(e,1);return getComputedStyle(this.children[t].dom).direction=="rtl"?I.RTL:I.LTR}measureTextSize(){for(let r of this.children)if(r instanceof ie){let o=r.measureTextSize();if(o)return o}let e=document.createElement("div"),t,i,s;return e.className="cm-line",e.style.width="99999px",e.style.position="absolute",e.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(e);let r=si(e.firstChild)[0];t=e.getBoundingClientRect().height,i=r?r.width/27:7,s=r?r.height:t,e.remove()}),{lineHeight:t,charWidth:i,textHeight:s}}childCursor(e=this.length){let t=this.children.length;return t&&(e-=this.children[--t].length),new bh(this.children,e,t)}computeBlockGapDeco(){let e=[],t=this.view.viewState;for(let i=0,s=0;;s++){let r=s==t.viewports.length?null:t.viewports[s],o=r?r.from-1:this.length;if(o>i){let l=(t.lineBlockAt(o).bottom-t.lineBlockAt(i).top)/this.view.scaleY;e.push(A.replace({widget:new ll(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o))}if(!r)break;i=r.to+1}return A.set(e)}updateDeco(){let e=0,t=this.view.state.facet(zi).map(r=>(this.dynamicDecorationMap[e++]=typeof r=="function")?r(this.view):r),i=!1,s=this.view.state.facet(Vh).map((r,o)=>{let l=typeof r=="function";return l&&(i=!0),l?r(this.view):r});for(s.length&&(this.dynamicDecorationMap[e++]=i,t.push(E.join(s))),this.decorations=[...t,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];e<this.decorations.length;)this.dynamicDecorationMap[e++]=!1;return this.decorations}scrollIntoView(e){if(e.isSnapshot){let h=this.view.viewState.lineBlockAt(e.range.head);this.view.scrollDOM.scrollTop=h.top-e.yMargin,this.view.scrollDOM.scrollLeft=e.xMargin;return}for(let h of this.view.state.facet(jh))try{if(h(this.view,e.range,e))return!0}catch(c){ge(this.view.state,c,"scroll handler")}let{range:t}=e,i=this.coordsAt(t.head,t.empty?t.assoc:t.head>t.anchor?-1:1),s;if(!i)return;!t.empty&&(s=this.coordsAt(t.anchor,t.anchor>t.head?-1:1))&&(i={left:Math.min(i.left,s.left),top:Math.min(i.top,s.top),right:Math.max(i.right,s.right),bottom:Math.max(i.bottom,s.bottom)});let r=Lh(this.view),o={left:i.left-r.left,top:i.top-r.top,right:i.right+r.right,bottom:i.bottom+r.bottom},{offsetWidth:l,offsetHeight:a}=this.view.scrollDOM;td(this.view.scrollDOM,o,t.head<t.anchor?-1:1,e.x,e.y,Math.max(Math.min(e.xMargin,l),-l),Math.max(Math.min(e.yMargin,a),-a),this.view.textDirection==I.LTR)}}function wd(n){return n.node.nodeType==1&&n.node.firstChild&&(n.offset==0||n.node.childNodes[n.offset-1].contentEditable=="false")&&(n.offset==n.node.childNodes.length||n.node.childNodes[n.offset].contentEditable=="false")}class ll extends st{constructor(e){super(),this.height=e}toDOM(){let e=document.createElement("div");return e.className="cm-gap",this.updateDOM(e),e}eq(e){return e.height==this.height}updateDOM(e){return e.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}function _h(n,e){let t=n.observer.selectionRange;if(!t.focusNode)return null;let i=mh(t.focusNode,t.focusOffset),s=gh(t.focusNode,t.focusOffset),r=i||s;if(s&&i&&s.node!=i.node){let l=L.get(s.node);if(!l||l instanceof je&&l.text!=s.node.nodeValue)r=s;else if(n.docView.lastCompositionAfterCursor){let a=L.get(i.node);!a||a instanceof je&&a.text!=i.node.nodeValue||(r=s)}}if(n.docView.lastCompositionAfterCursor=r!=i,!r)return null;let o=e-r.offset;return{from:o,to:o+r.node.nodeValue.length,node:r.node}}function Qd(n,e,t){let i=_h(n,t);if(!i)return null;let{node:s,from:r,to:o}=i,l=s.nodeValue;if(/[\n\r]/.test(l)||n.state.doc.sliceString(i.from,i.to)!=l)return null;let a=e.invertedDesc,h=new Ye(a.mapPos(r),a.mapPos(o),r,o),c=[];for(let f=s.parentNode;;f=f.parentNode){let u=L.get(f);if(u instanceof at)c.push({node:f,deco:u.mark});else{if(u instanceof ie||f.nodeName=="DIV"&&f.parentNode==n.contentDOM)return{range:h,text:s,marks:c,line:f};if(f!=n.contentDOM)c.push({node:f,deco:new Ki({inclusive:!0,attributes:cd(f),tagName:f.tagName.toLowerCase()})});else return null}}}function kd(n,e){return n.nodeType!=1?0:(e&&n.childNodes[e-1].contentEditable=="false"?1:0)|(e<n.childNodes.length&&n.childNodes[e].contentEditable=="false"?2:0)}let Pd=class{constructor(){this.changes=[]}compareRange(e,t){kr(e,t,this.changes)}comparePoint(e,t){kr(e,t,this.changes)}};function $d(n,e,t){let i=new Pd;return E.compare(n,e,t,i),i.changes}function vd(n,e){for(let t=n;t&&t!=e;t=t.assignedSlot||t.parentNode)if(t.nodeType==1&&t.contentEditable=="false")return!0;return!1}function Zd(n,e){let t=!1;return e&&n.iterChangedRanges((i,s)=>{i<e.to&&s>e.from&&(t=!0)}),t}function Cd(n,e,t=1){let i=n.charCategorizer(e),s=n.doc.lineAt(e),r=e-s.from;if(s.length==0)return y.cursor(e);r==0?t=1:r==s.length&&(t=-1);let o=r,l=r;t<0?o=ue(s.text,r,!1):l=ue(s.text,r);let a=i(s.text.slice(o,l));for(;o>0;){let h=ue(s.text,o,!1);if(i(s.text.slice(h,o))!=a)break;o=h}for(;l<s.length;){let h=ue(s.text,l);if(i(s.text.slice(l,h))!=a)break;l=h}return y.range(o+s.from,l+s.from)}function Td(n,e){return e.left>n?e.left-n:Math.max(0,n-e.right)}function Rd(n,e){return e.top>n?e.top-n:Math.max(0,n-e.bottom)}function Ms(n,e){return n.top<e.bottom-1&&n.bottom>e.top+1}function al(n,e){return e<n.top?{top:e,left:n.left,right:n.right,bottom:n.bottom}:n}function hl(n,e){return e>n.bottom?{top:n.top,left:n.left,right:n.right,bottom:e}:n}function Cr(n,e,t){let i,s,r,o,l=!1,a,h,c,f;for(let O=n.firstChild;O;O=O.nextSibling){let m=si(O);for(let g=0;g<m.length;g++){let b=m[g];s&&Ms(s,b)&&(b=al(hl(b,s.bottom),s.top));let S=Td(e,b),P=Rd(t,b);if(S==0&&P==0)return O.nodeType==3?cl(O,e,t):Cr(O,e,t);if(!i||o>P||o==P&&r>S){i=O,s=b,r=S,o=P;let Q=P?t<b.top?-1:1:S?e<b.left?-1:1:0;l=!Q||(Q>0?g<m.length-1:g>0)}S==0?t>b.bottom&&(!c||c.bottom<b.bottom)?(a=O,c=b):t<b.top&&(!f||f.top>b.top)&&(h=O,f=b):c&&Ms(c,b)?c=hl(c,b.bottom):f&&Ms(f,b)&&(f=al(f,b.top))}}if(c&&c.bottom>=t?(i=a,s=c):f&&f.top<=t&&(i=h,s=f),!i)return{node:n,offset:0};let u=Math.max(s.left,Math.min(s.right,e));if(i.nodeType==3)return cl(i,u,t);if(l&&i.contentEditable!="false")return Cr(i,u,t);let d=Array.prototype.indexOf.call(n.childNodes,i)+(e>=(s.left+s.right)/2?1:0);return{node:n,offset:d}}function cl(n,e,t){let i=n.nodeValue.length,s=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=qt(n,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let c=a[h];if(c.top==c.bottom)continue;o||(o=e-c.left);let f=(c.top>t?c.top-t:t-c.bottom)-1;if(c.left-1<=e&&c.right+1>=e&&f<r){let u=e>=(c.left+c.right)/2,d=u;if((T.chrome||T.gecko)&&qt(n,l).getBoundingClientRect().left==c.right&&(d=!u),f<=0)return{node:n,offset:l+(d?1:0)};s=l+(d?1:0),r=f}}}return{node:n,offset:s>-1?s:o>0?n.nodeValue.length:0}}function Nh(n,e,t,i=-1){var s,r;let o=n.contentDOM.getBoundingClientRect(),l=o.top+n.viewState.paddingTop,a,{docHeight:h}=n.viewState,{x:c,y:f}=e,u=f-l;if(u<0)return 0;if(u>h)return n.state.doc.length;for(let Q=n.viewState.heightOracle.textHeight/2,x=!1;a=n.elementAtHeight(u),a.type!=be.Text;)for(;u=i>0?a.bottom+Q:a.top-Q,!(u>=0&&u<=h);){if(x)return t?null:0;x=!0,i=-i}f=l+u;let d=a.from;if(d<n.viewport.from)return n.viewport.from==0?0:t?null:fl(n,o,a,c,f);if(d>n.viewport.to)return n.viewport.to==n.state.doc.length?n.state.doc.length:t?null:fl(n,o,a,c,f);let O=n.dom.ownerDocument,m=n.root.elementFromPoint?n.root:O,g=m.elementFromPoint(c,f);g&&!n.contentDOM.contains(g)&&(g=null),g||(c=Math.max(o.left+1,Math.min(o.right-1,c)),g=m.elementFromPoint(c,f),g&&!n.contentDOM.contains(g)&&(g=null));let b,S=-1;if(g&&((s=n.docView.nearest(g))===null||s===void 0?void 0:s.isEditable)!=!1){if(O.caretPositionFromPoint){let Q=O.caretPositionFromPoint(c,f);Q&&({offsetNode:b,offset:S}=Q)}else if(O.caretRangeFromPoint){let Q=O.caretRangeFromPoint(c,f);Q&&({startContainer:b,startOffset:S}=Q,(!n.contentDOM.contains(b)||T.safari&&Ad(b,S,c)||T.chrome&&Md(b,S,c))&&(b=void 0))}}if(!b||!n.docView.dom.contains(b)){let Q=ie.find(n.docView,d);if(!Q)return u>a.top+a.height/2?a.to:a.from;({node:b,offset:S}=Cr(Q.dom,c,f))}let P=n.docView.nearest(b);if(!P)return null;if(P.isWidget&&((r=P.dom)===null||r===void 0?void 0:r.nodeType)==1){let Q=P.dom.getBoundingClientRect();return e.y<Q.top||e.y<=Q.bottom&&e.x<=(Q.left+Q.right)/2?P.posAtStart:P.posAtEnd}else return P.localPosFromDOM(b,S)+P.posAtStart}function fl(n,e,t,i,s){let r=Math.round((i-e.left)*n.defaultCharacterWidth);if(n.lineWrapping&&t.height>n.defaultLineHeight*1.5){let l=n.viewState.heightOracle.textHeight,a=Math.floor((s-t.top-(n.defaultLineHeight-l)*.5)/l);r+=a*n.viewState.heightOracle.lineLength}let o=n.state.sliceDoc(t.from,t.to);return t.from+pr(o,r,n.state.tabSize)}function Ad(n,e,t){let i;if(n.nodeType!=3||e!=(i=n.nodeValue.length))return!1;for(let s=n.nextSibling;s;s=s.nextSibling)if(s.nodeType!=1||s.nodeName!="BR")return!1;return qt(n,i-1,i).getBoundingClientRect().left>t}function Md(n,e,t){if(e!=0)return!1;for(let s=n;;){let r=s.parentNode;if(!r||r.nodeType!=1||r.firstChild!=s)return!1;if(r.classList.contains("cm-line"))break;s=r}let i=n.nodeType==1?n.getBoundingClientRect():qt(n,0,Math.max(n.nodeValue.length,1)).getBoundingClientRect();return t-i.left>5}function Tr(n,e){let t=n.lineBlockAt(e);if(Array.isArray(t.type)){for(let i of t.type)if(i.to>e||i.to==e&&(i.to==t.to||i.type==be.Text))return i}return t}function Wd(n,e,t,i){let s=Tr(n,e.head),r=!i||s.type!=be.Text||!(n.lineWrapping||s.widgetLineBreaks)?null:n.coordsAtPos(e.assoc<0&&e.head>s.from?e.head-1:e.head);if(r){let o=n.dom.getBoundingClientRect(),l=n.textDirectionAt(s.from),a=n.posAtCoords({x:t==(l==I.LTR)?o.right-1:o.left+1,y:(r.top+r.bottom)/2});if(a!=null)return y.cursor(a,t?-1:1)}return y.cursor(t?s.to:s.from,t?-1:1)}function ul(n,e,t,i){let s=n.state.doc.lineAt(e.head),r=n.bidiSpans(s),o=n.textDirectionAt(s.from);for(let l=e,a=null;;){let h=yd(s,r,o,l,t),c=Rh;if(!h){if(s.number==(t?n.state.doc.lines:1))return l;c=`
`,s=n.state.doc.line(s.number+(t?1:-1)),r=n.bidiSpans(s),h=n.visualLineSide(s,!t)}if(a){if(!a(c))return l}else{if(!i)return h;a=i(c)}l=h}}function Xd(n,e,t){let i=n.state.charCategorizer(e),s=i(t);return r=>{let o=i(r);return s==G.Space&&(s=o),s==o}}function Yd(n,e,t,i){let s=e.head,r=t?1:-1;if(s==(t?n.state.doc.length:0))return y.cursor(s,e.assoc);let o=e.goalColumn,l,a=n.contentDOM.getBoundingClientRect(),h=n.coordsAtPos(s,e.assoc||-1),c=n.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else{let d=n.viewState.lineBlockAt(s);o==null&&(o=Math.min(a.right-a.left,n.defaultCharacterWidth*(s-d.from))),l=(r<0?d.top:d.bottom)+c}let f=a.left+o,u=i??n.viewState.heightOracle.textHeight>>1;for(let d=0;;d+=10){let O=l+(u+d)*r,m=Nh(n,{x:f,y:O},!1,r);if(O<a.top||O>a.bottom||(r<0?m<s:m>s)){let g=n.docView.coordsForChar(m),b=!g||O<g.top?-1:1;return y.cursor(m,b,void 0,o)}}}function Wn(n,e,t){for(;;){let i=0;for(let s of n)s.between(e-1,e+1,(r,o,l)=>{if(e>r&&e<o){let a=i||t||(e-r<o-e?-1:1);e=a<0?r:o,i=a}});if(!i)return e}}function Ws(n,e,t){let i=Wn(n.state.facet(co).map(s=>s(n)),t.from,e.head>t.from?-1:1);return i==t.from?t:y.cursor(i,i<t.from?1:-1)}class Dd{setSelectionOrigin(e){this.lastSelectionOrigin=e,this.lastSelectionTime=Date.now()}constructor(e){this.view=e,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=e.hasFocus,T.safari&&e.contentDOM.addEventListener("input",()=>null),T.gecko&&Jd(e.contentDOM.ownerDocument)}handleEvent(e){!Ld(this.view,e)||this.ignoreDuringComposition(e)||e.type=="keydown"&&this.keydown(e)||this.runHandlers(e.type,e)}runHandlers(e,t){let i=this.handlers[e];if(i){for(let s of i.observers)s(this.view,t);for(let s of i.handlers){if(t.defaultPrevented)break;if(s(this.view,t)){t.preventDefault();break}}}}ensureHandlers(e){let t=qd(e),i=this.handlers,s=this.view.contentDOM;for(let r in t)if(r!="scroll"){let o=!t[r].handlers.length,l=i[r];l&&o!=!l.handlers.length&&(s.removeEventListener(r,this.handleEvent),l=null),l||s.addEventListener(r,this.handleEvent,{passive:o})}for(let r in i)r!="scroll"&&!t[r]&&s.removeEventListener(r,this.handleEvent);this.handlers=t}keydown(e){if(this.lastKeyCode=e.keyCode,this.lastKeyTime=Date.now(),e.keyCode==9&&Date.now()<this.lastEscPress+2e3)return!0;if(e.keyCode!=27&&Gh.indexOf(e.keyCode)<0&&(this.view.inputState.lastEscPress=0),T.android&&T.chrome&&!e.synthetic&&(e.keyCode==13||e.keyCode==8))return this.view.observer.delayAndroidKey(e.key,e.keyCode),!0;let t;return T.ios&&!e.synthetic&&!e.altKey&&!e.metaKey&&((t=Ih.find(i=>i.keyCode==e.keyCode))&&!e.ctrlKey||zd.indexOf(e.key)>-1&&e.ctrlKey&&!e.shiftKey)?(this.pendingIOSKey=t||e,setTimeout(()=>this.flushIOSKey(),250),!0):(e.keyCode!=229&&this.view.observer.forceFlush(),!1)}flushIOSKey(e){let t=this.pendingIOSKey;return!t||t.key=="Enter"&&e&&e.from<e.to&&/^\S+$/.test(e.insert.toString())?!1:(this.pendingIOSKey=void 0,Ht(this.view.contentDOM,t.key,t.keyCode,t instanceof KeyboardEvent?t:void 0))}ignoreDuringComposition(e){return/^key/.test(e.type)?this.composing>0?!0:T.safari&&!T.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100?(this.compositionPendingKey=!1,!0):!1:!1}startMouseSelection(e){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=e}update(e){this.mouseSelection&&this.mouseSelection.update(e),this.draggedContent&&e.docChanged&&(this.draggedContent=this.draggedContent.map(e.changes)),e.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function dl(n,e){return(t,i)=>{try{return e.call(n,i,t)}catch(s){ge(t.state,s)}}}function qd(n){let e=Object.create(null);function t(i){return e[i]||(e[i]={observers:[],handlers:[]})}for(let i of n){let s=i.spec;if(s&&s.domEventHandlers)for(let r in s.domEventHandlers){let o=s.domEventHandlers[r];o&&t(r).handlers.push(dl(i.value,o))}if(s&&s.domEventObservers)for(let r in s.domEventObservers){let o=s.domEventObservers[r];o&&t(r).observers.push(dl(i.value,o))}}for(let i in Ee)t(i).handlers.push(Ee[i]);for(let i in De)t(i).observers.push(De[i]);return e}const Ih=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],zd="dthko",Gh=[16,17,18,20,91,92,224,225],dn=6;function On(n){return Math.max(0,n)*.7+8}function jd(n,e){return Math.max(Math.abs(n.clientX-e.clientX),Math.abs(n.clientY-e.clientY))}class Ed{constructor(e,t,i,s){this.view=e,this.startEvent=t,this.style=i,this.mustSelect=s,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=t,this.scrollParent=id(e.contentDOM),this.atoms=e.state.facet(co).map(o=>o(e));let r=e.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=t.shiftKey,this.multiple=e.state.facet(D.allowMultipleSelections)&&Vd(e,t),this.dragging=Ud(e,t)&&Jh(t)==1?null:!1}start(e){this.dragging===!1&&this.select(e)}move(e){var t;if(e.buttons==0)return this.destroy();if(this.dragging||this.dragging==null&&jd(this.startEvent,e)<10)return;this.select(this.lastEvent=e);let i=0,s=0,r=((t=this.scrollParent)===null||t===void 0?void 0:t.getBoundingClientRect())||{left:0,top:0,right:this.view.win.innerWidth,bottom:this.view.win.innerHeight},o=Lh(this.view);e.clientX-o.left<=r.left+dn?i=-On(r.left-e.clientX):e.clientX+o.right>=r.right-dn&&(i=On(e.clientX-r.right)),e.clientY-o.top<=r.top+dn?s=-On(r.top-e.clientY):e.clientY+o.bottom>=r.bottom-dn&&(s=On(e.clientY-r.bottom)),this.setScrollSpeed(i,s)}up(e){this.dragging==null&&this.select(this.lastEvent),this.dragging||e.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let e=this.view.contentDOM.ownerDocument;e.removeEventListener("mousemove",this.move),e.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(e,t){this.scrollSpeed={x:e,y:t},e||t?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){this.scrollParent?(this.scrollParent.scrollLeft+=this.scrollSpeed.x,this.scrollParent.scrollTop+=this.scrollSpeed.y):this.view.win.scrollBy(this.scrollSpeed.x,this.scrollSpeed.y),this.dragging===!1&&this.select(this.lastEvent)}skipAtoms(e){let t=null;for(let i=0;i<e.ranges.length;i++){let s=e.ranges[i],r=null;if(s.empty){let o=Wn(this.atoms,s.from,0);o!=s.from&&(r=y.cursor(o,-1))}else{let o=Wn(this.atoms,s.from,-1),l=Wn(this.atoms,s.to,1);(o!=s.from||l!=s.to)&&(r=y.range(s.from==s.anchor?o:l,s.from==s.head?o:l))}r&&(t||(t=e.ranges.slice()),t[i]=r)}return t?y.create(t,e.mainIndex):e}select(e){let{view:t}=this,i=this.skipAtoms(this.style.get(e,this.extend,this.multiple));(this.mustSelect||!i.eq(t.state.selection,this.dragging===!1))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(e){this.style.update(e)&&setTimeout(()=>this.select(this.lastEvent),20)}}function Vd(n,e){let t=n.state.facet(Ah);return t.length?t[0](e):T.mac?e.metaKey:e.ctrlKey}function Bd(n,e){let t=n.state.facet(Mh);return t.length?t[0](e):T.mac?!e.altKey:!e.ctrlKey}function Ud(n,e){let{main:t}=n.state.selection;if(t.empty)return!1;let i=Ln(n.root);if(!i||i.rangeCount==0)return!0;let s=i.getRangeAt(0).getClientRects();for(let r=0;r<s.length;r++){let o=s[r];if(o.left<=e.clientX&&o.right>=e.clientX&&o.top<=e.clientY&&o.bottom>=e.clientY)return!0}return!1}function Ld(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target,i;t!=n.contentDOM;t=t.parentNode)if(!t||t.nodeType==11||(i=L.get(t))&&i.ignoreEvent(e))return!1;return!0}const Ee=Object.create(null),De=Object.create(null),Fh=T.ie&&T.ie_version<15||T.ios&&T.webkit_version<604;function _d(n){let e=n.dom.parentNode;if(!e)return;let t=e.appendChild(document.createElement("textarea"));t.style.cssText="position: fixed; left: -10000px; top: 10px",t.focus(),setTimeout(()=>{n.focus(),t.remove(),Hh(n,t.value)},50)}function Hh(n,e){let{state:t}=n,i,s=1,r=t.toText(e),o=r.lines==t.selection.ranges.length;if(Rr!=null&&t.selection.ranges.every(a=>a.empty)&&Rr==r.toString()){let a=-1;i=t.changeByRange(h=>{let c=t.doc.lineAt(h.from);if(c.from==a)return{range:h};a=c.from;let f=t.toText((o?r.line(s++).text:e)+t.lineBreak);return{changes:{from:c.from,insert:f},range:y.cursor(h.from+f.length)}})}else o?i=t.changeByRange(a=>{let h=r.line(s++);return{changes:{from:a.from,to:a.to,insert:h.text},range:y.cursor(a.from+h.length)}}):i=t.replaceSelection(r);n.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}De.scroll=n=>{n.inputState.lastScrollTop=n.scrollDOM.scrollTop,n.inputState.lastScrollLeft=n.scrollDOM.scrollLeft};Ee.keydown=(n,e)=>(n.inputState.setSelectionOrigin("select"),e.keyCode==27&&(n.inputState.lastEscPress=Date.now()),!1);De.touchstart=(n,e)=>{n.inputState.lastTouchTime=Date.now(),n.inputState.setSelectionOrigin("select.pointer")};De.touchmove=n=>{n.inputState.setSelectionOrigin("select.pointer")};Ee.mousedown=(n,e)=>{if(n.observer.flush(),n.inputState.lastTouchTime>Date.now()-2e3)return!1;let t=null;for(let i of n.state.facet(Wh))if(t=i(n,e),t)break;if(!t&&e.button==0&&(t=Gd(n,e)),t){let i=!n.hasFocus;n.inputState.startMouseSelection(new Ed(n,e,t,i)),i&&n.observer.ignore(()=>dh(n.contentDOM));let s=n.inputState.mouseSelection;if(s)return s.start(e),s.dragging===!1}return!1};function Ol(n,e,t,i){if(i==1)return y.cursor(e,t);if(i==2)return Cd(n.state,e,t);{let s=ie.find(n.docView,e),r=n.state.doc.lineAt(s?s.posAtEnd:e),o=s?s.posAtStart:r.from,l=s?s.posAtEnd:r.to;return l<n.state.doc.length&&l==r.to&&l++,y.range(o,l)}}let Kh=(n,e)=>n>=e.top&&n<=e.bottom,pl=(n,e,t)=>Kh(e,t)&&n>=t.left&&n<=t.right;function Nd(n,e,t,i){let s=ie.find(n.docView,e);if(!s)return 1;let r=e-s.posAtStart;if(r==0)return 1;if(r==s.length)return-1;let o=s.coordsAt(r,-1);if(o&&pl(t,i,o))return-1;let l=s.coordsAt(r,1);return l&&pl(t,i,l)?1:o&&Kh(i,o)?-1:1}function ml(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:t,bias:Nd(n,t,e.clientX,e.clientY)}}const Id=T.ie&&T.ie_version<=11;let gl=null,bl=0,yl=0;function Jh(n){if(!Id)return n.detail;let e=gl,t=yl;return gl=n,yl=Date.now(),bl=!e||t>Date.now()-400&&Math.abs(e.clientX-n.clientX)<2&&Math.abs(e.clientY-n.clientY)<2?(bl+1)%3:1}function Gd(n,e){let t=ml(n,e),i=Jh(e),s=n.state.selection;return{update(r){r.docChanged&&(t.pos=r.changes.mapPos(t.pos),s=s.map(r.changes))},get(r,o,l){let a=ml(n,r),h,c=Ol(n,a.pos,a.bias,i);if(t.pos!=a.pos&&!o){let f=Ol(n,t.pos,t.bias,i),u=Math.min(f.from,c.from),d=Math.max(f.to,c.to);c=u<c.from?y.range(u,d):y.range(d,u)}return o?s.replaceRange(s.main.extend(c.from,c.to)):l&&i==1&&s.ranges.length>1&&(h=Fd(s,a.pos))?h:l?s.addRange(c):y.create([c])}}}function Fd(n,e){for(let t=0;t<n.ranges.length;t++){let{from:i,to:s}=n.ranges[t];if(i<=e&&s>=e)return y.create(n.ranges.slice(0,t).concat(n.ranges.slice(t+1)),n.mainIndex==t?0:n.mainIndex-(n.mainIndex>t?1:0))}return null}Ee.dragstart=(n,e)=>{let{selection:{main:t}}=n.state;if(e.target.draggable){let s=n.docView.nearest(e.target);if(s&&s.isWidget){let r=s.posAtStart,o=r+s.length;(r>=t.to||o<=t.from)&&(t=y.range(r,o))}}let{inputState:i}=n;return i.mouseSelection&&(i.mouseSelection.dragging=!0),i.draggedContent=t,e.dataTransfer&&(e.dataTransfer.setData("Text",n.state.sliceDoc(t.from,t.to)),e.dataTransfer.effectAllowed="copyMove"),!1};Ee.dragend=n=>(n.inputState.draggedContent=null,!1);function xl(n,e,t,i){if(!t)return;let s=n.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:r}=n.inputState,o=i&&r&&Bd(n,e)?{from:r.from,to:r.to}:null,l={from:s,insert:t},a=n.state.changes(o?[o,l]:l);n.focus(),n.dispatch({changes:a,selection:{anchor:a.mapPos(s,-1),head:a.mapPos(s,1)},userEvent:o?"move.drop":"input.drop"}),n.inputState.draggedContent=null}Ee.drop=(n,e)=>{if(!e.dataTransfer)return!1;if(n.state.readOnly)return!0;let t=e.dataTransfer.files;if(t&&t.length){let i=Array(t.length),s=0,r=()=>{++s==t.length&&xl(n,e,i.filter(o=>o!=null).join(n.state.lineBreak),!1)};for(let o=0;o<t.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r()},l.readAsText(t[o])}return!0}else{let i=e.dataTransfer.getData("Text");if(i)return xl(n,e,i,!0),!0}return!1};Ee.paste=(n,e)=>{if(n.state.readOnly)return!0;n.observer.flush();let t=Fh?null:e.clipboardData;return t?(Hh(n,t.getData("text/plain")||t.getData("text/uri-list")),!0):(_d(n),!1)};function Hd(n,e){let t=n.dom.parentNode;if(!t)return;let i=t.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=e,i.focus(),i.selectionEnd=e.length,i.selectionStart=0,setTimeout(()=>{i.remove(),n.focus()},50)}function Kd(n){let e=[],t=[],i=!1;for(let s of n.selection.ranges)s.empty||(e.push(n.sliceDoc(s.from,s.to)),t.push(s));if(!e.length){let s=-1;for(let{from:r}of n.selection.ranges){let o=n.doc.lineAt(r);o.number>s&&(e.push(o.text),t.push({from:o.from,to:Math.min(n.doc.length,o.to+1)})),s=o.number}i=!0}return{text:e.join(n.lineBreak),ranges:t,linewise:i}}let Rr=null;Ee.copy=Ee.cut=(n,e)=>{let{text:t,ranges:i,linewise:s}=Kd(n.state);if(!t&&!s)return!1;Rr=s?t:null,e.type=="cut"&&!n.state.readOnly&&n.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"});let r=Fh?null:e.clipboardData;return r?(r.clearData(),r.setData("text/plain",t),!0):(Hd(n,t),!1)};const ec=ht.define();function tc(n,e){let t=[];for(let i of n.facet(Dh)){let s=i(n,e);s&&t.push(s)}return t?n.update({effects:t,annotations:ec.of(!0)}):null}function ic(n){setTimeout(()=>{let e=n.hasFocus;if(e!=n.inputState.notifiedFocused){let t=tc(n.state,e);t?n.dispatch(t):n.update([])}},10)}De.focus=n=>{n.inputState.lastFocusTime=Date.now(),!n.scrollDOM.scrollTop&&(n.inputState.lastScrollTop||n.inputState.lastScrollLeft)&&(n.scrollDOM.scrollTop=n.inputState.lastScrollTop,n.scrollDOM.scrollLeft=n.inputState.lastScrollLeft),ic(n)};De.blur=n=>{n.observer.clearSelectionRange(),ic(n)};De.compositionstart=De.compositionupdate=n=>{n.inputState.compositionFirstChange==null&&(n.inputState.compositionFirstChange=!0),n.inputState.composing<0&&(n.inputState.composing=0)};De.compositionend=n=>{n.inputState.composing=-1,n.inputState.compositionEndedAt=Date.now(),n.inputState.compositionPendingKey=!0,n.inputState.compositionPendingChange=n.observer.pendingRecords().length>0,n.inputState.compositionFirstChange=null,T.chrome&&T.android?n.observer.flushSoon():n.inputState.compositionPendingChange?Promise.resolve().then(()=>n.observer.flush()):setTimeout(()=>{n.inputState.composing<0&&n.docView.hasComposition&&n.update([])},50)};De.contextmenu=n=>{n.inputState.lastContextMenu=Date.now()};Ee.beforeinput=(n,e)=>{var t;let i;if(T.chrome&&T.android&&(i=Ih.find(s=>s.inputType==e.inputType))&&(n.observer.delayAndroidKey(i.key,i.keyCode),i.key=="Backspace"||i.key=="Delete")){let s=((t=window.visualViewport)===null||t===void 0?void 0:t.height)||0;setTimeout(()=>{var r;(((r=window.visualViewport)===null||r===void 0?void 0:r.height)||0)>s+10&&n.hasFocus&&(n.contentDOM.blur(),n.focus())},100)}return T.ios&&e.inputType=="deleteContentForward"&&n.observer.flushSoon(),T.safari&&e.inputType=="insertText"&&n.inputState.composing>=0&&setTimeout(()=>De.compositionend(n,e),20),!1};const Sl=new Set;function Jd(n){Sl.has(n)||(Sl.add(n),n.addEventListener("copy",()=>{}),n.addEventListener("cut",()=>{}))}const wl=["pre-wrap","normal","pre-line","break-spaces"];class eO{constructor(e){this.lineWrapping=e,this.doc=B.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30,this.heightChanged=!1}heightForGap(e,t){let i=this.doc.lineAt(t).number-this.doc.lineAt(e).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((t-e-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(e){return this.lineWrapping?(1+Math.max(0,Math.ceil((e-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(e){return this.doc=e,this}mustRefreshForWrapping(e){return wl.indexOf(e)>-1!=this.lineWrapping}mustRefreshForHeights(e){let t=!1;for(let i=0;i<e.length;i++){let s=e[i];s<0?i++:this.heightSamples[Math.floor(s*10)]||(t=!0,this.heightSamples[Math.floor(s*10)]=!0)}return t}refresh(e,t,i,s,r,o){let l=wl.indexOf(e)>-1,a=Math.round(t)!=Math.round(this.lineHeight)||this.lineWrapping!=l;if(this.lineWrapping=l,this.lineHeight=t,this.charWidth=i,this.textHeight=s,this.lineLength=r,a){this.heightSamples={};for(let h=0;h<o.length;h++){let c=o[h];c<0?h++:this.heightSamples[Math.floor(c*10)]=!0}}return a}}class tO{constructor(e,t){this.from=e,this.heights=t,this.index=0}get more(){return this.index<this.heights.length}}class He{constructor(e,t,i,s,r){this.from=e,this.length=t,this.top=i,this.height=s,this._content=r}get type(){return typeof this._content=="number"?be.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof wt?this._content.widget:null}get widgetLineBreaks(){return typeof this._content=="number"?this._content:0}join(e){let t=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(e._content)?e._content:[e]);return new He(this.from,this.length+e.length,this.top,this.height+e.height,t)}}var N=function(n){return n[n.ByPos=0]="ByPos",n[n.ByHeight=1]="ByHeight",n[n.ByPosNoHeight=2]="ByPosNoHeight",n}(N||(N={}));const Xn=.001;class ye{constructor(e,t,i=2){this.length=e,this.height=t,this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(e){this.flags=(e?2:0)|this.flags&-3}setHeight(e,t){this.height!=t&&(Math.abs(this.height-t)>Xn&&(e.heightChanged=!0),this.height=t)}replace(e,t,i){return ye.of(i)}decomposeLeft(e,t){t.push(this)}decomposeRight(e,t){t.push(this)}applyChanges(e,t,i,s){let r=this,o=i.doc;for(let l=s.length-1;l>=0;l--){let{fromA:a,toA:h,fromB:c,toB:f}=s[l],u=r.lineAt(a,N.ByPosNoHeight,i.setDoc(t),0,0),d=u.to>=h?u:r.lineAt(h,N.ByPosNoHeight,i,0,0);for(f+=d.to-h,h=d.to;l>0&&u.from<=s[l-1].toA;)a=s[l-1].fromA,c=s[l-1].fromB,l--,a<u.from&&(u=r.lineAt(a,N.ByPosNoHeight,i,0,0));c+=u.from-a,a=u.from;let O=fo.build(i.setDoc(o),e,c,f);r=r.replace(a,h,O)}return r.updateHeight(i,0)}static empty(){return new Te(0,0)}static of(e){if(e.length==1)return e[0];let t=0,i=e.length,s=0,r=0;for(;;)if(t==i)if(s>r*2){let l=e[t-1];l.break?e.splice(--t,1,l.left,null,l.right):e.splice(--t,1,l.left,l.right),i+=1+l.break,s-=l.size}else if(r>s*2){let l=e[i];l.break?e.splice(i,1,l.left,null,l.right):e.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size}else break;else if(s<r){let l=e[t++];l&&(s+=l.size)}else{let l=e[--i];l&&(r+=l.size)}let o=0;return e[t-1]==null?(o=1,t--):e[t]==null&&(o=1,i++),new iO(ye.of(e.slice(0,t)),o,ye.of(e.slice(i)))}}ye.prototype.size=1;class nc extends ye{constructor(e,t,i){super(e,t),this.deco=i}blockAt(e,t,i,s){return new He(s,this.length,i,this.height,this.deco||0)}lineAt(e,t,i,s,r){return this.blockAt(0,i,s,r)}forEachLine(e,t,i,s,r,o){e<=r+this.length&&t>=r&&o(this.blockAt(0,i,s,r))}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more&&this.setHeight(e,s.heights[s.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Te extends nc{constructor(e,t){super(e,t,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(e,t,i,s){return new He(s,this.length,i,this.height,this.breaks)}replace(e,t,i){let s=i[0];return i.length==1&&(s instanceof Te||s instanceof le&&s.flags&4)&&Math.abs(this.length-s.length)<10?(s instanceof le?s=new Te(s.length,this.height):s.height=this.height,this.outdated||(s.outdated=!1),s):ye.of(i)}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more?this.setHeight(e,s.heights[s.index++]):(i||this.outdated)&&this.setHeight(e,Math.max(this.widgetHeight,e.heightForLine(this.length-this.collapsed))+this.breaks*e.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class le extends ye{constructor(e){super(e,0)}heightMetrics(e,t){let i=e.doc.lineAt(t).number,s=e.doc.lineAt(t+this.length).number,r=s-i+1,o,l=0;if(e.lineWrapping){let a=Math.min(this.height,e.lineHeight*r);o=a/r,this.length>r+1&&(l=(this.height-a)/(this.length-r-1))}else o=this.height/r;return{firstLine:i,lastLine:s,perLine:o,perChar:l}}blockAt(e,t,i,s){let{firstLine:r,lastLine:o,perLine:l,perChar:a}=this.heightMetrics(t,s);if(t.lineWrapping){let h=s+(e<t.lineHeight?0:Math.round(Math.max(0,Math.min(1,(e-i)/this.height))*this.length)),c=t.doc.lineAt(h),f=l+c.length*a,u=Math.max(i,e-f/2);return new He(c.from,c.length,u,f,0)}else{let h=Math.max(0,Math.min(o-r,Math.floor((e-i)/l))),{from:c,length:f}=t.doc.line(r+h);return new He(c,f,i+l*h,l,0)}}lineAt(e,t,i,s,r){if(t==N.ByHeight)return this.blockAt(e,i,s,r);if(t==N.ByPosNoHeight){let{from:d,to:O}=i.doc.lineAt(e);return new He(d,O-d,0,0,0)}let{firstLine:o,perLine:l,perChar:a}=this.heightMetrics(i,r),h=i.doc.lineAt(e),c=l+h.length*a,f=h.number-o,u=s+l*f+a*(h.from-r-f);return new He(h.from,h.length,Math.max(s,Math.min(u,s+this.height-c)),c,0)}forEachLine(e,t,i,s,r,o){e=Math.max(e,r),t=Math.min(t,r+this.length);let{firstLine:l,perLine:a,perChar:h}=this.heightMetrics(i,r);for(let c=e,f=s;c<=t;){let u=i.doc.lineAt(c);if(c==e){let O=u.number-l;f+=a*O+h*(e-r-O)}let d=a+h*u.length;o(new He(u.from,u.length,f,d,0)),f+=d,c=u.to+1}}replace(e,t,i){let s=this.length-t;if(s>0){let r=i[i.length-1];r instanceof le?i[i.length-1]=new le(r.length+s):i.push(null,new le(s-1))}if(e>0){let r=i[0];r instanceof le?i[0]=new le(e+r.length):i.unshift(new le(e-1),null)}return ye.of(i)}decomposeLeft(e,t){t.push(new le(e-1),null)}decomposeRight(e,t){t.push(null,new le(this.length-e-1))}updateHeight(e,t=0,i=!1,s){let r=t+this.length;if(s&&s.from<=t+this.length&&s.more){let o=[],l=Math.max(t,s.from),a=-1;for(s.from>t&&o.push(new le(s.from-t-1).updateHeight(e,t));l<=r&&s.more;){let c=e.doc.lineAt(l).length;o.length&&o.push(null);let f=s.heights[s.index++];a==-1?a=f:Math.abs(f-a)>=Xn&&(a=-2);let u=new Te(c,f);u.outdated=!1,o.push(u),l+=c+1}l<=r&&o.push(null,new le(r-l).updateHeight(e,l));let h=ye.of(o);return(a<0||Math.abs(h.height-this.height)>=Xn||Math.abs(a-this.heightMetrics(e,t).perLine)>=Xn)&&(e.heightChanged=!0),h}else(i||this.outdated)&&(this.setHeight(e,e.heightForGap(t,t+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class iO extends ye{constructor(e,t,i){super(e.length+t+i.length,e.height+i.height,t|(e.outdated||i.outdated?2:0)),this.left=e,this.right=i,this.size=e.size+i.size}get break(){return this.flags&1}blockAt(e,t,i,s){let r=i+this.left.height;return e<r?this.left.blockAt(e,t,i,s):this.right.blockAt(e,t,r,s+this.left.length+this.break)}lineAt(e,t,i,s,r){let o=s+this.left.height,l=r+this.left.length+this.break,a=t==N.ByHeight?e<o:e<l,h=a?this.left.lineAt(e,t,i,s,r):this.right.lineAt(e,t,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let c=t==N.ByPosNoHeight?N.ByPosNoHeight:N.ByPos;return a?h.join(this.right.lineAt(l,c,i,o,l)):this.left.lineAt(l,c,i,s,r).join(h)}forEachLine(e,t,i,s,r,o){let l=s+this.left.height,a=r+this.left.length+this.break;if(this.break)e<a&&this.left.forEachLine(e,t,i,s,r,o),t>=a&&this.right.forEachLine(e,t,i,l,a,o);else{let h=this.lineAt(a,N.ByPos,i,s,r);e<h.from&&this.left.forEachLine(e,h.from-1,i,s,r,o),h.to>=e&&h.from<=t&&o(h),t>h.to&&this.right.forEachLine(h.to+1,t,i,l,a,o)}}replace(e,t,i){let s=this.left.length+this.break;if(t<s)return this.balanced(this.left.replace(e,t,i),this.right);if(e>this.left.length)return this.balanced(this.left,this.right.replace(e-s,t-s,i));let r=[];e>0&&this.decomposeLeft(e,r);let o=r.length;for(let l of i)r.push(l);if(e>0&&Ql(r,o-1),t<this.length){let l=r.length;this.decomposeRight(t,r),Ql(r,l)}return ye.of(r)}decomposeLeft(e,t){let i=this.left.length;if(e<=i)return this.left.decomposeLeft(e,t);t.push(this.left),this.break&&(i++,e>=i&&t.push(null)),e>i&&this.right.decomposeLeft(e-i,t)}decomposeRight(e,t){let i=this.left.length,s=i+this.break;if(e>=s)return this.right.decomposeRight(e-s,t);e<i&&this.left.decomposeRight(e,t),this.break&&e<s&&t.push(null),t.push(this.right)}balanced(e,t){return e.size>2*t.size||t.size>2*e.size?ye.of(this.break?[e,null,t]:[e,t]):(this.left=e,this.right=t,this.height=e.height+t.height,this.outdated=e.outdated||t.outdated,this.size=e.size+t.size,this.length=e.length+this.break+t.length,this)}updateHeight(e,t=0,i=!1,s){let{left:r,right:o}=this,l=t+r.length+this.break,a=null;return s&&s.from<=t+r.length&&s.more?a=r=r.updateHeight(e,t,i,s):r.updateHeight(e,t,i),s&&s.from<=l+o.length&&s.more?a=o=o.updateHeight(e,l,i,s):o.updateHeight(e,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function Ql(n,e){let t,i;n[e]==null&&(t=n[e-1])instanceof le&&(i=n[e+1])instanceof le&&n.splice(e-1,3,new le(t.length+1+i.length))}const nO=5;class fo{constructor(e,t){this.pos=e,this.oracle=t,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=e}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(e,t){if(this.lineStart>-1){let i=Math.min(t,this.lineEnd),s=this.nodes[this.nodes.length-1];s instanceof Te?s.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new Te(i-this.pos,-1)),this.writtenTo=i,t>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=t}point(e,t,i){if(e<t||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0,r=i.widget?i.widget.lineBreaks:0;s<0&&(s=this.oracle.lineHeight);let o=t-e;i.block?this.addBlock(new nc(o,s,i)):(o||r||s>=nO)&&this.addLineDeco(s,r,o)}else t>e&&this.span(e,t);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:e,to:t}=this.oracle.doc.lineAt(this.pos);this.lineStart=e,this.lineEnd=t,this.writtenTo<e&&((this.writtenTo<e-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,e-1)),this.nodes.push(null)),this.pos>e&&this.nodes.push(new Te(this.pos-e,-1)),this.writtenTo=this.pos}blankContent(e,t){let i=new le(t-e);return this.oracle.doc.lineAt(e).to==t&&(i.flags|=4),i}ensureLine(){this.enterLine();let e=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(e instanceof Te)return e;let t=new Te(0,-1);return this.nodes.push(t),t}addBlock(e){this.enterLine();let t=e.deco;t&&t.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(e),this.writtenTo=this.pos=this.pos+e.length,t&&t.endSide>0&&(this.covering=e)}addLineDeco(e,t,i){let s=this.ensureLine();s.length+=i,s.collapsed+=i,s.widgetHeight=Math.max(s.widgetHeight,e),s.breaks+=t,this.writtenTo=this.pos=this.pos+i}finish(e){let t=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(t instanceof Te)&&!this.isCovered?this.nodes.push(new Te(0,-1)):(this.writtenTo<this.pos||t==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=e;for(let s of this.nodes)s instanceof Te&&s.updateHeight(this.oracle,i),i+=s?s.length:1;return this.nodes}static build(e,t,i,s){let r=new fo(i,e);return E.spans(t,i,s,r,0),r.finish(i)}}function sO(n,e,t){let i=new rO;return E.compare(n,e,t,i,0),i.changes}class rO{constructor(){this.changes=[]}compareRange(){}comparePoint(e,t,i,s){(e<t||i&&i.heightRelevant||s&&s.heightRelevant)&&kr(e,t,this.changes,5)}}function oO(n,e){let t=n.getBoundingClientRect(),i=n.ownerDocument,s=i.defaultView||window,r=Math.max(0,t.left),o=Math.min(s.innerWidth,t.right),l=Math.max(0,t.top),a=Math.min(s.innerHeight,t.bottom);for(let h=n.parentNode;h&&h!=i.body;)if(h.nodeType==1){let c=h,f=window.getComputedStyle(c);if((c.scrollHeight>c.clientHeight||c.scrollWidth>c.clientWidth)&&f.overflow!="visible"){let u=c.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=h==n.parentNode?u.bottom:Math.min(a,u.bottom)}h=f.position=="absolute"||f.position=="fixed"?c.offsetParent:c.parentNode}else if(h.nodeType==11)h=h.host;else break;return{left:r-t.left,right:Math.max(r,o)-t.left,top:l-(t.top+e),bottom:Math.max(l,a)-(t.top+e)}}function lO(n,e){let t=n.getBoundingClientRect();return{left:0,right:t.right-t.left,top:e,bottom:t.bottom-(t.top+e)}}class Xs{constructor(e,t,i){this.from=e,this.to=t,this.size=i}static same(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++){let s=e[i],r=t[i];if(s.from!=r.from||s.to!=r.to||s.size!=r.size)return!1}return!0}draw(e,t){return A.replace({widget:new aO(this.size*(t?e.scaleY:e.scaleX),t)}).range(this.from,this.to)}}class aO extends st{constructor(e,t){super(),this.size=e,this.vertical=t}eq(e){return e.size==this.size&&e.vertical==this.vertical}toDOM(){let e=document.createElement("div");return this.vertical?e.style.height=this.size+"px":(e.style.width=this.size+"px",e.style.height="2px",e.style.display="inline-block"),e}get estimatedHeight(){return this.vertical?this.size:-1}}class kl{constructor(e){this.state=e,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=Pl,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=I.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let t=e.facet(ho).some(i=>typeof i!="function"&&i.class=="cm-lineWrapping");this.heightOracle=new eO(t),this.stateDeco=e.facet(zi).filter(i=>typeof i!="function"),this.heightMap=ye.empty().applyChanges(this.stateDeco,B.empty,this.heightOracle.setDoc(e.doc),[new Ye(0,0,0,e.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=A.set(this.lineGaps.map(i=>i.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let e=[this.viewport],{main:t}=this.state.selection;for(let i=0;i<=1;i++){let s=i?t.head:t.anchor;if(!e.some(({from:r,to:o})=>s>=r&&s<=o)){let{from:r,to:o}=this.lineBlockAt(s);e.push(new pn(r,o))}}this.viewports=e.sort((i,s)=>i.from-s.from),this.scaler=this.heightMap.height<=7e6?Pl:new fO(this.heightOracle,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,e=>{this.viewportLines.push(this.scaler.scale==1?e:$i(e,this.scaler))})}update(e,t=null){this.state=e.state;let i=this.stateDeco;this.stateDeco=this.state.facet(zi).filter(c=>typeof c!="function");let s=e.changedRanges,r=Ye.extendWithRanges(s,sO(i,this.stateDeco,e?e.changes:ne.empty(this.state.doc.length))),o=this.heightMap.height,l=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);this.heightMap=this.heightMap.applyChanges(this.stateDeco,e.startState.doc,this.heightOracle.setDoc(this.state.doc),r),this.heightMap.height!=o&&(e.flags|=2),l?(this.scrollAnchorPos=e.changes.mapPos(l.from,-1),this.scrollAnchorHeight=l.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=r.length?this.mapViewport(this.viewport,e.changes):this.viewport;(t&&(t.range.head<a.from||t.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,t));let h=!e.changes.empty||e.flags&2||a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,this.updateForViewport(),h&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,e.changes))),e.flags|=this.computeVisibleRanges(),t&&(this.scrollTarget=t),!this.mustEnforceCursorAssoc&&e.selectionSet&&e.view.lineWrapping&&e.state.selection.main.empty&&e.state.selection.main.assoc&&!e.state.facet(zh)&&(this.mustEnforceCursorAssoc=!0)}measure(e){let t=e.contentDOM,i=window.getComputedStyle(t),s=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?I.RTL:I.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=t.getBoundingClientRect(),a=o||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height,this.mustMeasureContent=!1;let h=0,c=0;if(l.width&&l.height){let{scaleX:Q,scaleY:x}=uh(t,l);(Q>.005&&Math.abs(this.scaleX-Q)>.005||x>.005&&Math.abs(this.scaleY-x)>.005)&&(this.scaleX=Q,this.scaleY=x,h|=8,o=a=!0)}let f=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;(this.paddingTop!=f||this.paddingBottom!=u)&&(this.paddingTop=f,this.paddingBottom=u,h|=10),this.editorWidth!=e.scrollDOM.clientWidth&&(s.lineWrapping&&(a=!0),this.editorWidth=e.scrollDOM.clientWidth,h|=8);let d=e.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=ph(e.scrollDOM);let O=(this.printing?lO:oO)(t,this.paddingTop),m=O.top-this.pixelViewport.top,g=O.bottom-this.pixelViewport.bottom;this.pixelViewport=O;let b=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(b!=this.inView&&(this.inView=b,b&&(a=!0)),!this.inView&&!this.scrollTarget)return 0;let S=l.width;if((this.contentDOMWidth!=S||this.editorHeight!=e.scrollDOM.clientHeight)&&(this.contentDOMWidth=l.width,this.editorHeight=e.scrollDOM.clientHeight,h|=8),a){let Q=e.docView.measureVisibleLineHeights(this.viewport);if(s.mustRefreshForHeights(Q)&&(o=!0),o||s.lineWrapping&&Math.abs(S-this.contentDOMWidth)>s.charWidth){let{lineHeight:x,charWidth:Z,textHeight:$}=e.docView.measureTextSize();o=x>0&&s.refresh(r,x,Z,$,S/Z,Q),o&&(e.docView.minWidth=0,h|=8)}m>0&&g>0?c=Math.max(m,g):m<0&&g<0&&(c=Math.min(m,g)),s.heightChanged=!1;for(let x of this.viewports){let Z=x.from==this.viewport.from?Q:e.docView.measureVisibleLineHeights(x);this.heightMap=(o?ye.empty().applyChanges(this.stateDeco,B.empty,this.heightOracle,[new Ye(0,0,0,e.state.doc.length)]):this.heightMap).updateHeight(s,0,o,new tO(x.from,Z))}s.heightChanged&&(h|=2)}let P=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return P&&(this.viewport=this.getViewport(c,this.scrollTarget)),this.updateForViewport(),(h&2||P)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,e)),h|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,e.docView.enforceCursorAssoc()),h}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(e,t){let i=.5-Math.max(-.5,Math.min(.5,e/1e3/2)),s=this.heightMap,r=this.heightOracle,{visibleTop:o,visibleBottom:l}=this,a=new pn(s.lineAt(o-i*1e3,N.ByHeight,r,0,0).from,s.lineAt(l+(1-i)*1e3,N.ByHeight,r,0,0).to);if(t){let{head:h}=t.range;if(h<a.from||h>a.to){let c=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),f=s.lineAt(h,N.ByPos,r,0,0),u;t.y=="center"?u=(f.top+f.bottom)/2-c/2:t.y=="start"||t.y=="nearest"&&h<a.from?u=f.top:u=f.bottom-c,a=new pn(s.lineAt(u-1e3/2,N.ByHeight,r,0,0).from,s.lineAt(u+c+1e3/2,N.ByHeight,r,0,0).to)}}return a}mapViewport(e,t){let i=t.mapPos(e.from,-1),s=t.mapPos(e.to,1);return new pn(this.heightMap.lineAt(i,N.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(s,N.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:e,to:t},i=0){if(!this.inView)return!0;let{top:s}=this.heightMap.lineAt(e,N.ByPos,this.heightOracle,0,0),{bottom:r}=this.heightMap.lineAt(t,N.ByPos,this.heightOracle,0,0),{visibleTop:o,visibleBottom:l}=this;return(e==0||s<=o-Math.max(10,Math.min(-i,250)))&&(t==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&s>o-2*1e3&&r<l+2*1e3}mapLineGaps(e,t){if(!e.length||t.empty)return e;let i=[];for(let s of e)t.touchesRange(s.from,s.to)||i.push(new Xs(t.mapPos(s.from),t.mapPos(s.to),s.size));return i}ensureLineGaps(e,t){let i=this.heightOracle.lineWrapping,s=i?1e4:2e3,r=s>>1,o=s<<1;if(this.defaultTextDirection!=I.LTR&&!i)return[];let l=[],a=(h,c,f,u)=>{if(c-h<r)return;let d=this.state.selection.main,O=[d.from];d.empty||O.push(d.to);for(let g of O)if(g>h&&g<c){a(h,g-10,f,u),a(g+10,c,f,u);return}let m=cO(e,g=>g.from>=f.from&&g.to<=f.to&&Math.abs(g.from-h)<r&&Math.abs(g.to-c)<r&&!O.some(b=>g.from<b&&g.to>b));if(!m){if(c<f.to&&t&&i&&t.visibleRanges.some(g=>g.from<=c&&g.to>=c)){let g=t.moveToLineBoundary(y.cursor(c),!1,!0).head;g>h&&(c=g)}m=new Xs(h,c,this.gapSize(f,h,c,u))}l.push(m)};for(let h of this.viewportLines){if(h.length<o)continue;let c=hO(h.from,h.to,this.stateDeco);if(c.total<o)continue;let f=this.scrollTarget?this.scrollTarget.range.head:null,u,d;if(i){let O=s/this.heightOracle.lineLength*this.heightOracle.lineHeight,m,g;if(f!=null){let b=gn(c,f),S=((this.visibleBottom-this.visibleTop)/2+O)/h.height;m=b-S,g=b+S}else m=(this.visibleTop-h.top-O)/h.height,g=(this.visibleBottom-h.top+O)/h.height;u=mn(c,m),d=mn(c,g)}else{let O=c.total*this.heightOracle.charWidth,m=s*this.heightOracle.charWidth,g,b;if(f!=null){let S=gn(c,f),P=((this.pixelViewport.right-this.pixelViewport.left)/2+m)/O;g=S-P,b=S+P}else g=(this.pixelViewport.left-m)/O,b=(this.pixelViewport.right+m)/O;u=mn(c,g),d=mn(c,b)}u>h.from&&a(h.from,u,h,c),d<h.to&&a(d,h.to,h,c)}return l}gapSize(e,t,i,s){let r=gn(s,i)-gn(s,t);return this.heightOracle.lineWrapping?e.height*r:s.total*this.heightOracle.charWidth*r}updateLineGaps(e){Xs.same(e,this.lineGaps)||(this.lineGaps=e,this.lineGapDeco=A.set(e.map(t=>t.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let t=[];E.spans(e,this.viewport.from,this.viewport.to,{span(s,r){t.push({from:s,to:r})},point(){}},20);let i=t.length!=this.visibleRanges.length||this.visibleRanges.some((s,r)=>s.from!=t[r].from||s.to!=t[r].to);return this.visibleRanges=t,i?4:0}lineBlockAt(e){return e>=this.viewport.from&&e<=this.viewport.to&&this.viewportLines.find(t=>t.from<=e&&t.to>=e)||$i(this.heightMap.lineAt(e,N.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(e){return $i(this.heightMap.lineAt(this.scaler.fromDOM(e),N.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(e){let t=this.lineBlockAtHeight(e+8);return t.from>=this.viewport.from||this.viewportLines[0].top-e>200?t:this.viewportLines[0]}elementAtHeight(e){return $i(this.heightMap.blockAt(this.scaler.fromDOM(e),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class pn{constructor(e,t){this.from=e,this.to=t}}function hO(n,e,t){let i=[],s=n,r=0;return E.spans(t,n,e,{span(){},point(o,l){o>s&&(i.push({from:s,to:o}),r+=o-s),s=l}},20),s<e&&(i.push({from:s,to:e}),r+=e-s),{total:r,ranges:i}}function mn({total:n,ranges:e},t){if(t<=0)return e[0].from;if(t>=1)return e[e.length-1].to;let i=Math.floor(n*t);for(let s=0;;s++){let{from:r,to:o}=e[s],l=o-r;if(i<=l)return r+i;i-=l}}function gn(n,e){let t=0;for(let{from:i,to:s}of n.ranges){if(e<=s){t+=e-i;break}t+=s-i}return t/n.total}function cO(n,e){for(let t of n)if(e(t))return t}const Pl={toDOM(n){return n},fromDOM(n){return n},scale:1};class fO{constructor(e,t,i){let s=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=t.lineAt(l,N.ByPos,e,0,0).top,c=t.lineAt(a,N.ByPos,e,0,0).bottom;return s+=c-h,{from:l,to:a,top:h,bottom:c,domTop:0,domBottom:0}}),this.scale=(7e6-s)/(t.height-s);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom}toDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.top)return s+(e-i)*this.scale;if(e<=r.bottom)return r.domTop+(e-r.top);i=r.bottom,s=r.domBottom}}fromDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.domTop)return i+(e-s)/this.scale;if(e<=r.domBottom)return r.top+(e-r.domTop);i=r.bottom,s=r.domBottom}}}function $i(n,e){if(e.scale==1)return n;let t=e.toDOM(n.top),i=e.toDOM(n.bottom);return new He(n.from,n.length,t,i-t,Array.isArray(n._content)?n._content.map(s=>$i(s,e)):n._content)}const bn=C.define({combine:n=>n.join(" ")}),Ar=C.define({combine:n=>n.indexOf(!0)>-1}),Mr=xt.newName(),sc=xt.newName(),rc=xt.newName(),oc={"&light":"."+sc,"&dark":"."+rc};function Wr(n,e,t){return new xt(e,{finish(i){return/&/.test(i)?i.replace(/&\w*/,s=>{if(s=="&")return n;if(!t||!t[s])throw new RangeError(`Unsupported selector: ${s}`);return t[s]}):n+" "+i}})}const uO=Wr("."+Mr,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace:before":{content:"attr(data-display)",position:"absolute",pointerEvents:"none",color:"#888"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},oc),vi="￿";class dO{constructor(e,t){this.points=e,this.text="",this.lineSeparator=t.facet(D.lineSeparator)}append(e){this.text+=e}lineBreak(){this.text+=vi}readRange(e,t){if(!e)return this;let i=e.parentNode;for(let s=e;;){this.findPointBefore(i,s);let r=this.text.length;this.readNode(s);let o=s.nextSibling;if(o==t)break;let l=L.get(s),a=L.get(o);(l&&a?l.breakAfter:(l?l.breakAfter:_n(s))||_n(o)&&(s.nodeName!="BR"||s.cmIgnore)&&this.text.length>r)&&this.lineBreak(),s=o}return this.findPointBefore(i,t),this}readTextNode(e){let t=e.nodeValue;for(let i of this.points)i.node==e&&(i.pos=this.text.length+Math.min(i.offset,t.length));for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=t.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=s.exec(t))&&(r=l.index,o=l[0].length),this.append(t.slice(i,r<0?t.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==e&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o}}readNode(e){if(e.cmIgnore)return;let t=L.get(e),i=t&&t.overrideDOMText;if(i!=null){this.findPointInside(e,i.length);for(let s=i.iter();!s.next().done;)s.lineBreak?this.lineBreak():this.append(s.value)}else e.nodeType==3?this.readTextNode(e):e.nodeName=="BR"?e.nextSibling&&this.lineBreak():e.nodeType==1&&this.readRange(e.firstChild,null)}findPointBefore(e,t){for(let i of this.points)i.node==e&&e.childNodes[i.offset]==t&&(i.pos=this.text.length)}findPointInside(e,t){for(let i of this.points)(e.nodeType==3?i.node==e:e.contains(i.node))&&(i.pos=this.text.length+(OO(e,i.node,i.offset)?t:0))}}function OO(n,e,t){for(;;){if(!e||t<lt(e))return!1;if(e==n)return!0;t=Dt(e)+1,e=e.parentNode}}class $l{constructor(e,t){this.node=e,this.offset=t,this.pos=-1}}class pO{constructor(e,t,i,s){this.typeOver=s,this.bounds=null,this.text="";let{impreciseHead:r,impreciseAnchor:o}=e.docView;if(e.state.readOnly&&t>-1)this.newSel=null;else if(t>-1&&(this.bounds=e.docView.domBoundsAround(t,i,0))){let l=r||o?[]:bO(e),a=new dO(l,e.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=yO(l,this.bounds.from)}else{let l=e.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!br(e.contentDOM,l.focusNode)?e.state.selection.main.head:e.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!br(e.contentDOM,l.anchorNode)?e.state.selection.main.anchor:e.docView.posFromDOM(l.anchorNode,l.anchorOffset),c=e.viewport;if((T.ios||T.chrome)&&e.state.selection.main.empty&&a!=h&&(c.from>0||c.to<e.state.doc.length)){let f=Math.min(a,h),u=Math.max(a,h),d=c.from-f,O=c.to-u;(d==0||d==1||f==0)&&(O==0||O==-1||u==e.state.doc.length)&&(a=0,h=e.state.doc.length)}this.newSel=y.single(h,a)}}}function lc(n,e){let t,{newSel:i}=e,s=n.state.selection.main,r=n.inputState.lastKeyTime>Date.now()-100?n.inputState.lastKeyCode:-1;if(e.bounds){let{from:o,to:l}=e.bounds,a=s.from,h=null;(r===8||T.android&&e.text.length<l-o)&&(a=s.to,h="end");let c=gO(n.state.doc.sliceString(o,l,vi),e.text,a-o,h);c&&(T.chrome&&r==13&&c.toB==c.from+2&&e.text.slice(c.from,c.toB)==vi+vi&&c.toB--,t={from:o+c.from,to:o+c.toA,insert:B.of(e.text.slice(c.from,c.toB).split(vi))})}else i&&(!n.hasFocus&&n.state.facet(ms)||i.main.eq(s))&&(i=null);if(!t&&!i)return!1;if(!t&&e.typeOver&&!s.empty&&i&&i.main.empty?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,s.to)}:t&&t.from>=s.from&&t.to<=s.to&&(t.from!=s.from||t.to!=s.to)&&s.to-s.from-(t.to-t.from)<=4?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,t.from).append(t.insert).append(n.state.doc.slice(t.to,s.to))}:(T.mac||T.android)&&t&&t.from==t.to&&t.from==s.head-1&&/^\. ?$/.test(t.insert.toString())&&n.contentDOM.getAttribute("autocorrect")=="off"?(i&&t.insert.length==2&&(i=y.single(i.main.anchor-1,i.main.head-1)),t={from:s.from,to:s.to,insert:B.of([" "])}):T.chrome&&t&&t.from==t.to&&t.from==s.head&&t.insert.toString()==`
 `&&n.lineWrapping&&(i&&(i=y.single(i.main.anchor-1,i.main.head-1)),t={from:s.from,to:s.to,insert:B.of([" "])}),t){if(T.ios&&n.inputState.flushIOSKey(t)||T.android&&(t.to==s.to&&(t.from==s.from||t.from==s.from-1&&n.state.sliceDoc(t.from,s.from)==" ")&&t.insert.length==1&&t.insert.lines==2&&Ht(n.contentDOM,"Enter",13)||(t.from==s.from-1&&t.to==s.to&&t.insert.length==0||r==8&&t.insert.length<t.to-t.from&&t.to>s.head)&&Ht(n.contentDOM,"Backspace",8)||t.from==s.from&&t.to==s.to+1&&t.insert.length==0&&Ht(n.contentDOM,"Delete",46)))return!0;let o=t.insert.toString();n.inputState.composing>=0&&n.inputState.composing++;let l,a=()=>l||(l=mO(n,t,i));return n.state.facet(Yh).some(h=>h(n,t.from,t.to,o,a))||n.dispatch(a()),!0}else if(i&&!i.main.eq(s)){let o=!1,l="select";return n.inputState.lastSelectionTime>Date.now()-50&&(n.inputState.lastSelectionOrigin=="select"&&(o=!0),l=n.inputState.lastSelectionOrigin),n.dispatch({selection:i,scrollIntoView:o,userEvent:l}),!0}else return!1}function mO(n,e,t){let i,s=n.state,r=s.selection.main;if(e.from>=r.from&&e.to<=r.to&&e.to-e.from>=(r.to-r.from)/3&&(!t||t.main.empty&&t.main.from==e.from+e.insert.length)&&n.inputState.composing<0){let l=r.from<e.from?s.sliceDoc(r.from,e.from):"",a=r.to>e.to?s.sliceDoc(e.to,r.to):"";i=s.replaceSelection(n.state.toText(l+e.insert.sliceString(0,void 0,n.state.lineBreak)+a))}else{let l=s.changes(e),a=t&&t.main.to<=l.newLength?t.main:void 0;if(s.selection.ranges.length>1&&n.inputState.composing>=0&&e.to<=r.to&&e.to>=r.to-10){let h=n.state.sliceDoc(e.from,e.to),c,f=t&&_h(n,t.main.head);if(f){let O=e.insert.length-(e.to-e.from);c={from:f.from,to:f.to-O}}else c=n.state.doc.lineAt(r.head);let u=r.to-e.to,d=r.to-r.from;i=s.changeByRange(O=>{if(O.from==r.from&&O.to==r.to)return{changes:l,range:a||O.map(l)};let m=O.to-u,g=m-h.length;if(O.to-O.from!=d||n.state.sliceDoc(g,m)!=h||O.to>=c.from&&O.from<=c.to)return{range:O};let b=s.changes({from:g,to:m,insert:e.insert}),S=O.to-r.to;return{changes:b,range:a?y.range(Math.max(0,a.anchor+S),Math.max(0,a.head+S)):O.map(b)}})}else i={changes:l,selection:a&&s.selection.replaceRange(a)}}let o="input.type";return(n.composing||n.inputState.compositionPendingChange&&n.inputState.compositionEndedAt>Date.now()-50)&&(n.inputState.compositionPendingChange=!1,o+=".compose",n.inputState.compositionFirstChange&&(o+=".start",n.inputState.compositionFirstChange=!1)),s.update(i,{userEvent:o,scrollIntoView:!0})}function gO(n,e,t,i){let s=Math.min(n.length,e.length),r=0;for(;r<s&&n.charCodeAt(r)==e.charCodeAt(r);)r++;if(r==s&&n.length==e.length)return null;let o=n.length,l=e.length;for(;o>0&&l>0&&n.charCodeAt(o-1)==e.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));t-=o+a-r}if(o<r&&n.length<e.length){let a=t<=r&&t>=o?r-t:0;r-=a,l=r+(l-o),o=r}else if(l<r){let a=t<=r&&t>=l?r-t:0;r-=a,o=r+(o-l),l=r}return{from:r,toA:o,toB:l}}function bO(n){let e=[];if(n.root.activeElement!=n.contentDOM)return e;let{anchorNode:t,anchorOffset:i,focusNode:s,focusOffset:r}=n.observer.selectionRange;return t&&(e.push(new $l(t,i)),(s!=t||r!=i)&&e.push(new $l(s,r))),e}function yO(n,e){if(n.length==0)return null;let t=n[0].pos,i=n.length==2?n[1].pos:t;return t>-1&&i>-1?y.single(t+e,i+e):null}const xO={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},Ys=T.ie&&T.ie_version<=11;class SO{constructor(e){this.view=e,this.active=!1,this.selectionRange=new nd,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=e.contentDOM,this.observer=new MutationObserver(t=>{for(let i of t)this.queue.push(i);(T.ie&&T.ie_version<=11||T.ios&&e.composing)&&t.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),Ys&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),typeof ResizeObserver=="function"&&(this.resizeScroll=new ResizeObserver(()=>{var t;((t=this.view.docView)===null||t===void 0?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(e.scrollDOM)),this.addWindowListeners(this.win=e.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(e){this.view.inputState.runHandlers("scroll",e),this.intersecting&&this.view.measure()}onScroll(e){this.intersecting&&this.flush(!1),this.onScrollChanged(e)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(e){e.type=="change"&&!e.matches||(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(e){if(this.gapIntersection&&(e.length!=this.gaps.length||this.gaps.some((t,i)=>t!=e[i]))){this.gapIntersection.disconnect();for(let t of e)this.gapIntersection.observe(t);this.gaps=e}}onSelectionChange(e){let t=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(ms)?i.root.activeElement!=this.dom:!Mn(i.dom,s))return;let r=s.anchorNode&&i.docView.nearest(s.anchorNode);if(r&&r.ignoreEvent(e)){t||(this.selectionChanged=!1);return}(T.ie&&T.ie_version<=11||T.android&&T.chrome)&&!i.state.selection.main.empty&&s.focusNode&&Ai(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:e}=this,t=Ln(e.root);if(!t)return!1;let i=T.safari&&e.root.nodeType==11&&Ju(this.dom.ownerDocument)==this.dom&&wO(this.view,t)||t;if(!i||this.selectionRange.eq(i))return!1;let s=Mn(this.dom,i);return s&&!this.selectionChanged&&e.inputState.lastFocusTime>Date.now()-200&&e.inputState.lastTouchTime<Date.now()-300&&rd(this.dom,i)?(this.view.inputState.lastFocusTime=0,e.docView.updateSelection(),!1):(this.selectionRange.setRange(i),s&&(this.selectionChanged=!0),!0)}setSelectionRange(e,t){this.selectionRange.set(e.node,e.offset,t.node,t.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let e=0,t=null;for(let i=this.dom;i;)if(i.nodeType==1)!t&&e<this.scrollTargets.length&&this.scrollTargets[e]==i?e++:t||(t=this.scrollTargets.slice(0,e)),t&&t.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(e<this.scrollTargets.length&&!t&&(t=this.scrollTargets.slice(0,e)),t){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=t)i.addEventListener("scroll",this.onScroll)}}ignore(e){if(!this.active)return e();try{return this.stop(),e()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,xO),Ys&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),Ys&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(e,t){var i;if(!this.delayedAndroidKey){let s=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=r.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&r.force&&Ht(this.dom,r.key,r.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(s)}(!this.delayedAndroidKey||e=="Enter")&&(this.delayedAndroidKey={key:e,keyCode:t,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}processRecords(){let e=this.pendingRecords();e.length&&(this.queue=[]);let t=-1,i=-1,s=!1;for(let r of e){let o=this.readMutation(r);o&&(o.typeOver&&(s=!0),t==-1?{from:t,to:i}=o:(t=Math.min(o.from,t),i=Math.max(o.to,i)))}return{from:t,to:i,typeOver:s}}readChange(){let{from:e,to:t,typeOver:i}=this.processRecords(),s=this.selectionChanged&&Mn(this.dom,this.selectionRange);if(e<0&&!s)return null;e>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let r=new pO(this.view,e,t,i);return this.view.docView.domChanged={newSel:r.newSel?r.newSel.main:null},r}flush(e=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;e&&this.readSelectionRange();let t=this.readChange();if(!t)return this.view.requestMeasure(),!1;let i=this.view.state,s=lc(this.view,t);return this.view.state==i&&this.view.update([]),s}readMutation(e){let t=this.view.docView.nearest(e.target);if(!t||t.ignoreMutation(e))return null;if(t.markDirty(e.type=="attributes"),e.type=="attributes"&&(t.flags|=4),e.type=="childList"){let i=vl(t,e.previousSibling||e.target.previousSibling,-1),s=vl(t,e.nextSibling||e.target.nextSibling,1);return{from:i?t.posAfter(i):t.posAtStart,to:s?t.posBefore(s):t.posAtEnd,typeOver:!1}}else return e.type=="characterData"?{from:t.posAtStart,to:t.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}:null}setWindow(e){e!=this.win&&(this.removeWindowListeners(this.win),this.win=e,this.addWindowListeners(this.win))}addWindowListeners(e){e.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener("change",this.onPrint):e.addEventListener("beforeprint",this.onPrint),e.addEventListener("scroll",this.onScroll),e.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(e){e.removeEventListener("scroll",this.onScroll),e.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener("change",this.onPrint):e.removeEventListener("beforeprint",this.onPrint),e.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var e,t,i;this.stop(),(e=this.intersection)===null||e===void 0||e.disconnect(),(t=this.gapIntersection)===null||t===void 0||t.disconnect(),(i=this.resizeScroll)===null||i===void 0||i.disconnect();for(let s of this.scrollTargets)s.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function vl(n,e,t){for(;e;){let i=L.get(e);if(i&&i.parent==n)return i;let s=e.parentNode;e=s!=n.dom?s:t>0?e.nextSibling:e.previousSibling}return null}function Zl(n,e){let t=e.startContainer,i=e.startOffset,s=e.endContainer,r=e.endOffset,o=n.docView.domAtPos(n.state.selection.main.anchor);return Ai(o.node,o.offset,s,r)&&([t,i,s,r]=[s,r,t,i]),{anchorNode:t,anchorOffset:i,focusNode:s,focusOffset:r}}function wO(n,e){if(e.getComposedRanges){let s=e.getComposedRanges(n.root)[0];if(s)return Zl(n,s)}let t=null;function i(s){s.preventDefault(),s.stopImmediatePropagation(),t=s.getTargetRanges()[0]}return n.contentDOM.addEventListener("beforeinput",i,!0),n.dom.ownerDocument.execCommand("indent"),n.contentDOM.removeEventListener("beforeinput",i,!0),t?Zl(n,t):null}class v{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(e={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),e.parent&&e.parent.appendChild(this.dom);let{dispatch:t}=e;this.dispatchTransactions=e.dispatchTransactions||t&&(i=>i.forEach(s=>t(s,this)))||(i=>this.update(i)),this.dispatch=this.dispatch.bind(this),this._root=e.root||sd(e.parent)||document,this.viewState=new kl(e.state||D.create(e)),e.scrollTo&&e.scrollTo.is(un)&&(this.viewState.scrollTarget=e.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(ki).map(i=>new As(i));for(let i of this.plugins)i.update(this);this.observer=new SO(this),this.inputState=new Dd(this),this.inputState.ensureHandlers(this.plugins),this.docView=new ol(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure()}dispatch(...e){let t=e.length==1&&e[0]instanceof K?e:e.length==1&&Array.isArray(e[0])?e[0]:[this.state.update(...e)];this.dispatchTransactions(t,this)}update(e){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let t=!1,i=!1,s,r=this.state;for(let u of e){if(u.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=u.state}if(this.destroyed){this.viewState.state=r;return}let o=this.hasFocus,l=0,a=null;e.some(u=>u.annotation(ec))?(this.inputState.notifiedFocused=o,l=1):o!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=o,a=tc(r,o),a||(l=1));let h=this.observer.delayedAndroidKey,c=null;if(h?(this.observer.clearDelayedAndroidKey(),c=this.observer.readChange(),(c&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(c=null)):this.observer.clear(),r.facet(D.phrases)!=this.state.facet(D.phrases))return this.setState(r);s=Nn.create(this,r,e),s.flags|=l;let f=this.viewState.scrollTarget;try{this.updateState=2;for(let u of e){if(f&&(f=f.map(u.changes)),u.scrollIntoView){let{main:d}=u.state.selection;f=new Kt(d.empty?d:y.cursor(d.head,d.head>d.anchor?-1:1))}for(let d of u.effects)d.is(un)&&(f=d.value.clip(this.state))}this.viewState.update(s,f),this.bidiCache=In.update(this.bidiCache,s.changes),s.empty||(this.updatePlugins(s),this.inputState.update(s)),t=this.docView.update(s),this.state.facet(Pi)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(e),this.docView.updateSelection(t,e.some(u=>u.isUserEvent("select.pointer")))}finally{this.updateState=0}if(s.startState.facet(bn)!=s.state.facet(bn)&&(this.viewState.mustMeasureContent=!0),(t||i||f||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),t&&this.docViewUpdate(),!s.empty)for(let u of this.state.facet(Zr))try{u(s)}catch(d){ge(this.state,d,"update listener")}(a||c)&&Promise.resolve().then(()=>{a&&this.state==a.startState&&this.dispatch(a),c&&!lc(this,c)&&h.force&&Ht(this.contentDOM,h.key,h.keyCode)})}setState(e){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=e;return}this.updateState=2;let t=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new kl(e),this.plugins=e.facet(ki).map(i=>new As(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView.destroy(),this.docView=new ol(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}t&&this.focus(),this.requestMeasure()}updatePlugins(e){let t=e.startState.facet(ki),i=e.state.facet(ki);if(t!=i){let s=[];for(let r of i){let o=t.indexOf(r);if(o<0)s.push(new As(r));else{let l=this.plugins[o];l.mustUpdate=e,s.push(l)}}for(let r of this.plugins)r.mustUpdate!=e&&r.destroy(this);this.plugins=s,this.pluginMap.clear()}else for(let s of this.plugins)s.mustUpdate=e;for(let s=0;s<this.plugins.length;s++)this.plugins[s].update(this);t!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let e of this.plugins){let t=e.value;if(t&&t.docViewUpdate)try{t.docViewUpdate(this)}catch(i){ge(this.state,i,"doc view update listener")}}}measure(e=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,e&&this.observer.forceFlush();let t=null,i=this.scrollDOM,s=i.scrollTop*this.scaleY,{scrollAnchorPos:r,scrollAnchorHeight:o}=this.viewState;Math.abs(s-this.viewState.scrollTop)>1&&(o=-1),this.viewState.scrollAnchorHeight=-1;try{for(let l=0;;l++){if(o<0)if(ph(i))r=-1,o=this.viewState.heightMap.height;else{let d=this.viewState.scrollAnchorAt(s);r=d.from,o=d.top}this.updateState=1;let a=this.viewState.measure(this);if(!a&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let h=[];a&4||([this.measureRequests,h]=[h,this.measureRequests]);let c=h.map(d=>{try{return d.read(this)}catch(O){return ge(this.state,O),Cl}}),f=Nn.create(this,this.state,[]),u=!1;f.flags|=a,t?t.flags|=a:t=f,this.updateState=2,f.empty||(this.updatePlugins(f),this.inputState.update(f),this.updateAttrs(),u=this.docView.update(f),u&&this.docViewUpdate());for(let d=0;d<h.length;d++)if(c[d]!=Cl)try{let O=h[d];O.write&&O.write(c[d],this)}catch(O){ge(this.state,O)}if(u&&this.docView.updateSelection(!0),!f.viewportChanged&&this.measureRequests.length==0){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,o=-1;continue}else{let O=(r<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(r).top)-o;if(O>1||O<-1){s=s+O,i.scrollTop=s/this.scaleY,o=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(t&&!t.empty)for(let l of this.state.facet(Zr))l(t)}get themeClasses(){return Mr+" "+(this.state.facet(Ar)?rc:sc)+" "+this.state.facet(bn)}updateAttrs(){let e=Tl(this,Eh,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),t={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(ms)?"true":"false",class:"cm-content",style:`${T.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(t["aria-readonly"]="true"),Tl(this,ho,t);let i=this.observer.ignore(()=>{let s=Qr(this.contentDOM,this.contentAttrs,t),r=Qr(this.dom,this.editorAttrs,e);return s||r});return this.editorAttrs=e,this.contentAttrs=t,i}showAnnouncements(e){let t=!0;for(let i of e)for(let s of i.effects)if(s.is(v.announce)){t&&(this.announceDOM.textContent=""),t=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=s.value}}mountStyles(){this.styleModules=this.state.facet(Pi);let e=this.state.facet(v.cspNonce);xt.mount(this.root,this.styleModules.concat(uO).reverse(),e?{nonce:e}:void 0)}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(e){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),e){if(this.measureRequests.indexOf(e)>-1)return;if(e.key!=null){for(let t=0;t<this.measureRequests.length;t++)if(this.measureRequests[t].key===e.key){this.measureRequests[t]=e;return}}this.measureRequests.push(e)}}plugin(e){let t=this.pluginMap.get(e);return(t===void 0||t&&t.spec!=e)&&this.pluginMap.set(e,t=this.plugins.find(i=>i.spec==e)||null),t&&t.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(e){return this.readMeasured(),this.viewState.elementAtHeight(e)}lineBlockAtHeight(e){return this.readMeasured(),this.viewState.lineBlockAtHeight(e)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(e){return this.viewState.lineBlockAt(e)}get contentHeight(){return this.viewState.contentHeight}moveByChar(e,t,i){return Ws(this,e,ul(this,e,t,i))}moveByGroup(e,t){return Ws(this,e,ul(this,e,t,i=>Xd(this,e.head,i)))}visualLineSide(e,t){let i=this.bidiSpans(e),s=this.textDirectionAt(e.from),r=i[t?i.length-1:0];return y.cursor(r.side(t,s)+e.from,r.forward(!t,s)?1:-1)}moveToLineBoundary(e,t,i=!0){return Wd(this,e,t,i)}moveVertically(e,t,i){return Ws(this,e,Yd(this,e,t,i))}domAtPos(e){return this.docView.domAtPos(e)}posAtDOM(e,t=0){return this.docView.posFromDOM(e,t)}posAtCoords(e,t=!0){return this.readMeasured(),Nh(this,e,t)}coordsAtPos(e,t=1){this.readMeasured();let i=this.docView.coordsAt(e,t);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(e),r=this.bidiSpans(s),o=r[pt.find(r,e-s.from,-1,t)];return Os(i,o.dir==I.LTR==t>0)}coordsForChar(e){return this.readMeasured(),this.docView.coordsForChar(e)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(e){return!this.state.facet(qh)||e<this.viewport.from||e>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(e))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(e){if(e.length>QO)return Th(e.length);let t=this.textDirectionAt(e.from),i;for(let r of this.bidiCache)if(r.from==e.from&&r.dir==t&&(r.fresh||Ch(r.isolates,i=rl(this,e))))return r.order;i||(i=rl(this,e));let s=bd(e.text,t,i);return this.bidiCache.push(new In(e.from,e.to,t,i,!0,s)),s}get hasFocus(){var e;return(this.dom.ownerDocument.hasFocus()||T.safari&&((e=this.inputState)===null||e===void 0?void 0:e.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{dh(this.contentDOM),this.docView.updateSelection()})}setRoot(e){this._root!=e&&(this._root=e,this.observer.setWindow((e.nodeType==9?e:e.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let e of this.plugins)e.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(e,t={}){return un.of(new Kt(typeof e=="number"?y.cursor(e):e,t.y,t.x,t.yMargin,t.xMargin))}scrollSnapshot(){let{scrollTop:e,scrollLeft:t}=this.scrollDOM,i=this.viewState.scrollAnchorAt(e);return un.of(new Kt(y.cursor(i.from),"start","start",i.top-e,t,!0))}static domEventHandlers(e){return F.define(()=>({}),{eventHandlers:e})}static domEventObservers(e){return F.define(()=>({}),{eventObservers:e})}static theme(e,t){let i=xt.newName(),s=[bn.of(i),Pi.of(Wr(`.${i}`,e))];return t&&t.dark&&s.push(Ar.of(!0)),s}static baseTheme(e){return Pt.lowest(Pi.of(Wr("."+Mr,e,oc)))}static findFromDOM(e){var t;let i=e.querySelector(".cm-content"),s=i&&L.get(i)||L.get(e);return((t=s==null?void 0:s.rootView)===null||t===void 0?void 0:t.view)||null}}v.styleModule=Pi;v.inputHandler=Yh;v.scrollHandler=jh;v.focusChangeEffect=Dh;v.perLineTextDirection=qh;v.exceptionSink=Xh;v.updateListener=Zr;v.editable=ms;v.mouseSelectionStyle=Wh;v.dragMovesSelection=Mh;v.clickAddsSelectionRange=Ah;v.decorations=zi;v.outerDecorations=Vh;v.atomicRanges=co;v.bidiIsolatedRanges=Bh;v.scrollMargins=Uh;v.darkTheme=Ar;v.cspNonce=C.define({combine:n=>n.length?n[0]:""});v.contentAttributes=ho;v.editorAttributes=Eh;v.lineWrapping=v.contentAttributes.of({class:"cm-lineWrapping"});v.announce=M.define();const QO=4096,Cl={};class In{constructor(e,t,i,s,r,o){this.from=e,this.to=t,this.dir=i,this.isolates=s,this.fresh=r,this.order=o}static update(e,t){if(t.empty&&!e.some(r=>r.fresh))return e;let i=[],s=e.length?e[e.length-1].dir:I.LTR;for(let r=Math.max(0,e.length-10);r<e.length;r++){let o=e[r];o.dir==s&&!t.touchesRange(o.from,o.to)&&i.push(new In(t.mapPos(o.from,1),t.mapPos(o.to,-1),o.dir,o.isolates,!1,o.order))}return i}}function Tl(n,e,t){for(let i=n.state.facet(e),s=i.length-1;s>=0;s--){let r=i[s],o=typeof r=="function"?r(n):r;o&&wr(o,t)}return t}const kO=T.mac?"mac":T.windows?"win":T.linux?"linux":"key";function PO(n,e){const t=n.split(/-(?!$)/);let i=t[t.length-1];i=="Space"&&(i=" ");let s,r,o,l;for(let a=0;a<t.length-1;++a){const h=t[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))s=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))e=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return s&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function yn(n,e,t){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t!==!1&&e.shiftKey&&(n="Shift-"+n),n}const $O=Pt.default(v.domEventHandlers({keydown(n,e){return hc(ac(e.state),n,e,"editor")}})),fi=C.define({enables:$O}),Rl=new WeakMap;function ac(n){let e=n.facet(fi),t=Rl.get(e);return t||Rl.set(e,t=CO(e.reduce((i,s)=>i.concat(s),[]))),t}function vO(n,e,t){return hc(ac(n.state),e,n,t)}let ut=null;const ZO=4e3;function CO(n,e=kO){let t=Object.create(null),i=Object.create(null),s=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h,c)=>{var f,u;let d=t[o]||(t[o]=Object.create(null)),O=l.split(/ (?!$)/).map(b=>PO(b,e));for(let b=1;b<O.length;b++){let S=O.slice(0,b).join(" ");s(S,!0),d[S]||(d[S]={preventDefault:!0,stopPropagation:!1,run:[P=>{let Q=ut={view:P,prefix:S,scope:o};return setTimeout(()=>{ut==Q&&(ut=null)},ZO),!0}]})}let m=O.join(" ");s(m,!1);let g=d[m]||(d[m]={preventDefault:!1,stopPropagation:!1,run:((u=(f=d._any)===null||f===void 0?void 0:f.run)===null||u===void 0?void 0:u.slice())||[]});a&&g.run.push(a),h&&(g.preventDefault=!0),c&&(g.stopPropagation=!0)};for(let o of n){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let c=t[h]||(t[h]=Object.create(null));c._any||(c._any={preventDefault:!1,stopPropagation:!1,run:[]});for(let f in c)c[f].run.push(o.any)}let a=o[e]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault,o.stopPropagation),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault,o.stopPropagation)}return t}function hc(n,e,t,i){let s=Ku(e),r=ae(s,0),o=We(r)==s.length&&s!=" ",l="",a=!1,h=!1,c=!1;ut&&ut.view==t&&ut.scope==i&&(l=ut.prefix+" ",Gh.indexOf(e.keyCode)<0&&(h=!0,ut=null));let f=new Set,u=g=>{if(g){for(let b of g.run)if(!f.has(b)&&(f.add(b),b(t,e)))return g.stopPropagation&&(c=!0),!0;g.preventDefault&&(g.stopPropagation&&(c=!0),h=!0)}return!1},d=n[i],O,m;return d&&(u(d[l+yn(s,e,!o)])?a=!0:o&&(e.altKey||e.metaKey||e.ctrlKey)&&!(T.windows&&e.ctrlKey&&e.altKey)&&(O=St[e.keyCode])&&O!=s?(u(d[l+yn(O,e,!0)])||e.shiftKey&&(m=qi[e.keyCode])!=s&&m!=O&&u(d[l+yn(m,e,!1)]))&&(a=!0):o&&e.shiftKey&&u(d[l+yn(s,e,!0)])&&(a=!0),!a&&u(d._any)&&(a=!0)),h&&(a=!0),a&&c&&e.stopPropagation(),a}class en{constructor(e,t,i,s,r){this.className=e,this.left=t,this.top=i,this.width=s,this.height=r}draw(){let e=document.createElement("div");return e.className=this.className,this.adjust(e),e}update(e,t){return t.className!=this.className?!1:(this.adjust(e),!0)}adjust(e){e.style.left=this.left+"px",e.style.top=this.top+"px",this.width!=null&&(e.style.width=this.width+"px"),e.style.height=this.height+"px"}eq(e){return this.left==e.left&&this.top==e.top&&this.width==e.width&&this.height==e.height&&this.className==e.className}static forRange(e,t,i){if(i.empty){let s=e.coordsAtPos(i.head,i.assoc||1);if(!s)return[];let r=cc(e);return[new en(t,s.left-r.left,s.top-r.top,null,s.bottom-s.top)]}else return TO(e,t,i)}}function cc(n){let e=n.scrollDOM.getBoundingClientRect();return{left:(n.textDirection==I.LTR?e.left:e.right-n.scrollDOM.clientWidth*n.scaleX)-n.scrollDOM.scrollLeft*n.scaleX,top:e.top-n.scrollDOM.scrollTop*n.scaleY}}function Al(n,e,t){let i=y.cursor(e);return{from:Math.max(t.from,n.moveToLineBoundary(i,!1,!0).from),to:Math.min(t.to,n.moveToLineBoundary(i,!0,!0).from),type:be.Text}}function TO(n,e,t){if(t.to<=n.viewport.from||t.from>=n.viewport.to)return[];let i=Math.max(t.from,n.viewport.from),s=Math.min(t.to,n.viewport.to),r=n.textDirection==I.LTR,o=n.contentDOM,l=o.getBoundingClientRect(),a=cc(n),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),f=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),d=Tr(n,i),O=Tr(n,s),m=d.type==be.Text?d:null,g=O.type==be.Text?O:null;if(m&&(n.lineWrapping||d.widgetLineBreaks)&&(m=Al(n,i,m)),g&&(n.lineWrapping||O.widgetLineBreaks)&&(g=Al(n,s,g)),m&&g&&m.from==g.from)return S(P(t.from,t.to,m));{let x=m?P(t.from,null,m):Q(d,!1),Z=g?P(null,t.to,g):Q(O,!0),$=[];return(m||d).to<(g||O).from-(m&&g?1:0)||d.widgetLineBreaks>1&&x.bottom+n.defaultLineHeight/2<Z.top?$.push(b(f,x.bottom,u,Z.top)):x.bottom<Z.top&&n.elementAtHeight((x.bottom+Z.top)/2).type==be.Text&&(x.bottom=Z.top=(x.bottom+Z.top)/2),S(x).concat($).concat(S(Z))}function b(x,Z,$,W){return new en(e,x-a.left,Z-a.top-.01,$-x,W-Z+.01)}function S({top:x,bottom:Z,horizontal:$}){let W=[];for(let z=0;z<$.length;z+=2)W.push(b($[z],x,$[z+1],Z));return W}function P(x,Z,$){let W=1e9,z=-1e9,j=[];function Y(R,V,oe,xe,ve){let Ae=n.coordsAtPos(R,R==$.to?-2:2),te=n.coordsAtPos(oe,oe==$.from?2:-2);!Ae||!te||(W=Math.min(Ae.top,te.top,W),z=Math.max(Ae.bottom,te.bottom,z),ve==I.LTR?j.push(r&&V?f:Ae.left,r&&xe?u:te.right):j.push(!r&&xe?f:te.left,!r&&V?u:Ae.right))}let X=x??$.from,w=Z??$.to;for(let R of n.visibleRanges)if(R.to>X&&R.from<w)for(let V=Math.max(R.from,X),oe=Math.min(R.to,w);;){let xe=n.state.doc.lineAt(V);for(let ve of n.bidiSpans(xe)){let Ae=ve.from+xe.from,te=ve.to+xe.from;if(Ae>=oe)break;te>V&&Y(Math.max(Ae,V),x==null&&Ae<=X,Math.min(te,oe),Z==null&&te>=w,ve.dir)}if(V=xe.to+1,V>=oe)break}return j.length==0&&Y(X,x==null,w,Z==null,n.textDirection),{top:W,bottom:z,horizontal:j}}function Q(x,Z){let $=l.top+(Z?x.top:x.bottom);return{top:$,bottom:$,horizontal:[]}}}function RO(n,e){return n.constructor==e.constructor&&n.eq(e)}class AO{constructor(e,t){this.view=e,this.layer=t,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=e.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),t.above&&this.dom.classList.add("cm-layer-above"),t.class&&this.dom.classList.add(t.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(e.state),e.requestMeasure(this.measureReq),t.mount&&t.mount(this.dom,e)}update(e){e.startState.facet(Yn)!=e.state.facet(Yn)&&this.setOrder(e.state),(this.layer.update(e,this.dom)||e.geometryChanged)&&(this.scale(),e.view.requestMeasure(this.measureReq))}docViewUpdate(e){this.layer.updateOnDocViewUpdate!==!1&&e.requestMeasure(this.measureReq)}setOrder(e){let t=0,i=e.facet(Yn);for(;t<i.length&&i[t]!=this.layer;)t++;this.dom.style.zIndex=String((this.layer.above?150:-1)-t)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:e,scaleY:t}=this.view;(e!=this.scaleX||t!=this.scaleY)&&(this.scaleX=e,this.scaleY=t,this.dom.style.transform=`scale(${1/e}, ${1/t})`)}draw(e){if(e.length!=this.drawn.length||e.some((t,i)=>!RO(t,this.drawn[i]))){let t=this.dom.firstChild,i=0;for(let s of e)s.update&&t&&s.constructor&&this.drawn[i].constructor&&s.update(t,this.drawn[i])?(t=t.nextSibling,i++):this.dom.insertBefore(s.draw(),t);for(;t;){let s=t.nextSibling;t.remove(),t=s}this.drawn=e}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const Yn=C.define();function fc(n){return[F.define(e=>new AO(e,n)),Yn.of(n)]}const uc=!T.ios,ji=C.define({combine(n){return Ve(n,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(e,t)=>Math.min(e,t),drawRangeCursor:(e,t)=>e||t})}});function dc(n={}){return[ji.of(n),MO,WO,XO,zh.of(!0)]}function Oc(n){return n.startState.facet(ji)!=n.state.facet(ji)}const MO=fc({above:!0,markers(n){let{state:e}=n,t=e.facet(ji),i=[];for(let s of e.selection.ranges){let r=s==e.selection.main;if(s.empty?!r||uc:t.drawRangeCursor){let o=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",l=s.empty?s:y.cursor(s.head,s.head>s.anchor?-1:1);for(let a of en.forRange(n,o,l))i.push(a)}}return i},update(n,e){n.transactions.some(i=>i.selection)&&(e.style.animationName=e.style.animationName=="cm-blink"?"cm-blink2":"cm-blink");let t=Oc(n);return t&&Ml(n.state,e),n.docChanged||n.selectionSet||t},mount(n,e){Ml(e.state,n)},class:"cm-cursorLayer"});function Ml(n,e){e.style.animationDuration=n.facet(ji).cursorBlinkRate+"ms"}const WO=fc({above:!1,markers(n){return n.state.selection.ranges.map(e=>e.empty?[]:en.forRange(n,"cm-selectionBackground",e)).reduce((e,t)=>e.concat(t))},update(n,e){return n.docChanged||n.selectionSet||n.viewportChanged||Oc(n)},class:"cm-selectionLayer"}),Xr={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};uc&&(Xr[".cm-line"].caretColor="transparent !important",Xr[".cm-content"]={caretColor:"transparent !important"});const XO=Pt.highest(v.theme(Xr)),pc=M.define({map(n,e){return n==null?null:e.mapPos(n)}}),Zi=ee.define({create(){return null},update(n,e){return n!=null&&(n=e.changes.mapPos(n)),e.effects.reduce((t,i)=>i.is(pc)?i.value:t,n)}}),YO=F.fromClass(class{constructor(n){this.view=n,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(n){var e;let t=n.state.field(Zi);t==null?this.cursor!=null&&((e=this.cursor)===null||e===void 0||e.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(n.startState.field(Zi)!=t||n.docChanged||n.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let{view:n}=this,e=n.state.field(Zi),t=e!=null&&n.coordsAtPos(e);if(!t)return null;let i=n.scrollDOM.getBoundingClientRect();return{left:t.left-i.left+n.scrollDOM.scrollLeft*n.scaleX,top:t.top-i.top+n.scrollDOM.scrollTop*n.scaleY,height:t.bottom-t.top}}drawCursor(n){if(this.cursor){let{scaleX:e,scaleY:t}=this.view;n?(this.cursor.style.left=n.left/e+"px",this.cursor.style.top=n.top/t+"px",this.cursor.style.height=n.height/t+"px"):this.cursor.style.left="-100000px"}}destroy(){this.cursor&&this.cursor.remove()}setDropPos(n){this.view.state.field(Zi)!=n&&this.view.dispatch({effects:pc.of(n)})}},{eventObservers:{dragover(n){this.setDropPos(this.view.posAtCoords({x:n.clientX,y:n.clientY}))},dragleave(n){(n.target==this.view.contentDOM||!this.view.contentDOM.contains(n.relatedTarget))&&this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function DO(){return[Zi,YO]}function Wl(n,e,t,i,s){e.lastIndex=0;for(let r=n.iterRange(t,i),o=t,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=e.exec(r.value);)s(o+l.index,l)}function qO(n,e){let t=n.visibleRanges;if(t.length==1&&t[0].from==n.viewport.from&&t[0].to==n.viewport.to)return t;let i=[];for(let{from:s,to:r}of t)s=Math.max(n.state.doc.lineAt(s).from,s-e),r=Math.min(n.state.doc.lineAt(r).to,r+e),i.length&&i[i.length-1].to>=s?i[i.length-1].to=r:i.push({from:s,to:r});return i}class zO{constructor(e){const{regexp:t,decoration:i,decorate:s,boundary:r,maxLength:o=1e3}=e;if(!t.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=t,s)this.addMatch=(l,a,h,c)=>s(c,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,c)=>{let f=i(l,a,h);f&&c(h,h+l[0].length,f)};else if(i)this.addMatch=(l,a,h,c)=>c(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o}createDeco(e){let t=new yt,i=t.add.bind(t);for(let{from:s,to:r}of qO(e,this.maxLength))Wl(e.state.doc,this.regexp,s,r,(o,l)=>this.addMatch(l,e,o,i));return t.finish()}updateDeco(e,t){let i=1e9,s=-1;return e.docChanged&&e.changes.iterChanges((r,o,l,a)=>{a>e.view.viewport.from&&l<e.view.viewport.to&&(i=Math.min(l,i),s=Math.max(a,s))}),e.viewportChanged||s-i>1e3?this.createDeco(e.view):s>-1?this.updateRange(e.view,t.map(e.changes),i,s):t}updateRange(e,t,i,s){for(let r of e.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,s);if(l>o){let a=e.state.doc.lineAt(o),h=a.to<l?e.state.doc.lineAt(l):a,c=Math.max(r.from,a.from),f=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){c=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){f=l;break}}let u=[],d,O=(m,g,b)=>u.push(b.range(m,g));if(a==h)for(this.regexp.lastIndex=c-a.from;(d=this.regexp.exec(a.text))&&d.index<f-a.from;)this.addMatch(d,e,d.index+a.from,O);else Wl(e.state.doc,this.regexp,c,f,(m,g)=>this.addMatch(g,e,m,O));t=t.update({filterFrom:c,filterTo:f,filter:(m,g)=>m<c||g>f,add:u})}}return t}}const Yr=/x/.unicode!=null?"gu":"g",jO=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,Yr),EO={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let Ds=null;function VO(){var n;if(Ds==null&&typeof document<"u"&&document.body){let e=document.body.style;Ds=((n=e.tabSize)!==null&&n!==void 0?n:e.MozTabSize)!=null}return Ds||!1}const Dn=C.define({combine(n){let e=Ve(n,{render:null,specialChars:jO,addSpecialChars:null});return(e.replaceTabs=!VO())&&(e.specialChars=new RegExp("	|"+e.specialChars.source,Yr)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,Yr)),e}});function mc(n={}){return[Dn.of(n),BO()]}let Xl=null;function BO(){return Xl||(Xl=F.fromClass(class{constructor(n){this.view=n,this.decorations=A.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(n.state.facet(Dn)),this.decorations=this.decorator.createDeco(n)}makeDecorator(n){return new zO({regexp:n.specialChars,decoration:(e,t,i)=>{let{doc:s}=t.state,r=ae(e[0],0);if(r==9){let o=s.lineAt(i),l=t.state.tabSize,a=ci(o.text,l,i-o.from);return A.replace({widget:new NO((l-a%l)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=A.replace({widget:new _O(n,r)}))},boundary:n.replaceTabs?void 0:/[^]/})}update(n){let e=n.state.facet(Dn);n.startState.facet(Dn)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(n.view)):this.decorations=this.decorator.updateDeco(n,this.decorations)}},{decorations:n=>n.decorations}))}const UO="•";function LO(n){return n>=32?UO:n==10?"␤":String.fromCharCode(9216+n)}class _O extends st{constructor(e,t){super(),this.options=e,this.code=t}eq(e){return e.code==this.code}toDOM(e){let t=LO(this.code),i=e.state.phrase("Control character")+" "+(EO[this.code]||"0x"+this.code.toString(16)),s=this.options.render&&this.options.render(this.code,i,t);if(s)return s;let r=document.createElement("span");return r.textContent=t,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class NO extends st{constructor(e){super(),this.width=e}eq(e){return e.width==this.width}toDOM(){let e=document.createElement("span");return e.textContent="	",e.className="cm-tab",e.style.width=this.width+"px",e}ignoreEvent(){return!1}}function IO(){return FO}const GO=A.line({class:"cm-activeLine"}),FO=F.fromClass(class{constructor(n){this.decorations=this.getDeco(n)}update(n){(n.docChanged||n.selectionSet)&&(this.decorations=this.getDeco(n.view))}getDeco(n){let e=-1,t=[];for(let i of n.state.selection.ranges){let s=n.lineBlockAt(i.head);s.from>e&&(t.push(GO.range(s.from)),e=s.from)}return A.set(t)}},{decorations:n=>n.decorations});class HO extends st{constructor(e){super(),this.content=e}toDOM(){let e=document.createElement("span");return e.className="cm-placeholder",e.style.pointerEvents="none",e.appendChild(typeof this.content=="string"?document.createTextNode(this.content):this.content),typeof this.content=="string"?e.setAttribute("aria-label","placeholder "+this.content):e.setAttribute("aria-hidden","true"),e}coordsAt(e){let t=e.firstChild?si(e.firstChild):[];if(!t.length)return null;let i=window.getComputedStyle(e.parentNode),s=Os(t[0],i.direction!="rtl"),r=parseInt(i.lineHeight);return s.bottom-s.top>r*1.5?{left:s.left,right:s.right,top:s.top,bottom:s.top+r}:s}ignoreEvent(){return!1}}function KO(n){return F.fromClass(class{constructor(e){this.view=e,this.placeholder=n?A.set([A.widget({widget:new HO(n),side:1}).range(0)]):A.none}get decorations(){return this.view.state.doc.length?A.none:this.placeholder}},{decorations:e=>e.decorations})}const Dr=2e3;function JO(n,e,t){let i=Math.min(e.line,t.line),s=Math.max(e.line,t.line),r=[];if(e.off>Dr||t.off>Dr||e.col<0||t.col<0){let o=Math.min(e.off,t.off),l=Math.max(e.off,t.off);for(let a=i;a<=s;a++){let h=n.doc.line(a);h.length<=l&&r.push(y.range(h.from+o,h.to+l))}}else{let o=Math.min(e.col,t.col),l=Math.max(e.col,t.col);for(let a=i;a<=s;a++){let h=n.doc.line(a),c=pr(h.text,o,n.tabSize,!0);if(c<0)r.push(y.cursor(h.to));else{let f=pr(h.text,l,n.tabSize);r.push(y.range(h.from+c,h.from+f))}}}return r}function ep(n,e){let t=n.coordsAtPos(n.viewport.from);return t?Math.round(Math.abs((t.left-e)/n.defaultCharacterWidth)):-1}function Yl(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1),i=n.state.doc.lineAt(t),s=t-i.from,r=s>Dr?-1:s==i.length?ep(n,e.clientX):ci(i.text,n.state.tabSize,t-i.from);return{line:i.number,col:r,off:s}}function tp(n,e){let t=Yl(n,e),i=n.state.selection;return t?{update(s){if(s.docChanged){let r=s.changes.mapPos(s.startState.doc.line(t.line).from),o=s.state.doc.lineAt(r);t={line:o.number,col:t.col,off:Math.min(t.off,o.length)},i=i.map(s.changes)}},get(s,r,o){let l=Yl(n,s);if(!l)return i;let a=JO(n.state,t,l);return a.length?o?y.create(a.concat(i.ranges)):y.create(a):i}}:null}function ip(n){let e=t=>t.altKey&&t.button==0;return v.mouseSelectionStyle.of((t,i)=>e(i)?tp(t,i):null)}const np={Alt:[18,n=>!!n.altKey],Control:[17,n=>!!n.ctrlKey],Shift:[16,n=>!!n.shiftKey],Meta:[91,n=>!!n.metaKey]},sp={style:"cursor: crosshair"};function rp(n={}){let[e,t]=np[n.key||"Alt"],i=F.fromClass(class{constructor(s){this.view=s,this.isDown=!1}set(s){this.isDown!=s&&(this.isDown=s,this.view.update([]))}},{eventObservers:{keydown(s){this.set(s.keyCode==e||t(s))},keyup(s){(s.keyCode==e||!t(s))&&this.set(!1)},mousemove(s){this.set(t(s))}}});return[i,v.contentAttributes.of(s=>{var r;return!((r=s.plugin(i))===null||r===void 0)&&r.isDown?sp:null})]}const bi="-10000px";class gc{constructor(e,t,i,s){this.facet=t,this.createTooltipView=i,this.removeTooltipView=s,this.input=e.state.facet(t),this.tooltips=this.input.filter(o=>o);let r=null;this.tooltipViews=this.tooltips.map(o=>r=i(o,r))}update(e,t){var i;let s=e.state.facet(this.facet),r=s.filter(a=>a);if(s===this.input){for(let a of this.tooltipViews)a.update&&a.update(e);return!1}let o=[],l=t?[]:null;for(let a=0;a<r.length;a++){let h=r[a],c=-1;if(h){for(let f=0;f<this.tooltips.length;f++){let u=this.tooltips[f];u&&u.create==h.create&&(c=f)}if(c<0)o[a]=this.createTooltipView(h,a?o[a-1]:null),l&&(l[a]=!!h.above);else{let f=o[a]=this.tooltipViews[c];l&&(l[a]=t[c]),f.update&&f.update(e)}}}for(let a of this.tooltipViews)o.indexOf(a)<0&&(this.removeTooltipView(a),(i=a.destroy)===null||i===void 0||i.call(a));return t&&(l.forEach((a,h)=>t[h]=a),t.length=l.length),this.input=s,this.tooltips=r,this.tooltipViews=o,!0}}function op(n){let{win:e}=n;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const qs=C.define({combine:n=>{var e,t,i;return{position:T.ios?"absolute":((e=n.find(s=>s.position))===null||e===void 0?void 0:e.position)||"fixed",parent:((t=n.find(s=>s.parent))===null||t===void 0?void 0:t.parent)||null,tooltipSpace:((i=n.find(s=>s.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||op}}}),Dl=new WeakMap,uo=F.fromClass(class{constructor(n){this.view=n,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=n.state.facet(qs);this.position=e.position,this.parent=e.parent,this.classes=n.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver=typeof ResizeObserver=="function"?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new gc(n,gs,(t,i)=>this.createTooltip(t,i),t=>{this.resizeObserver&&this.resizeObserver.unobserve(t.dom),t.dom.remove()}),this.above=this.manager.tooltips.map(t=>!!t.above),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),n.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let n of this.manager.tooltipViews)this.intersectionObserver.observe(n.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(n){n.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(n,this.above);e&&this.observeIntersection();let t=e||n.geometryChanged,i=n.state.facet(qs);if(i.position!=this.position&&!this.madeAbsolute){this.position=i.position;for(let s of this.manager.tooltipViews)s.dom.style.position=this.position;t=!0}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let s of this.manager.tooltipViews)this.container.appendChild(s.dom);t=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);t&&this.maybeMeasure()}createTooltip(n,e){let t=n.create(this.view),i=e?e.dom:null;if(t.dom.classList.add("cm-tooltip"),n.arrow&&!t.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let s=document.createElement("div");s.className="cm-tooltip-arrow",t.dom.appendChild(s)}return t.dom.style.position=this.position,t.dom.style.top=bi,t.dom.style.left="0px",this.container.insertBefore(t.dom,i),t.mount&&t.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(t.dom),t}destroy(){var n,e,t;this.view.win.removeEventListener("resize",this.measureSoon);for(let i of this.manager.tooltipViews)i.dom.remove(),(n=i.destroy)===null||n===void 0||n.call(i);this.parent&&this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect(),(t=this.intersectionObserver)===null||t===void 0||t.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let n=this.view.dom.getBoundingClientRect(),e=1,t=1,i=!1;if(this.position=="fixed"&&this.manager.tooltipViews.length){let{dom:s}=this.manager.tooltipViews[0];if(T.gecko)i=s.offsetParent!=this.container.ownerDocument.body;else if(s.style.top==bi&&s.style.left=="0px"){let r=s.getBoundingClientRect();i=Math.abs(r.top+1e4)>1||Math.abs(r.left)>1}}if(i||this.position=="absolute")if(this.parent){let s=this.parent.getBoundingClientRect();s.width&&s.height&&(e=s.width/this.parent.offsetWidth,t=s.height/this.parent.offsetHeight)}else({scaleX:e,scaleY:t}=this.view.viewState);return{editor:n,parent:this.parent?this.container.getBoundingClientRect():n,pos:this.manager.tooltips.map((s,r)=>{let o=this.manager.tooltipViews[r];return o.getCoords?o.getCoords(s.pos):this.view.coordsAtPos(s.pos)}),size:this.manager.tooltipViews.map(({dom:s})=>s.getBoundingClientRect()),space:this.view.state.facet(qs).tooltipSpace(this.view),scaleX:e,scaleY:t,makeAbsolute:i}}writeMeasure(n){var e;if(n.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let l of this.manager.tooltipViews)l.dom.style.position="absolute"}let{editor:t,space:i,scaleX:s,scaleY:r}=n,o=[];for(let l=0;l<this.manager.tooltips.length;l++){let a=this.manager.tooltips[l],h=this.manager.tooltipViews[l],{dom:c}=h,f=n.pos[l],u=n.size[l];if(!f||f.bottom<=Math.max(t.top,i.top)||f.top>=Math.min(t.bottom,i.bottom)||f.right<Math.max(t.left,i.left)-.1||f.left>Math.min(t.right,i.right)+.1){c.style.top=bi;continue}let d=a.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,O=d?7:0,m=u.right-u.left,g=(e=Dl.get(h))!==null&&e!==void 0?e:u.bottom-u.top,b=h.offset||ap,S=this.view.textDirection==I.LTR,P=u.width>i.right-i.left?S?i.left:i.right-u.width:S?Math.min(f.left-(d?14:0)+b.x,i.right-m):Math.max(i.left,f.left-m+(d?14:0)-b.x),Q=this.above[l];!a.strictSide&&(Q?f.top-(u.bottom-u.top)-b.y<i.top:f.bottom+(u.bottom-u.top)+b.y>i.bottom)&&Q==i.bottom-f.bottom>f.top-i.top&&(Q=this.above[l]=!Q);let x=(Q?f.top-i.top:i.bottom-f.bottom)-O;if(x<g&&h.resize!==!1){if(x<this.view.defaultLineHeight){c.style.top=bi;continue}Dl.set(h,g),c.style.height=(g=x)/r+"px"}else c.style.height&&(c.style.height="");let Z=Q?f.top-g-O-b.y:f.bottom+O+b.y,$=P+m;if(h.overlap!==!0)for(let W of o)W.left<$&&W.right>P&&W.top<Z+g&&W.bottom>Z&&(Z=Q?W.top-g-2-O:W.bottom+O+2);if(this.position=="absolute"?(c.style.top=(Z-n.parent.top)/r+"px",c.style.left=(P-n.parent.left)/s+"px"):(c.style.top=Z/r+"px",c.style.left=P/s+"px"),d){let W=f.left+(S?b.x:-b.x)-(P+14-7);d.style.left=W/s+"px"}h.overlap!==!0&&o.push({left:P,top:Z,right:$,bottom:Z+g}),c.classList.toggle("cm-tooltip-above",Q),c.classList.toggle("cm-tooltip-below",!Q),h.positioned&&h.positioned(n.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let n of this.manager.tooltipViews)n.dom.style.top=bi}},{eventObservers:{scroll(){this.maybeMeasure()}}}),lp=v.baseTheme({".cm-tooltip":{zIndex:100,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),ap={x:0,y:0},gs=C.define({enables:[uo,lp]}),Gn=C.define({combine:n=>n.reduce((e,t)=>e.concat(t),[])});class bs{static create(e){return new bs(e)}constructor(e){this.view=e,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new gc(e,Gn,(t,i)=>this.createHostedView(t,i),t=>t.dom.remove())}createHostedView(e,t){let i=e.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,t?t.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(e){for(let t of this.manager.tooltipViews)t.mount&&t.mount(e);this.mounted=!0}positioned(e){for(let t of this.manager.tooltipViews)t.positioned&&t.positioned(e)}update(e){this.manager.update(e)}destroy(){var e;for(let t of this.manager.tooltipViews)(e=t.destroy)===null||e===void 0||e.call(t)}passProp(e){let t;for(let i of this.manager.tooltipViews){let s=i[e];if(s!==void 0){if(t===void 0)t=s;else if(t!==s)return}}return t}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}const hp=gs.compute([Gn],n=>{let e=n.facet(Gn);return e.length===0?null:{pos:Math.min(...e.map(t=>t.pos)),end:Math.max(...e.map(t=>{var i;return(i=t.end)!==null&&i!==void 0?i:t.pos})),create:bs.create,above:e[0].above,arrow:e.some(t=>t.arrow)}});class cp{constructor(e,t,i,s,r){this.view=e,this.source=t,this.field=i,this.setHover=s,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:e.dom,time:0},this.checkHover=this.checkHover.bind(this),e.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),e.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let e=Date.now()-this.lastMove.time;e<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-e):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:e,lastMove:t}=this,i=e.docView.nearest(t.target);if(!i)return;let s,r=1;if(i instanceof Ot)s=i.posAtStart;else{if(s=e.posAtCoords(t),s==null)return;let l=e.coordsAtPos(s);if(!l||t.y<l.top||t.y>l.bottom||t.x<l.left-e.defaultCharacterWidth||t.x>l.right+e.defaultCharacterWidth)return;let a=e.bidiSpans(e.state.doc.lineAt(s)).find(c=>c.from<=s&&c.to>=s),h=a&&a.dir==I.RTL?-1:1;r=t.x<l.left?-h:h}let o=this.source(e,s,r);if(o!=null&&o.then){let l=this.pending={pos:s};o.then(a=>{this.pending==l&&(this.pending=null,a&&!(Array.isArray(a)&&!a.length)&&e.dispatch({effects:this.setHover.of(Array.isArray(a)?a:[a])}))},a=>ge(e.state,a,"hover tooltip"))}else o&&!(Array.isArray(o)&&!o.length)&&e.dispatch({effects:this.setHover.of(Array.isArray(o)?o:[o])})}get tooltip(){let e=this.view.plugin(uo),t=e?e.manager.tooltips.findIndex(i=>i.create==bs.create):-1;return t>-1?e.manager.tooltipViews[t]:null}mousemove(e){var t,i;this.lastMove={x:e.clientX,y:e.clientY,target:e.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:s,tooltip:r}=this;if(s.length&&r&&!fp(r.dom,e)||this.pending){let{pos:o}=s[0]||this.pending,l=(i=(t=s[0])===null||t===void 0?void 0:t.end)!==null&&i!==void 0?i:o;(o==l?this.view.posAtCoords(this.lastMove)!=o:!up(this.view,o,l,e.clientX,e.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(e){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:t}=this;if(t.length){let{tooltip:i}=this;i&&i.dom.contains(e.relatedTarget)?this.watchTooltipLeave(i.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(e){let t=i=>{e.removeEventListener("mouseleave",t),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};e.addEventListener("mouseleave",t)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}const xn=4;function fp(n,e){let t=n.getBoundingClientRect();return e.clientX>=t.left-xn&&e.clientX<=t.right+xn&&e.clientY>=t.top-xn&&e.clientY<=t.bottom+xn}function up(n,e,t,i,s,r){let o=n.scrollDOM.getBoundingClientRect(),l=n.documentTop+n.documentPadding.top+n.contentHeight;if(o.left>i||o.right<i||o.top>s||Math.min(o.bottom,l)<s)return!1;let a=n.posAtCoords({x:i,y:s},!1);return a>=e&&a<=t}function dp(n,e={}){let t=M.define(),i=ee.define({create(){return[]},update(s,r){if(s.length&&(e.hideOnChange&&(r.docChanged||r.selection)?s=[]:e.hideOn&&(s=s.filter(o=>!e.hideOn(r,o))),r.docChanged)){let o=[];for(let l of s){let a=r.changes.mapPos(l.pos,-1,ce.TrackDel);if(a!=null){let h=Object.assign(Object.create(null),l);h.pos=a,h.end!=null&&(h.end=r.changes.mapPos(h.end)),o.push(h)}}s=o}for(let o of r.effects)o.is(t)&&(s=o.value),o.is(Op)&&(s=[]);return s},provide:s=>Gn.from(s)});return[i,F.define(s=>new cp(s,n,i,t,e.hoverTime||300)),hp]}function bc(n,e){let t=n.plugin(uo);if(!t)return null;let i=t.manager.tooltips.indexOf(e);return i<0?null:t.manager.tooltipViews[i]}const Op=M.define(),ql=C.define({combine(n){let e,t;for(let i of n)e=e||i.topContainer,t=t||i.bottomContainer;return{topContainer:e,bottomContainer:t}}});function Ei(n,e){let t=n.plugin(yc),i=t?t.specs.indexOf(e):-1;return i>-1?t.panels[i]:null}const yc=F.fromClass(class{constructor(n){this.input=n.state.facet(Vi),this.specs=this.input.filter(t=>t),this.panels=this.specs.map(t=>t(n));let e=n.state.facet(ql);this.top=new Sn(n,!0,e.topContainer),this.bottom=new Sn(n,!1,e.bottomContainer),this.top.sync(this.panels.filter(t=>t.top)),this.bottom.sync(this.panels.filter(t=>!t.top));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(n){let e=n.state.facet(ql);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new Sn(n.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new Sn(n.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let t=n.state.facet(Vi);if(t!=this.input){let i=t.filter(a=>a),s=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),c;h<0?(c=a(n.view),l.push(c)):(c=this.panels[h],c.update&&c.update(n)),s.push(c),(c.top?r:o).push(c)}this.specs=i,this.panels=s,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount()}else for(let i of this.panels)i.update&&i.update(n)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:n=>v.scrollMargins.of(e=>{let t=e.plugin(n);return t&&{top:t.top.scrollMargin(),bottom:t.bottom.scrollMargin()}})});class Sn{constructor(e,t,i){this.view=e,this.top=t,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(e){for(let t of this.panels)t.destroy&&e.indexOf(t)<0&&t.destroy();this.panels=e,this.syncDOM()}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let e=this.dom.firstChild;for(let t of this.panels)if(t.dom.parentNode==this.dom){for(;e!=t.dom;)e=zl(e);e=e.nextSibling}else this.dom.insertBefore(t.dom,e);for(;e;)e=zl(e)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let e of this.classes.split(" "))e&&this.container.classList.remove(e);for(let e of(this.classes=this.view.themeClasses).split(" "))e&&this.container.classList.add(e)}}}function zl(n){let e=n.nextSibling;return n.remove(),e}const Vi=C.define({enables:yc});class nt extends Yt{compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}eq(e){return!1}destroy(e){}}nt.prototype.elementClass="";nt.prototype.toDOM=void 0;nt.prototype.mapMode=ce.TrackBefore;nt.prototype.startSide=nt.prototype.endSide=-1;nt.prototype.point=!0;const qn=C.define(),pp={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>E.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Wi=C.define();function xc(n){return[Sc(),Wi.of(Object.assign(Object.assign({},pp),n))]}const jl=C.define({combine:n=>n.some(e=>e)});function Sc(n){return[mp]}const mp=F.fromClass(class{constructor(n){this.view=n,this.prevViewport=n.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=n.state.facet(Wi).map(e=>new Vl(n,e));for(let e of this.gutters)this.dom.appendChild(e.dom);this.fixed=!n.state.facet(jl),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),n.scrollDOM.insertBefore(this.dom,n.contentDOM)}update(n){if(this.updateGutters(n)){let e=this.prevViewport,t=n.view.viewport,i=Math.min(e.to,t.to)-Math.max(e.from,t.from);this.syncGutters(i<(t.to-t.from)*.8)}n.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px"),this.view.state.facet(jl)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=n.view.viewport}syncGutters(n){let e=this.dom.nextSibling;n&&this.dom.remove();let t=E.iter(this.view.state.facet(qn),this.view.viewport.from),i=[],s=this.gutters.map(r=>new gp(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks)if(i.length&&(i=[]),Array.isArray(r.type)){let o=!0;for(let l of r.type)if(l.type==be.Text&&o){qr(t,i,l.from);for(let a of s)a.line(this.view,l,i);o=!1}else if(l.widget)for(let a of s)a.widget(this.view,l)}else if(r.type==be.Text){qr(t,i,r.from);for(let o of s)o.line(this.view,r,i)}else if(r.widget)for(let o of s)o.widget(this.view,r);for(let r of s)r.finish();n&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(n){let e=n.startState.facet(Wi),t=n.state.facet(Wi),i=n.docChanged||n.heightChanged||n.viewportChanged||!E.eq(n.startState.facet(qn),n.state.facet(qn),n.view.viewport.from,n.view.viewport.to);if(e==t)for(let s of this.gutters)s.update(n)&&(i=!0);else{i=!0;let s=[];for(let r of t){let o=e.indexOf(r);o<0?s.push(new Vl(this.view,r)):(this.gutters[o].update(n),s.push(this.gutters[o]))}for(let r of this.gutters)r.dom.remove(),s.indexOf(r)<0&&r.destroy();for(let r of s)this.dom.appendChild(r.dom);this.gutters=s}return i}destroy(){for(let n of this.gutters)n.destroy();this.dom.remove()}},{provide:n=>v.scrollMargins.of(e=>{let t=e.plugin(n);return!t||t.gutters.length==0||!t.fixed?null:e.textDirection==I.LTR?{left:t.dom.offsetWidth*e.scaleX}:{right:t.dom.offsetWidth*e.scaleX}})});function El(n){return Array.isArray(n)?n:[n]}function qr(n,e,t){for(;n.value&&n.from<=t;)n.from==t&&e.push(n.value),n.next()}class gp{constructor(e,t,i){this.gutter=e,this.height=i,this.i=0,this.cursor=E.iter(e.markers,t.from)}addElement(e,t,i){let{gutter:s}=this,r=(t.top-this.height)/e.scaleY,o=t.height/e.scaleY;if(this.i==s.elements.length){let l=new wc(e,o,r,i);s.elements.push(l),s.dom.appendChild(l.dom)}else s.elements[this.i].update(e,o,r,i);this.height=t.bottom,this.i++}line(e,t,i){let s=[];qr(this.cursor,s,t.from),i.length&&(s=s.concat(i));let r=this.gutter.config.lineMarker(e,t,s);r&&s.unshift(r);let o=this.gutter;s.length==0&&!o.config.renderEmptyElements||this.addElement(e,t,s)}widget(e,t){let i=this.gutter.config.widgetMarker(e,t.widget,t);i&&this.addElement(e,t,[i])}finish(){let e=this.gutter;for(;e.elements.length>this.i;){let t=e.elements.pop();e.dom.removeChild(t.dom),t.destroy()}}}class Vl{constructor(e,t){this.view=e,this.config=t,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in t.domEventHandlers)this.dom.addEventListener(i,s=>{let r=s.target,o;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let a=r.getBoundingClientRect();o=(a.top+a.bottom)/2}else o=s.clientY;let l=e.lineBlockAtHeight(o-e.documentTop);t.domEventHandlers[i](e,l,s)&&s.preventDefault()});this.markers=El(t.markers(e)),t.initialSpacer&&(this.spacer=new wc(e,0,0,[t.initialSpacer(e)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(e){let t=this.markers;if(this.markers=El(this.config.markers(e.view)),this.spacer&&this.config.updateSpacer){let s=this.config.updateSpacer(this.spacer.markers[0],e);s!=this.spacer.markers[0]&&this.spacer.update(e.view,0,0,[s])}let i=e.view.viewport;return!E.eq(this.markers,t,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(e):!1)}destroy(){for(let e of this.elements)e.destroy()}}class wc{constructor(e,t,i,s){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(e,t,i,s)}update(e,t,i,s){this.height!=t&&(this.height=t,this.dom.style.height=t+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),bp(this.markers,s)||this.setMarkers(e,s)}setMarkers(e,t){let i="cm-gutterElement",s=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<t.length?t[r++]:null,h=!1;if(a){let c=a.elementClass;c&&(i+=" "+c);for(let f=o;f<this.markers.length;f++)if(this.markers[f].compare(a)){l=f,h=!0;break}}else l=this.markers.length;for(;o<l;){let c=this.markers[o++];if(c.toDOM){c.destroy(s);let f=s.nextSibling;s.remove(),s=f}}if(!a)break;a.toDOM&&(h?s=s.nextSibling:this.dom.insertBefore(a.toDOM(e),s)),h&&o++}this.dom.className=i,this.markers=t}destroy(){this.setMarkers(null,[])}}function bp(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].compare(e[t]))return!1;return!0}const yp=C.define(),_t=C.define({combine(n){return Ve(n,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(e,t){let i=Object.assign({},e);for(let s in t){let r=i[s],o=t[s];i[s]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o}return i}})}});class zs extends nt{constructor(e){super(),this.number=e}eq(e){return this.number==e.number}toDOM(){return document.createTextNode(this.number)}}function js(n,e){return n.state.facet(_t).formatNumber(e,n.state)}const xp=Wi.compute([_t],n=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(e){return e.state.facet(yp)},lineMarker(e,t,i){return i.some(s=>s.toDOM)?null:new zs(js(e,e.state.doc.lineAt(t.from).number))},widgetMarker:()=>null,lineMarkerChange:e=>e.startState.facet(_t)!=e.state.facet(_t),initialSpacer(e){return new zs(js(e,Bl(e.state.doc.lines)))},updateSpacer(e,t){let i=js(t.view,Bl(t.view.state.doc.lines));return i==e.number?e:new zs(i)},domEventHandlers:n.facet(_t).domEventHandlers}));function Sp(n={}){return[_t.of(n),Sc(),xp]}function Bl(n){let e=9;for(;e<n;)e=e*10+9;return e}const wp=new class extends nt{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},Qp=qn.compute(["selection"],n=>{let e=[],t=-1;for(let i of n.selection.ranges){let s=n.doc.lineAt(i.head).from;s>t&&(t=s,e.push(wp.range(s)))}return E.of(e)});function kp(){return Qp}const Qc=1024;let Pp=0;class Es{constructor(e,t){this.from=e,this.to=t}}class q{constructor(e={}){this.id=Pp++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof e!="function"&&(e=$e.match(e)),t=>{let i=e(t);return i===void 0?null:[this,i]}}}q.closedBy=new q({deserialize:n=>n.split(" ")});q.openedBy=new q({deserialize:n=>n.split(" ")});q.group=new q({deserialize:n=>n.split(" ")});q.isolate=new q({deserialize:n=>{if(n&&n!="rtl"&&n!="ltr"&&n!="auto")throw new RangeError("Invalid value for isolate: "+n);return n||"auto"}});q.contextHash=new q({perNode:!0});q.lookAhead=new q({perNode:!0});q.mounted=new q({perNode:!0});class Fn{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[q.mounted.id]}}const $p=Object.create(null);class $e{constructor(e,t,i,s=0){this.name=e,this.props=t,this.id=i,this.flags=s}static define(e){let t=e.props&&e.props.length?Object.create(null):$p,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(e.name==null?8:0),s=new $e(e.name||"",t,e.id,i);if(e.props){for(let r of e.props)if(Array.isArray(r)||(r=r(s)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[r[0].id]=r[1]}}return s}prop(e){return this.props[e.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(e){if(typeof e=="string"){if(this.name==e)return!0;let t=this.prop(q.group);return t?t.indexOf(e)>-1:!1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let s of i.split(" "))t[s]=e[i];return i=>{for(let s=i.prop(q.group),r=-1;r<(s?s.length:0);r++){let o=t[r<0?i.name:s[r]];if(o)return o}}}}$e.none=new $e("",Object.create(null),0,8);class Oo{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let s=null;for(let r of e){let o=r(i);o&&(s||(s=Object.assign({},i.props)),s[o[0].id]=o[1])}t.push(s?new $e(i.name,s,i.id,i.flags):i)}return new Oo(t)}}const wn=new WeakMap,Ul=new WeakMap;var se;(function(n){n[n.ExcludeBuffers=1]="ExcludeBuffers",n[n.IncludeAnonymous=2]="IncludeAnonymous",n[n.IgnoreMounts=4]="IgnoreMounts",n[n.IgnoreOverlays=8]="IgnoreOverlays"})(se||(se={}));class J{constructor(e,t,i,s,r){if(this.type=e,this.children=t,this.positions=i,this.length=s,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l}}toString(){let e=Fn.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let i of this.children){let s=i.toString();s&&(t&&(t+=","),t+=s)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new jr(this.topNode,e)}cursorAt(e,t=0,i=0){let s=wn.get(this)||this.topNode,r=new jr(s);return r.moveTo(e,t),wn.set(this,r._tree),r}get topNode(){return new Pe(this,0,0,null)}resolve(e,t=0){let i=Bi(wn.get(this)||this.topNode,e,t,!1);return wn.set(this,i),i}resolveInner(e,t=0){let i=Bi(Ul.get(this)||this.topNode,e,t,!0);return Ul.set(this,i),i}resolveStack(e,t=0){return Cp(this,e,t)}iterate(e){let{enter:t,leave:i,from:s=0,to:r=this.length}=e,o=e.mode||0,l=(o&se.IncludeAnonymous)>0;for(let a=this.cursor(o|se.IncludeAnonymous);;){let h=!1;if(a.from<=r&&a.to>=s&&(!l&&a.type.isAnonymous||t(a)!==!1)){if(a.firstChild())continue;h=!0}for(;h&&i&&(l||!a.type.isAnonymous)&&i(a),!a.nextSibling();){if(!a.parent())return;h=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:go($e.none,this.children,this.positions,0,this.children.length,0,this.length,(t,i,s)=>new J(this.type,t,i,s,this.propValues),e.makeTree||((t,i,s)=>new J($e.none,t,i,s)))}static build(e){return Tp(e)}}J.empty=new J($e.none,[],[],0);class po{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new po(this.buffer,this.index)}}class Qt{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return $e.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],s=this.set.types[t],r=s.name;if(/\W/.test(r)&&!s.isError&&(r=JSON.stringify(r)),e+=4,i==e)return r;let o=[];for(;e<i;)o.push(this.childString(e)),e=this.buffer[e+3];return r+"("+o.join(",")+")"}findChild(e,t,i,s,r){let{buffer:o}=this,l=-1;for(let a=e;a!=t&&!(kc(r,s,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(e,t,i){let s=this.buffer,r=new Uint16Array(t-e),o=0;for(let l=e,a=0;l<t;){r[a++]=s[l++],r[a++]=s[l++]-i;let h=r[a++]=s[l++]-i;r[a++]=s[l++]-e,o=Math.max(o,h)}return new Qt(r,o,this.set)}}function kc(n,e,t,i){switch(n){case-2:return t<e;case-1:return i>=e&&t<e;case 0:return t<e&&i>e;case 1:return t<=e&&i>e;case 2:return i>e;case 4:return!0}}function Bi(n,e,t,i){for(var s;n.from==n.to||(t<1?n.from>=e:n.from>e)||(t>-1?n.to<=e:n.to<e);){let o=!i&&n instanceof Pe&&n.index<0?null:n.parent;if(!o)return n;n=o}let r=i?0:se.IgnoreOverlays;if(i)for(let o=n,l=o.parent;l;o=l,l=o.parent)o instanceof Pe&&o.index<0&&((s=l.enter(e,t,r))===null||s===void 0?void 0:s.from)!=o.from&&(n=l);for(;;){let o=n.enter(e,t,r);if(!o)return n;n=o}}class Pc{cursor(e=0){return new jr(this,e)}getChild(e,t=null,i=null){let s=Ll(this,e,t,i);return s.length?s[0]:null}getChildren(e,t=null,i=null){return Ll(this,e,t,i)}resolve(e,t=0){return Bi(this,e,t,!1)}resolveInner(e,t=0){return Bi(this,e,t,!0)}matchContext(e){return zr(this,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let s=t.lastChild;if(!s||s.to!=t.to)break;s.type.isError&&s.from==s.to?(i=t,t=s.prevSibling):t=s}return i}get node(){return this}get next(){return this.parent}}class Pe extends Pc{constructor(e,t,i,s){super(),this._tree=e,this.from=t,this.index=i,this._parent=s}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,s,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=t>0?l.length:-1;e!=h;e+=t){let c=l[e],f=a[e]+o.from;if(kc(s,i,f,f+c.length)){if(c instanceof Qt){if(r&se.ExcludeBuffers)continue;let u=c.findChild(0,c.buffer.length,t,i-f,s);if(u>-1)return new Ke(new vp(o,c,e,f),null,u)}else if(r&se.IncludeAnonymous||!c.type.isAnonymous||mo(c)){let u;if(!(r&se.IgnoreMounts)&&(u=Fn.get(c))&&!u.overlay)return new Pe(u.tree,f,e,o);let d=new Pe(c,f,e,o);return r&se.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(t<0?c.children.length-1:0,t,i,s)}}}if(r&se.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?e=o.index+t:e=t<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let s;if(!(i&se.IgnoreOverlays)&&(s=Fn.get(this._tree))&&s.overlay){let r=e-this.from;for(let{from:o,to:l}of s.overlay)if((t>0?o<=r:o<r)&&(t<0?l>=r:l>r))return new Pe(s.tree,s.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function Ll(n,e,t,i){let s=n.cursor(),r=[];if(!s.firstChild())return r;if(t!=null){for(let o=!1;!o;)if(o=s.type.is(t),!s.nextSibling())return r}for(;;){if(i!=null&&s.type.is(i))return r;if(s.type.is(e)&&r.push(s.node),!s.nextSibling())return i==null?r:[]}}function zr(n,e,t=e.length-1){for(let i=n.parent;t>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(e[t]&&e[t]!=i.name)return!1;t--}}return!0}class vp{constructor(e,t,i,s){this.parent=e,this.buffer=t,this.index=i,this.start=s}}class Ke extends Pc{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.context.start,i);return r<0?null:new Ke(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&se.ExcludeBuffers)return null;let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return r<0?null:new Ke(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new Ke(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new Ke(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,s=this.index+4,r=i.buffer[this.index+3];if(r>s){let o=i.buffer[this.index+1];e.push(i.slice(s,r,o)),t.push(0)}return new J(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function $c(n){if(!n.length)return null;let e=0,t=n[0];for(let r=1;r<n.length;r++){let o=n[r];(o.from>t.from||o.to<t.to)&&(t=o,e=r)}let i=t instanceof Pe&&t.index<0?null:t.parent,s=n.slice();return i?s[e]=i:s.splice(e,1),new Zp(s,t)}class Zp{constructor(e,t){this.heads=e,this.node=t}get next(){return $c(this.heads)}}function Cp(n,e,t){let i=n.resolveInner(e,t),s=null;for(let r=i instanceof Pe?i:i.context.parent;r;r=r.parent)if(r.index<0){let o=r.parent;(s||(s=[i])).push(o.resolve(e,t)),r=o}else{let o=Fn.get(r.tree);if(o&&o.overlay&&o.overlay[0].from<=e&&o.overlay[o.overlay.length-1].to>=e){let l=new Pe(o.tree,o.overlay[0].from+r.from,-1,r);(s||(s=[i])).push(Bi(l,e,t,!1))}}return s?$c(s):i}class jr{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof Pe)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let i=e._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return e?(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0):!1}yieldBuf(e,t){this.index=e;let{start:i,buffer:s}=this.buffer;return this.type=t||s.set.types[s.buffer[e]],this.from=i+s.buffer[e+1],this.to=i+s.buffer[e+2],!0}yield(e){return e?e instanceof Pe?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:s}=this.buffer,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?i&se.ExcludeBuffers?!1:this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&se.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&se.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode)):!1;let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let s=i<0?0:this.stack[i]+4;if(this.index!=s)return this.yieldBuf(t.findChild(s,this.index,-1,0,4))}else{let s=t.buffer[this.index+3];if(s<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(s)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:s}=this;if(s){if(e>0){if(this.index<s.buffer.buffer.length)return!1}else for(let r=0;r<this.index;r++)if(s.buffer.buffer[r+3]<this.index)return!1;({index:t,parent:i}=s)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,o=e<0?-1:i._tree.children.length;r!=o;r+=e){let l=i._tree.children[r];if(this.mode&se.IncludeAnonymous||l instanceof Qt||!l.type.isAnonymous||mo(l))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let s=this.index,r=this.stack.length;r>=0;){for(let o=e;o;o=o._parent)if(o.index==s){if(s==this.index)return o;t=o,i=r+1;break e}s=this.stack[--r]}for(let s=i;s<this.stack.length;s++)t=new Ke(this.buffer,t,this.stack[s]);return this.bufferNode=new Ke(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let s=!1;if(this.type.isAnonymous||e(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(s=!0)}for(;s&&t&&t(this),s=this.type.isAnonymous,!this.nextSibling();){if(!i)return;this.parent(),i--,s=!0}}}matchContext(e){if(!this.buffer)return zr(this.node,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let s=e.length-1,r=this.stack.length-1;s>=0;r--){if(r<0)return zr(this.node,e,s);let o=i[t.buffer[this.stack[r]]];if(!o.isAnonymous){if(e[s]&&e[s]!=o.name)return!1;s--}}return!0}}function mo(n){return n.children.some(e=>e instanceof Qt||!e.type.isAnonymous||mo(e))}function Tp(n){var e;let{buffer:t,nodeSet:i,maxBufferLength:s=Qc,reused:r=[],minRepeatType:o=i.types.length}=n,l=Array.isArray(t)?new po(t,t.length):t,a=i.types,h=0,c=0;function f(x,Z,$,W,z,j){let{id:Y,start:X,end:w,size:R}=l,V=c;for(;R<0;)if(l.next(),R==-1){let te=r[Y];$.push(te),W.push(X-x);return}else if(R==-3){h=Y;return}else if(R==-4){c=Y;return}else throw new RangeError(`Unrecognized record size: ${R}`);let oe=a[Y],xe,ve,Ae=X-x;if(w-X<=s&&(ve=g(l.pos-Z,z))){let te=new Uint16Array(ve.size-ve.skip),Me=l.pos-ve.size,Ue=te.length;for(;l.pos>Me;)Ue=b(ve.start,te,Ue);xe=new Qt(te,w-ve.start,i),Ae=ve.start-x}else{let te=l.pos-R;l.next();let Me=[],Ue=[],vt=Y>=o?Y:-1,Et=0,an=w;for(;l.pos>te;)vt>=0&&l.id==vt&&l.size>=0?(l.end<=an-s&&(O(Me,Ue,X,Et,l.end,an,vt,V),Et=Me.length,an=l.end),l.next()):j>2500?u(X,te,Me,Ue):f(X,te,Me,Ue,vt,j+1);if(vt>=0&&Et>0&&Et<Me.length&&O(Me,Ue,X,Et,X,an,vt,V),Me.reverse(),Ue.reverse(),vt>-1&&Et>0){let jo=d(oe);xe=go(oe,Me,Ue,0,Me.length,0,w-X,jo,jo)}else xe=m(oe,Me,Ue,w-X,V-w)}$.push(xe),W.push(Ae)}function u(x,Z,$,W){let z=[],j=0,Y=-1;for(;l.pos>Z;){let{id:X,start:w,end:R,size:V}=l;if(V>4)l.next();else{if(Y>-1&&w<Y)break;Y<0&&(Y=R-s),z.push(X,w,R),j++,l.next()}}if(j){let X=new Uint16Array(j*4),w=z[z.length-2];for(let R=z.length-3,V=0;R>=0;R-=3)X[V++]=z[R],X[V++]=z[R+1]-w,X[V++]=z[R+2]-w,X[V++]=V;$.push(new Qt(X,z[2]-w,i)),W.push(w-x)}}function d(x){return(Z,$,W)=>{let z=0,j=Z.length-1,Y,X;if(j>=0&&(Y=Z[j])instanceof J){if(!j&&Y.type==x&&Y.length==W)return Y;(X=Y.prop(q.lookAhead))&&(z=$[j]+Y.length+X)}return m(x,Z,$,W,z)}}function O(x,Z,$,W,z,j,Y,X){let w=[],R=[];for(;x.length>W;)w.push(x.pop()),R.push(Z.pop()+$-z);x.push(m(i.types[Y],w,R,j-z,X-j)),Z.push(z-$)}function m(x,Z,$,W,z=0,j){if(h){let Y=[q.contextHash,h];j=j?[Y].concat(j):[Y]}if(z>25){let Y=[q.lookAhead,z];j=j?[Y].concat(j):[Y]}return new J(x,Z,$,W,j)}function g(x,Z){let $=l.fork(),W=0,z=0,j=0,Y=$.end-s,X={size:0,start:0,skip:0};e:for(let w=$.pos-x;$.pos>w;){let R=$.size;if($.id==Z&&R>=0){X.size=W,X.start=z,X.skip=j,j+=4,W+=4,$.next();continue}let V=$.pos-R;if(R<0||V<w||$.start<Y)break;let oe=$.id>=o?4:0,xe=$.start;for($.next();$.pos>V;){if($.size<0)if($.size==-3)oe+=4;else break e;else $.id>=o&&(oe+=4);$.next()}z=xe,W+=R,j+=oe}return(Z<0||W==x)&&(X.size=W,X.start=z,X.skip=j),X.size>4?X:void 0}function b(x,Z,$){let{id:W,start:z,end:j,size:Y}=l;if(l.next(),Y>=0&&W<o){let X=$;if(Y>4){let w=l.pos-(Y-4);for(;l.pos>w;)$=b(x,Z,$)}Z[--$]=X,Z[--$]=j-x,Z[--$]=z-x,Z[--$]=W}else Y==-3?h=W:Y==-4&&(c=W);return $}let S=[],P=[];for(;l.pos>0;)f(n.start||0,n.bufferStart||0,S,P,-1,0);let Q=(e=n.length)!==null&&e!==void 0?e:S.length?P[0]+S[0].length:0;return new J(a[n.topID],S.reverse(),P.reverse(),Q)}const _l=new WeakMap;function zn(n,e){if(!n.isAnonymous||e instanceof Qt||e.type!=n)return 1;let t=_l.get(e);if(t==null){t=1;for(let i of e.children){if(i.type!=n||!(i instanceof J)){t=1;break}t+=zn(n,i)}_l.set(e,t)}return t}function go(n,e,t,i,s,r,o,l,a){let h=0;for(let O=i;O<s;O++)h+=zn(n,e[O]);let c=Math.ceil(h*1.5/8),f=[],u=[];function d(O,m,g,b,S){for(let P=g;P<b;){let Q=P,x=m[P],Z=zn(n,O[P]);for(P++;P<b;P++){let $=zn(n,O[P]);if(Z+$>=c)break;Z+=$}if(P==Q+1){if(Z>c){let $=O[Q];d($.children,$.positions,0,$.children.length,m[Q]+S);continue}f.push(O[Q])}else{let $=m[P-1]+O[P-1].length-x;f.push(go(n,O,m,Q,P,x,$,null,a))}u.push(x+S-r)}}return d(e,t,i,s,0),(l||a)(f,u,o)}class Rp{constructor(){this.map=new WeakMap}setBuffer(e,t,i){let s=this.map.get(e);s||this.map.set(e,s=new Map),s.set(t,i)}getBuffer(e,t){let i=this.map.get(e);return i&&i.get(t)}set(e,t){e instanceof Ke?this.setBuffer(e.context.buffer,e.index,t):e instanceof Pe&&this.map.set(e.tree,t)}get(e){return e instanceof Ke?this.getBuffer(e.context.buffer,e.index):e instanceof Pe?this.map.get(e.tree):void 0}cursorSet(e,t){e.buffer?this.setBuffer(e.buffer.buffer,e.index,t):this.map.set(e.tree,t)}cursorGet(e){return e.buffer?this.getBuffer(e.buffer.buffer,e.index):this.map.get(e.tree)}}class Wt{constructor(e,t,i,s,r=!1,o=!1){this.from=e,this.to=t,this.tree=i,this.offset=s,this.open=(r?1:0)|(o?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(e,t=[],i=!1){let s=[new Wt(0,e.length,e,0,!1,i)];for(let r of t)r.to>e.length&&s.push(r);return s}static applyChanges(e,t,i=128){if(!t.length)return e;let s=[],r=1,o=e.length?e[0]:null;for(let l=0,a=0,h=0;;l++){let c=l<t.length?t[l]:null,f=c?c.fromA:1e9;if(f-a>=i)for(;o&&o.from<f;){let u=o;if(a>=u.from||f<=u.to||h){let d=Math.max(u.from,a)-h,O=Math.min(u.to,f)-h;u=d>=O?null:new Wt(d,O,u.tree,u.offset+h,l>0,!!c)}if(u&&s.push(u),o.to>f)break;o=r<e.length?e[r++]:null}if(!c)break;a=c.toA,h=c.toA-c.toB}return s}}class vc{startParse(e,t,i){return typeof e=="string"&&(e=new Ap(e)),i=i?i.length?i.map(s=>new Es(s.from,s.to)):[new Es(0,0)]:[new Es(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let s=this.startParse(e,t,i);for(;;){let r=s.advance();if(r)return r}}}class Ap{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new q({perNode:!0});let Mp=0;class Ge{constructor(e,t,i){this.set=e,this.base=t,this.modified=i,this.id=Mp++}static define(e){if(e!=null&&e.base)throw new Error("Can not derive from a modified tag");let t=new Ge([],null,[]);if(t.set.push(t),e)for(let i of e.set)t.set.push(i);return t}static defineModifier(){let e=new Hn;return t=>t.modified.indexOf(e)>-1?t:Hn.get(t.base||t,t.modified.concat(e).sort((i,s)=>i.id-s.id))}}let Wp=0;class Hn{constructor(){this.instances=[],this.id=Wp++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(l=>l.base==e&&Xp(t,l.modified));if(i)return i;let s=[],r=new Ge(s,e,t);for(let l of t)l.instances.push(r);let o=Yp(t);for(let l of e.set)if(!l.modified.length)for(let a of o)s.push(Hn.get(l,a));return r}}function Xp(n,e){return n.length==e.length&&n.every((t,i)=>t==e[i])}function Yp(n){let e=[[]];for(let t=0;t<n.length;t++)for(let i=0,s=e.length;i<s;i++)e.push(e[i].concat(n[t]));return e.sort((t,i)=>i.length-t.length)}function bo(n){let e=Object.create(null);for(let t in n){let i=n[t];Array.isArray(i)||(i=[i]);for(let s of t.split(" "))if(s){let r=[],o=2,l=s;for(let f=0;;){if(l=="..."&&f>0&&f+3==s.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+s);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),f+=u[0].length,f==s.length)break;let d=s[f++];if(f==s.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+s);l=s.slice(f)}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+s);let c=new Kn(i,o,a>0?r.slice(0,a):null);e[h]=c.sort(e[h])}}return Zc.add(e)}const Zc=new q;class Kn{constructor(e,t,i,s){this.tags=e,this.mode=t,this.context=i,this.next=s}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}Kn.empty=new Kn([],2,null);function Cc(n,e){let t=Object.create(null);for(let r of n)if(!Array.isArray(r.tag))t[r.tag.id]=r.class;else for(let o of r.tag)t[o.id]=r.class;let{scope:i,all:s=null}=e||{};return{style:r=>{let o=s;for(let l of r)for(let a of l.set){let h=t[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function Dp(n,e){let t=null;for(let i of n){let s=i.style(e);s&&(t=t?t+" "+s:s)}return t}function qp(n,e,t,i=0,s=n.length){let r=new zp(i,Array.isArray(e)?e:[e],t);r.highlightRange(n.cursor(),i,s,"",r.highlighters),r.flush(s)}class zp{constructor(e,t,i){this.at=e,this.highlighters=t,this.span=i,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,i,s,r){let{type:o,from:l,to:a}=e;if(l>=i||a<=t)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=s,c=jp(e)||Kn.empty,f=Dp(r,c.tags);if(f&&(h&&(h+=" "),h+=f,c.mode==1&&(s+=(s?" ":"")+f)),this.startSpan(Math.max(t,l),h),c.opaque)return;let u=e.tree&&e.tree.prop(q.mounted);if(u&&u.overlay){let d=e.node.enter(u.overlay[0].from+l,1),O=this.highlighters.filter(g=>!g.scope||g.scope(u.tree.type)),m=e.firstChild();for(let g=0,b=l;;g++){let S=g<u.overlay.length?u.overlay[g]:null,P=S?S.from+l:a,Q=Math.max(t,b),x=Math.min(i,P);if(Q<x&&m)for(;e.from<x&&(this.highlightRange(e,Q,x,s,r),this.startSpan(Math.min(x,e.to),h),!(e.to>=P||!e.nextSibling())););if(!S||P>i)break;b=S.to+l,b>t&&(this.highlightRange(d.cursor(),Math.max(t,S.from+l),Math.min(i,b),"",O),this.startSpan(Math.min(i,b),h))}m&&e.parent()}else if(e.firstChild()){u&&(s="");do if(!(e.to<=t)){if(e.from>=i)break;this.highlightRange(e,t,i,s,r),this.startSpan(Math.min(i,e.to),h)}while(e.nextSibling());e.parent()}}}function jp(n){let e=n.type.prop(Zc);for(;e&&e.context&&!n.matchContext(e.context);)e=e.next;return e||null}const k=Ge.define,Qn=k(),ct=k(),Nl=k(ct),Il=k(ct),ft=k(),kn=k(ft),Vs=k(ft),Ie=k(),Zt=k(Ie),_e=k(),Ne=k(),Er=k(),yi=k(Er),Pn=k(),p={comment:Qn,lineComment:k(Qn),blockComment:k(Qn),docComment:k(Qn),name:ct,variableName:k(ct),typeName:Nl,tagName:k(Nl),propertyName:Il,attributeName:k(Il),className:k(ct),labelName:k(ct),namespace:k(ct),macroName:k(ct),literal:ft,string:kn,docString:k(kn),character:k(kn),attributeValue:k(kn),number:Vs,integer:k(Vs),float:k(Vs),bool:k(ft),regexp:k(ft),escape:k(ft),color:k(ft),url:k(ft),keyword:_e,self:k(_e),null:k(_e),atom:k(_e),unit:k(_e),modifier:k(_e),operatorKeyword:k(_e),controlKeyword:k(_e),definitionKeyword:k(_e),moduleKeyword:k(_e),operator:Ne,derefOperator:k(Ne),arithmeticOperator:k(Ne),logicOperator:k(Ne),bitwiseOperator:k(Ne),compareOperator:k(Ne),updateOperator:k(Ne),definitionOperator:k(Ne),typeOperator:k(Ne),controlOperator:k(Ne),punctuation:Er,separator:k(Er),bracket:yi,angleBracket:k(yi),squareBracket:k(yi),paren:k(yi),brace:k(yi),content:Ie,heading:Zt,heading1:k(Zt),heading2:k(Zt),heading3:k(Zt),heading4:k(Zt),heading5:k(Zt),heading6:k(Zt),contentSeparator:k(Ie),list:k(Ie),quote:k(Ie),emphasis:k(Ie),strong:k(Ie),link:k(Ie),monospace:k(Ie),strikethrough:k(Ie),inserted:k(),deleted:k(),changed:k(),invalid:k(),meta:Pn,documentMeta:k(Pn),annotation:k(Pn),processingInstruction:k(Pn),definition:Ge.defineModifier(),constant:Ge.defineModifier(),function:Ge.defineModifier(),standard:Ge.defineModifier(),local:Ge.defineModifier(),special:Ge.defineModifier()};Cc([{tag:p.link,class:"tok-link"},{tag:p.heading,class:"tok-heading"},{tag:p.emphasis,class:"tok-emphasis"},{tag:p.strong,class:"tok-strong"},{tag:p.keyword,class:"tok-keyword"},{tag:p.atom,class:"tok-atom"},{tag:p.bool,class:"tok-bool"},{tag:p.url,class:"tok-url"},{tag:p.labelName,class:"tok-labelName"},{tag:p.inserted,class:"tok-inserted"},{tag:p.deleted,class:"tok-deleted"},{tag:p.literal,class:"tok-literal"},{tag:p.string,class:"tok-string"},{tag:p.number,class:"tok-number"},{tag:[p.regexp,p.escape,p.special(p.string)],class:"tok-string2"},{tag:p.variableName,class:"tok-variableName"},{tag:p.local(p.variableName),class:"tok-variableName tok-local"},{tag:p.definition(p.variableName),class:"tok-variableName tok-definition"},{tag:p.special(p.variableName),class:"tok-variableName2"},{tag:p.definition(p.propertyName),class:"tok-propertyName tok-definition"},{tag:p.typeName,class:"tok-typeName"},{tag:p.namespace,class:"tok-namespace"},{tag:p.className,class:"tok-className"},{tag:p.macroName,class:"tok-macroName"},{tag:p.propertyName,class:"tok-propertyName"},{tag:p.operator,class:"tok-operator"},{tag:p.comment,class:"tok-comment"},{tag:p.meta,class:"tok-meta"},{tag:p.invalid,class:"tok-invalid"},{tag:p.punctuation,class:"tok-punctuation"}]);var Bs;const Nt=new q;function Tc(n){return C.define({combine:n?e=>e.concat(n):void 0})}const yo=new q;class ze{constructor(e,t,i=[],s=""){this.data=e,this.name=s,D.prototype.hasOwnProperty("tree")||Object.defineProperty(D.prototype,"tree",{get(){return re(this)}}),this.parser=t,this.extension=[kt.of(this),D.languageData.of((r,o,l)=>{let a=Gl(r,o,l),h=a.type.prop(Nt);if(!h)return[];let c=r.facet(h),f=a.type.prop(yo);if(f){let u=a.resolve(o-a.from,l);for(let d of f)if(d.test(u,r)){let O=r.facet(d.facet);return d.type=="replace"?O:O.concat(c)}}return c})].concat(i)}isActiveAt(e,t,i=-1){return Gl(e,t,i).type.prop(Nt)==this.data}findRegions(e){let t=e.facet(kt);if((t==null?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],s=(r,o)=>{if(r.prop(Nt)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(q.mounted);if(l){if(l.tree.prop(Nt)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(s(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof J&&s(h,r.positions[a]+o)}};return s(re(e),0),i}get allowsNesting(){return!0}}ze.setState=M.define();function Gl(n,e,t){let i=n.facet(kt),s=re(n).topNode;if(!i||i.allowsNesting)for(let r=s;r;r=r.enter(e,t,se.ExcludeBuffers))r.type.isTop&&(s=r);return s}class Ui extends ze{constructor(e,t,i){super(e,t,[],i),this.parser=t}static define(e){let t=Tc(e.languageData);return new Ui(t,e.parser.configure({props:[Nt.add(i=>i.isTop?t:void 0)]}),e.name)}configure(e,t){return new Ui(this.data,this.parser.configure(e),t||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function re(n){let e=n.field(ze.state,!1);return e?e.tree:J.empty}class Ep{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let xi=null;class Jn{constructor(e,t,i=[],s,r,o,l,a){this.parser=e,this.state=t,this.fragments=i,this.tree=s,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new Jn(e,t,[],J.empty,0,i,[],null)}startParse(){return this.parser.startParse(new Ep(this.state.doc),this.fragments)}work(e,t){return t!=null&&t>=this.state.doc.length&&(t=void 0),this.tree!=J.empty&&this.isDone(t??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof e=="number"){let s=Date.now()+e;e=()=>Date.now()>s}for(this.parse||(this.parse=this.startParse()),t!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let s=this.parse.advance();if(s)if(this.fragments=this.withoutTempSkipped(Wt.addTree(s,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=s,this.parse=null,this.treeLen<(t??this.state.doc.length))this.parse=this.startParse();else return!0;if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(Wt.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=xi;xi=this;try{return e()}finally{xi=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=Fl(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:s,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!e.empty){let a=[];if(e.iterChangedRanges((h,c,f,u)=>a.push({fromA:h,toA:c,fromB:f,toB:u})),i=Wt.applyChanges(i,a),s=J.empty,r=0,o={from:e.mapPos(o.from,-1),to:e.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let c=e.mapPos(h.from,1),f=e.mapPos(h.to,-1);c<f&&l.push({from:c,to:f})}}}return new Jn(this.parser,t,i,s,r,o,l,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:s,to:r}=this.skipped[i];s<e.to&&r>e.from&&(this.fragments=Fl(this.fragments,s,r),this.skipped.splice(i--,1))}return this.skipped.length>=t?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends vc{createParse(t,i,s){let r=s[0].from,o=s[s.length-1].to;return{parsedPos:r,advance(){let a=xi;if(a){for(let h of s)a.tempSkipped.push(h);e&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,e]):e)}return this.parsedPos=o,new J($e.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&t[0].from==0&&t[0].to>=e}static get(){return xi}}function Fl(n,e,t){return Wt.applyChanges(n,[{fromA:e,toA:t,fromB:e,toB:t}])}class li{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new li(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=Jn.create(e.facet(kt).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new li(i)}}ze.state=ee.define({create:li.init,update(n,e){for(let t of e.effects)if(t.is(ze.setState))return t.value;return e.startState.facet(kt)!=e.state.facet(kt)?li.init(e.state):n.apply(e)}});let Rc=n=>{let e=setTimeout(()=>n(),500);return()=>clearTimeout(e)};typeof requestIdleCallback<"u"&&(Rc=n=>{let e=-1,t=setTimeout(()=>{e=requestIdleCallback(n,{timeout:400})},100);return()=>e<0?clearTimeout(t):cancelIdleCallback(e)});const Us=typeof navigator<"u"&&(!((Bs=navigator.scheduling)===null||Bs===void 0)&&Bs.isInputPending)?()=>navigator.scheduling.isInputPending():null,Vp=F.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(ze.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),(e.docChanged||e.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(ze.state);(t.tree!=t.context.tree||!t.context.isDone(e.doc.length))&&(this.working=Rc(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:s}}=this.view,r=i.field(ze.state);if(r.tree==r.context.tree&&r.context.isDone(s+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,e&&!Us?Math.max(25,e.timeRemaining()-5):1e9),l=r.context.treeLen<s&&i.doc.length>s+1e3,a=r.context.work(()=>Us&&Us()||Date.now()>o,s+(l?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:ze.setState.of(new li(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(t=>ge(this.view.state,t)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),kt=C.define({combine(n){return n.length?n[0]:null},enables:n=>[ze.state,Vp,v.contentAttributes.compute([n],e=>{let t=e.facet(n);return t&&t.name?{"data-language":t.name}:{}})]});class Ac{constructor(e,t=[]){this.language=e,this.support=t,this.extension=[e,t]}}const Bp=C.define(),tn=C.define({combine:n=>{if(!n.length)return"  ";let e=n[0];if(!e||/\S/.test(e)||Array.from(e).some(t=>t!=e[0]))throw new Error("Invalid indent unit: "+JSON.stringify(n[0]));return e}});function es(n){let e=n.facet(tn);return e.charCodeAt(0)==9?n.tabSize*e.length:e.length}function Li(n,e){let t="",i=n.tabSize,s=n.facet(tn)[0];if(s=="	"){for(;e>=i;)t+="	",e-=i;s=" "}for(let r=0;r<e;r++)t+=s;return t}function xo(n,e){n instanceof D&&(n=new ys(n));for(let i of n.state.facet(Bp)){let s=i(n,e);if(s!==void 0)return s}let t=re(n.state);return t.length>=e?Up(n,t,e):null}class ys{constructor(e,t={}){this.state=e,this.options=t,this.unit=es(e)}lineAt(e,t=1){let i=this.state.doc.lineAt(e),{simulateBreak:s,simulateDoubleBreak:r}=this.options;return s!=null&&s>=i.from&&s<=i.to?r&&s==e?{text:"",from:e}:(t<0?s<e:s<=e)?{text:i.text.slice(s-i.from),from:s}:{text:i.text.slice(0,s-i.from),from:i.from}:i}textAfterPos(e,t=1){if(this.options.simulateDoubleBreak&&e==this.options.simulateBreak)return"";let{text:i,from:s}=this.lineAt(e,t);return i.slice(e-s,Math.min(i.length,e+100-s))}column(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.countColumn(i,e-s),o=this.options.overrideIndentation?this.options.overrideIndentation(s):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(e,t=e.length){return ci(e,this.state.tabSize,t)}lineIndent(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.options.overrideIndentation;if(r){let o=r(s);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const So=new q;function Up(n,e,t){let i=e.resolveStack(t),s=i.node.enterUnfinishedNodesBefore(t);if(s!=i.node){let r=[];for(let o=s;o!=i.node;o=o.parent)r.push(o);for(let o=r.length-1;o>=0;o--)i={node:r[o],next:i}}return Mc(i,n,t)}function Mc(n,e,t){for(let i=n;i;i=i.next){let s=_p(i.node);if(s)return s(wo.create(e,t,i))}return 0}function Lp(n){return n.pos==n.options.simulateBreak&&n.options.simulateDoubleBreak}function _p(n){let e=n.type.prop(So);if(e)return e;let t=n.firstChild,i;if(t&&(i=t.type.prop(q.closedBy))){let s=n.lastChild,r=s&&i.indexOf(s.name)>-1;return o=>Wc(o,!0,1,void 0,r&&!Lp(o)?s.from:void 0)}return n.parent==null?Np:null}function Np(){return 0}class wo extends ys{constructor(e,t,i){super(e.state,e.options),this.base=e,this.pos=t,this.context=i}get node(){return this.context.node}static create(e,t,i){return new wo(e,t,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(e){let t=this.state.doc.lineAt(e.from);for(;;){let i=e.resolve(t.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(Ip(i,e))break;t=this.state.doc.lineAt(i.from)}return this.lineIndent(t.from)}continue(){return Mc(this.context.next,this.base,this.pos)}}function Ip(n,e){for(let t=e;t;t=t.parent)if(n==t)return!0;return!1}function Gp(n){let e=n.node,t=e.childAfter(e.from),i=e.lastChild;if(!t)return null;let s=n.options.simulateBreak,r=n.state.doc.lineAt(t.from),o=s==null||s<=r.from?r.to:Math.min(r.to,s);for(let l=t.to;;){let a=e.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped)return a.from<o?t:null;l=a.to}}function Fp({closing:n,align:e=!0,units:t=1}){return i=>Wc(i,e,t,n)}function Wc(n,e,t,i,s){let r=n.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||s==n.pos+o,a=e?Gp(n):null;return a?l?n.column(a.from):n.column(a.to):n.baseIndent+(l?0:n.unit*t)}const Hp=n=>n.baseIndent;function Xi({except:n,units:e=1}={}){return t=>{let i=n&&n.test(t.textAfter);return t.baseIndent+(i?0:e*t.unit)}}const Kp=200;function Jp(){return D.transactionFilter.of(n=>{if(!n.docChanged||!n.isUserEvent("input.type")&&!n.isUserEvent("input.complete"))return n;let e=n.startState.languageDataAt("indentOnInput",n.startState.selection.main.head);if(!e.length)return n;let t=n.newDoc,{head:i}=n.newSelection.main,s=t.lineAt(i);if(i>s.from+Kp)return n;let r=t.sliceString(s.from,i);if(!e.some(h=>h.test(r)))return n;let{state:o}=n,l=-1,a=[];for(let{head:h}of o.selection.ranges){let c=o.doc.lineAt(h);if(c.from==l)continue;l=c.from;let f=xo(o,c.from);if(f==null)continue;let u=/^\s*/.exec(c.text)[0],d=Li(o,f);u!=d&&a.push({from:c.from,to:c.from+u.length,insert:d})}return a.length?[n,{changes:a,sequential:!0}]:n})}const em=C.define(),Qo=new q;function Xc(n){let e=n.firstChild,t=n.lastChild;return e&&e.to<t.from?{from:e.to,to:t.type.isError?n.to:t.from}:null}function tm(n,e,t){let i=re(n);if(i.length<t)return null;let s=i.resolveStack(t,1),r=null;for(let o=s;o;o=o.next){let l=o.node;if(l.to<=t||l.from>t)continue;if(r&&l.from<e)break;let a=l.type.prop(Qo);if(a&&(l.to<i.length-50||i.length==n.doc.length||!im(l))){let h=a(l,n);h&&h.from<=t&&h.from>=e&&h.to>t&&(r=h)}}return r}function im(n){let e=n.lastChild;return e&&e.to==n.to&&e.type.isError}function ts(n,e,t){for(let i of n.facet(em)){let s=i(n,e,t);if(s)return s}return tm(n,e,t)}function Yc(n,e){let t=e.mapPos(n.from,1),i=e.mapPos(n.to,-1);return t>=i?void 0:{from:t,to:i}}const xs=M.define({map:Yc}),nn=M.define({map:Yc});function Dc(n){let e=[];for(let{head:t}of n.state.selection.ranges)e.some(i=>i.from<=t&&i.to>=t)||e.push(n.lineBlockAt(t));return e}const jt=ee.define({create(){return A.none},update(n,e){n=n.map(e.changes);for(let t of e.effects)if(t.is(xs)&&!nm(n,t.value.from,t.value.to)){let{preparePlaceholder:i}=e.state.facet(jc),s=i?A.replace({widget:new cm(i(e.state,t.value))}):Hl;n=n.update({add:[s.range(t.value.from,t.value.to)]})}else t.is(nn)&&(n=n.update({filter:(i,s)=>t.value.from!=i||t.value.to!=s,filterFrom:t.value.from,filterTo:t.value.to}));if(e.selection){let t=!1,{head:i}=e.selection.main;n.between(i,i,(s,r)=>{s<i&&r>i&&(t=!0)}),t&&(n=n.update({filterFrom:i,filterTo:i,filter:(s,r)=>r<=i||s>=i}))}return n},provide:n=>v.decorations.from(n),toJSON(n,e){let t=[];return n.between(0,e.doc.length,(i,s)=>{t.push(i,s)}),t},fromJSON(n){if(!Array.isArray(n)||n.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let t=0;t<n.length;){let i=n[t++],s=n[t++];if(typeof i!="number"||typeof s!="number")throw new RangeError("Invalid JSON for fold state");e.push(Hl.range(i,s))}return A.set(e,!0)}});function is(n,e,t){var i;let s=null;return(i=n.field(jt,!1))===null||i===void 0||i.between(e,t,(r,o)=>{(!s||s.from>r)&&(s={from:r,to:o})}),s}function nm(n,e,t){let i=!1;return n.between(e,e,(s,r)=>{s==e&&r==t&&(i=!0)}),i}function qc(n,e){return n.field(jt,!1)?e:e.concat(M.appendConfig.of(Ec()))}const sm=n=>{for(let e of Dc(n)){let t=ts(n.state,e.from,e.to);if(t)return n.dispatch({effects:qc(n.state,[xs.of(t),zc(n,t)])}),!0}return!1},rm=n=>{if(!n.state.field(jt,!1))return!1;let e=[];for(let t of Dc(n)){let i=is(n.state,t.from,t.to);i&&e.push(nn.of(i),zc(n,i,!1))}return e.length&&n.dispatch({effects:e}),e.length>0};function zc(n,e,t=!0){let i=n.state.doc.lineAt(e.from).number,s=n.state.doc.lineAt(e.to).number;return v.announce.of(`${n.state.phrase(t?"Folded lines":"Unfolded lines")} ${i} ${n.state.phrase("to")} ${s}.`)}const om=n=>{let{state:e}=n,t=[];for(let i=0;i<e.doc.length;){let s=n.lineBlockAt(i),r=ts(e,s.from,s.to);r&&t.push(xs.of(r)),i=(r?n.lineBlockAt(r.to):s).to+1}return t.length&&n.dispatch({effects:qc(n.state,t)}),!!t.length},lm=n=>{let e=n.state.field(jt,!1);if(!e||!e.size)return!1;let t=[];return e.between(0,n.state.doc.length,(i,s)=>{t.push(nn.of({from:i,to:s}))}),n.dispatch({effects:t}),!0},am=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:sm},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:rm},{key:"Ctrl-Alt-[",run:om},{key:"Ctrl-Alt-]",run:lm}],hm={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},jc=C.define({combine(n){return Ve(n,hm)}});function Ec(n){return[jt,dm]}function Vc(n,e){let{state:t}=n,i=t.facet(jc),s=o=>{let l=n.lineBlockAt(n.posAtDOM(o.target)),a=is(n.state,l.from,l.to);a&&n.dispatch({effects:nn.of(a)}),o.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(n,s,e);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",t.phrase("folded code")),r.title=t.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=s,r}const Hl=A.replace({widget:new class extends st{toDOM(n){return Vc(n,null)}}});class cm extends st{constructor(e){super(),this.value=e}eq(e){return this.value==e.value}toDOM(e){return Vc(e,this.value)}}const fm={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Ls extends nt{constructor(e,t){super(),this.config=e,this.open=t}eq(e){return this.config==e.config&&this.open==e.open}toDOM(e){if(this.config.markerDOM)return this.config.markerDOM(this.open);let t=document.createElement("span");return t.textContent=this.open?this.config.openText:this.config.closedText,t.title=e.state.phrase(this.open?"Fold line":"Unfold line"),t}}function um(n={}){let e=Object.assign(Object.assign({},fm),n),t=new Ls(e,!0),i=new Ls(e,!1),s=F.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o)}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(kt)!=o.state.facet(kt)||o.startState.field(jt,!1)!=o.state.field(jt,!1)||re(o.startState)!=re(o.state)||e.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view))}buildMarkers(o){let l=new yt;for(let a of o.viewportLineBlocks){let h=is(o.state,a.from,a.to)?i:ts(o.state,a.from,a.to)?t:null;h&&l.add(a.from,a.from,h)}return l.finish()}}),{domEventHandlers:r}=e;return[s,xc({class:"cm-foldGutter",markers(o){var l;return((l=o.plugin(s))===null||l===void 0?void 0:l.markers)||E.empty},initialSpacer(){return new Ls(e,!1)},domEventHandlers:Object.assign(Object.assign({},r),{click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return!0;let h=is(o.state,l.from,l.to);if(h)return o.dispatch({effects:nn.of(h)}),!0;let c=ts(o.state,l.from,l.to);return c?(o.dispatch({effects:xs.of(c)}),!0):!1}})}),Ec()]}const dm=v.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class sn{constructor(e,t){this.specs=e;let i;function s(l){let a=xt.newName();return(i||(i=Object.create(null)))["."+a]=l,a}const r=typeof t.all=="string"?t.all:t.all?s(t.all):void 0,o=t.scope;this.scope=o instanceof ze?l=>l.prop(Nt)==o.data:o?l=>l==o:void 0,this.style=Cc(e.map(l=>({tag:l.tag,class:l.class||s(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new xt(i):null,this.themeType=t.themeType}static define(e,t){return new sn(e,t||{})}}const Vr=C.define(),Bc=C.define({combine(n){return n.length?[n[0]]:null}});function _s(n){let e=n.facet(Vr);return e.length?e:n.facet(Bc)}function ko(n,e){let t=[pm],i;return n instanceof sn&&(n.module&&t.push(v.styleModule.of(n.module)),i=n.themeType),e!=null&&e.fallback?t.push(Bc.of(n)):i?t.push(Vr.computeN([v.darkTheme],s=>s.facet(v.darkTheme)==(i=="dark")?[n]:[])):t.push(Vr.of(n)),t}class Om{constructor(e){this.markCache=Object.create(null),this.tree=re(e.state),this.decorations=this.buildDeco(e,_s(e.state)),this.decoratedTo=e.viewport.to}update(e){let t=re(e.state),i=_s(e.state),s=i!=_s(e.startState),{viewport:r}=e.view,o=e.changes.mapPos(this.decoratedTo,1);t.length<r.to&&!s&&t.type==this.tree.type&&o>=r.to?(this.decorations=this.decorations.map(e.changes),this.decoratedTo=o):(t!=this.tree||e.viewportChanged||s)&&(this.tree=t,this.decorations=this.buildDeco(e.view,i),this.decoratedTo=r.to)}buildDeco(e,t){if(!t||!this.tree.length)return A.none;let i=new yt;for(let{from:s,to:r}of e.visibleRanges)qp(this.tree,t,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=A.mark({class:a})))},s,r);return i.finish()}}const pm=Pt.high(F.fromClass(Om,{decorations:n=>n.decorations})),Uc=sn.define([{tag:p.meta,color:"#404740"},{tag:p.link,textDecoration:"underline"},{tag:p.heading,textDecoration:"underline",fontWeight:"bold"},{tag:p.emphasis,fontStyle:"italic"},{tag:p.strong,fontWeight:"bold"},{tag:p.strikethrough,textDecoration:"line-through"},{tag:p.keyword,color:"#708"},{tag:[p.atom,p.bool,p.url,p.contentSeparator,p.labelName],color:"#219"},{tag:[p.literal,p.inserted],color:"#164"},{tag:[p.string,p.deleted],color:"#a11"},{tag:[p.regexp,p.escape,p.special(p.string)],color:"#e40"},{tag:p.definition(p.variableName),color:"#00f"},{tag:p.local(p.variableName),color:"#30a"},{tag:[p.typeName,p.namespace],color:"#085"},{tag:p.className,color:"#167"},{tag:[p.special(p.variableName),p.macroName],color:"#256"},{tag:p.definition(p.propertyName),color:"#00c"},{tag:p.comment,color:"#940"},{tag:p.invalid,color:"#f00"}]),mm=v.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),Lc=1e4,_c="()[]{}",Nc=C.define({combine(n){return Ve(n,{afterCursor:!0,brackets:_c,maxScanDistance:Lc,renderMatch:ym})}}),gm=A.mark({class:"cm-matchingBracket"}),bm=A.mark({class:"cm-nonmatchingBracket"});function ym(n){let e=[],t=n.matched?gm:bm;return e.push(t.range(n.start.from,n.start.to)),n.end&&e.push(t.range(n.end.from,n.end.to)),e}const xm=ee.define({create(){return A.none},update(n,e){if(!e.docChanged&&!e.selection)return n;let t=[],i=e.state.facet(Nc);for(let s of e.state.selection.ranges){if(!s.empty)continue;let r=Je(e.state,s.head,-1,i)||s.head>0&&Je(e.state,s.head-1,1,i)||i.afterCursor&&(Je(e.state,s.head,1,i)||s.head<e.state.doc.length&&Je(e.state,s.head+1,-1,i));r&&(t=t.concat(i.renderMatch(r,e.state)))}return A.set(t,!0)},provide:n=>v.decorations.from(n)}),Sm=[xm,mm];function wm(n={}){return[Nc.of(n),Sm]}const Qm=new q;function Br(n,e,t){let i=n.prop(e<0?q.openedBy:q.closedBy);if(i)return i;if(n.name.length==1){let s=t.indexOf(n.name);if(s>-1&&s%2==(e<0?1:0))return[t[s+e]]}return null}function Ur(n){let e=n.type.prop(Qm);return e?e(n.node):n}function Je(n,e,t,i={}){let s=i.maxScanDistance||Lc,r=i.brackets||_c,o=re(n),l=o.resolveInner(e,t);for(let a=l;a;a=a.parent){let h=Br(a.type,t,r);if(h&&a.from<a.to){let c=Ur(a);if(c&&(t>0?e>=c.from&&e<c.to:e>c.from&&e<=c.to))return km(n,e,t,a,c,h,r)}}return Pm(n,e,t,o,l.type,s,r)}function km(n,e,t,i,s,r,o){let l=i.parent,a={from:s.from,to:s.to},h=0,c=l==null?void 0:l.cursor();if(c&&(t<0?c.childBefore(i.from):c.childAfter(i.to)))do if(t<0?c.to<=i.from:c.from>=i.to){if(h==0&&r.indexOf(c.type.name)>-1&&c.from<c.to){let f=Ur(c);return{start:a,end:f?{from:f.from,to:f.to}:void 0,matched:!0}}else if(Br(c.type,t,o))h++;else if(Br(c.type,-t,o)){if(h==0){let f=Ur(c);return{start:a,end:f&&f.from<f.to?{from:f.from,to:f.to}:void 0,matched:!1}}h--}}while(t<0?c.prevSibling():c.nextSibling());return{start:a,matched:!1}}function Pm(n,e,t,i,s,r,o){let l=t<0?n.sliceDoc(e-1,e):n.sliceDoc(e,e+1),a=o.indexOf(l);if(a<0||a%2==0!=t>0)return null;let h={from:t<0?e-1:e,to:t>0?e+1:e},c=n.doc.iterRange(e,t>0?n.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let d=c.value;t<0&&(u+=d.length);let O=e+u*t;for(let m=t>0?0:d.length-1,g=t>0?d.length:-1;m!=g;m+=t){let b=o.indexOf(d[m]);if(!(b<0||i.resolveInner(O+m,1).type!=s))if(b%2==0==t>0)f++;else{if(f==1)return{start:h,end:{from:O+m,to:O+m+1},matched:b>>1==a>>1};f--}}t>0&&(u+=d.length)}return c.done?{start:h,matched:!1}:null}const $m=Object.create(null),Kl=[$e.none],Jl=[],ea=Object.create(null),vm=Object.create(null);for(let[n,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])vm[n]=Zm($m,e);function Ns(n,e){Jl.indexOf(n)>-1||(Jl.push(n),console.warn(e))}function Zm(n,e){let t=[];for(let l of e.split(" ")){let a=[];for(let h of l.split(".")){let c=n[h]||p[h];c?typeof c=="function"?a.length?a=a.map(c):Ns(h,`Modifier ${h} used at start of tag`):a.length?Ns(h,`Tag ${h} used as modifier`):a=Array.isArray(c)?c:[c]:Ns(h,`Unknown highlighting tag ${h}`)}for(let h of a)t.push(h)}if(!t.length)return 0;let i=e.replace(/ /g,"_"),s=i+" "+t.map(l=>l.id),r=ea[s];if(r)return r.id;let o=ea[s]=$e.define({id:Kl.length,name:i,props:[bo({[i]:t})]});return Kl.push(o),o.id}I.RTL,I.LTR;const Cm=n=>{let{state:e}=n,t=e.doc.lineAt(e.selection.main.from),i=$o(n.state,t.from);return i.line?Tm(n):i.block?Am(n):!1};function Po(n,e){return({state:t,dispatch:i})=>{if(t.readOnly)return!1;let s=n(e,t);return s?(i(t.update(s)),!0):!1}}const Tm=Po(Xm,0),Rm=Po(Ic,0),Am=Po((n,e)=>Ic(n,e,Wm(e)),0);function $o(n,e){let t=n.languageDataAt("commentTokens",e);return t.length?t[0]:{}}const Si=50;function Mm(n,{open:e,close:t},i,s){let r=n.sliceDoc(i-Si,i),o=n.sliceDoc(s,s+Si),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-e.length,h)==e&&o.slice(a,a+t.length)==t)return{open:{pos:i-l,margin:l&&1},close:{pos:s+a,margin:a&&1}};let c,f;s-i<=2*Si?c=f=n.sliceDoc(i,s):(c=n.sliceDoc(i,i+Si),f=n.sliceDoc(s-Si,s));let u=/^\s*/.exec(c)[0].length,d=/\s*$/.exec(f)[0].length,O=f.length-d-t.length;return c.slice(u,u+e.length)==e&&f.slice(O,O+t.length)==t?{open:{pos:i+u+e.length,margin:/\s/.test(c.charAt(u+e.length))?1:0},close:{pos:s-d-t.length,margin:/\s/.test(f.charAt(O-1))?1:0}}:null}function Wm(n){let e=[];for(let t of n.selection.ranges){let i=n.doc.lineAt(t.from),s=t.to<=i.to?i:n.doc.lineAt(t.to),r=e.length-1;r>=0&&e[r].to>i.from?e[r].to=s.to:e.push({from:i.from+/^\s*/.exec(i.text)[0].length,to:s.to})}return e}function Ic(n,e,t=e.selection.ranges){let i=t.map(r=>$o(e,r.from).block);if(!i.every(r=>r))return null;let s=t.map((r,o)=>Mm(e,i[o],r.from,r.to));if(n!=2&&!s.every(r=>r))return{changes:e.changes(t.map((r,o)=>s[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(n!=1&&s.some(r=>r)){let r=[];for(let o=0,l;o<s.length;o++)if(l=s[o]){let a=i[o],{open:h,close:c}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:c.pos-c.margin,to:c.pos+a.close.length})}return{changes:r}}return null}function Xm(n,e,t=e.selection.ranges){let i=[],s=-1;for(let{from:r,to:o}of t){let l=i.length,a=1e9,h=$o(e,r).line;if(h){for(let c=r;c<=o;){let f=e.doc.lineAt(c);if(f.from>s&&(r==o||o>f.from)){s=f.from;let u=/^\s*/.exec(f.text)[0].length,d=u==f.length,O=f.text.slice(u,u+h.length)==h?u:-1;u<f.text.length&&u<a&&(a=u),i.push({line:f,comment:O,token:h,indent:u,empty:d,single:!1})}c=f.to+1}if(a<1e9)for(let c=l;c<i.length;c++)i[c].indent<i[c].line.text.length&&(i[c].indent=a);i.length==l+1&&(i[l].single=!0)}}if(n!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:c,single:f}of i)(f||!c)&&r.push({from:l.from+h,insert:a+" "});let o=e.changes(r);return{changes:o,selection:e.selection.map(o,1)}}else if(n!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,c=h+a.length;o.text[c-o.from]==" "&&c++,r.push({from:h,to:c})}return{changes:r}}return null}const Lr=ht.define(),Ym=ht.define(),Dm=C.define(),Gc=C.define({combine(n){return Ve(n,{minDepth:100,newGroupDelay:500,joinToEvent:(e,t)=>t},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(e,t)=>(i,s)=>e(i,s)||t(i,s)})}}),Fc=ee.define({create(){return et.empty},update(n,e){let t=e.state.facet(Gc),i=e.annotation(Lr);if(i){let a=Qe.fromTransaction(e,i.selection),h=i.side,c=h==0?n.undone:n.done;return a?c=ns(c,c.length,t.minDepth,a):c=ef(c,e.startState.selection),new et(h==0?i.rest:c,h==0?c:i.rest)}let s=e.annotation(Ym);if((s=="full"||s=="before")&&(n=n.isolate()),e.annotation(K.addToHistory)===!1)return e.changes.empty?n:n.addMapping(e.changes.desc);let r=Qe.fromTransaction(e),o=e.annotation(K.time),l=e.annotation(K.userEvent);return r?n=n.addChanges(r,o,l,t,e):e.selection&&(n=n.addSelection(e.startState.selection,o,l,t.newGroupDelay)),(s=="full"||s=="after")&&(n=n.isolate()),n},toJSON(n){return{done:n.done.map(e=>e.toJSON()),undone:n.undone.map(e=>e.toJSON())}},fromJSON(n){return new et(n.done.map(Qe.fromJSON),n.undone.map(Qe.fromJSON))}});function Hc(n={}){return[Fc,Gc.of(n),v.domEventHandlers({beforeinput(e,t){let i=e.inputType=="historyUndo"?Kc:e.inputType=="historyRedo"?_r:null;return i?(e.preventDefault(),i(t)):!1}})]}function Ss(n,e){return function({state:t,dispatch:i}){if(!e&&t.readOnly)return!1;let s=t.field(Fc,!1);if(!s)return!1;let r=s.pop(n,t,e);return r?(i(r),!0):!1}}const Kc=Ss(0,!1),_r=Ss(1,!1),qm=Ss(0,!0),zm=Ss(1,!0);class Qe{constructor(e,t,i,s,r){this.changes=e,this.effects=t,this.mapped=i,this.startSelection=s,this.selectionsAfter=r}setSelAfter(e){return new Qe(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,i;return{changes:(e=this.changes)===null||e===void 0?void 0:e.toJSON(),mapped:(t=this.mapped)===null||t===void 0?void 0:t.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(s=>s.toJSON())}}static fromJSON(e){return new Qe(e.changes&&ne.fromJSON(e.changes),[],e.mapped&&it.fromJSON(e.mapped),e.startSelection&&y.fromJSON(e.startSelection),e.selectionsAfter.map(y.fromJSON))}static fromTransaction(e,t){let i=Xe;for(let s of e.startState.facet(Dm)){let r=s(e);r.length&&(i=i.concat(r))}return!i.length&&e.changes.empty?null:new Qe(e.changes.invert(e.startState.doc),i,void 0,t||e.startState.selection,Xe)}static selection(e){return new Qe(void 0,Xe,void 0,void 0,e)}}function ns(n,e,t,i){let s=e+1>t+20?e-t-1:0,r=n.slice(s,e);return r.push(i),r}function jm(n,e){let t=[],i=!1;return n.iterChangedRanges((s,r)=>t.push(s,r)),e.iterChangedRanges((s,r,o,l)=>{for(let a=0;a<t.length;){let h=t[a++],c=t[a++];l>=h&&o<=c&&(i=!0)}}),i}function Em(n,e){return n.ranges.length==e.ranges.length&&n.ranges.filter((t,i)=>t.empty!=e.ranges[i].empty).length===0}function Jc(n,e){return n.length?e.length?n.concat(e):n:e}const Xe=[],Vm=200;function ef(n,e){if(n.length){let t=n[n.length-1],i=t.selectionsAfter.slice(Math.max(0,t.selectionsAfter.length-Vm));return i.length&&i[i.length-1].eq(e)?n:(i.push(e),ns(n,n.length-1,1e9,t.setSelAfter(i)))}else return[Qe.selection([e])]}function Bm(n){let e=n[n.length-1],t=n.slice();return t[n.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),t}function Is(n,e){if(!n.length)return n;let t=n.length,i=Xe;for(;t;){let s=Um(n[t-1],e,i);if(s.changes&&!s.changes.empty||s.effects.length){let r=n.slice(0,t);return r[t-1]=s,r}else e=s.mapped,t--,i=s.selectionsAfter}return i.length?[Qe.selection(i)]:Xe}function Um(n,e,t){let i=Jc(n.selectionsAfter.length?n.selectionsAfter.map(l=>l.map(e)):Xe,t);if(!n.changes)return Qe.selection(i);let s=n.changes.map(e),r=e.mapDesc(n.changes,!0),o=n.mapped?n.mapped.composeDesc(r):r;return new Qe(s,M.mapEffects(n.effects,e),o,n.startSelection.map(r),i)}const Lm=/^(input\.type|delete)($|\.)/;class et{constructor(e,t,i=0,s=void 0){this.done=e,this.undone=t,this.prevTime=i,this.prevUserEvent=s}isolate(){return this.prevTime?new et(this.done,this.undone):this}addChanges(e,t,i,s,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&e.changes&&(!i||Lm.test(i))&&(!l.selectionsAfter.length&&t-this.prevTime<s.newGroupDelay&&s.joinToEvent(r,jm(l.changes,e.changes))||i=="input.type.compose")?o=ns(o,o.length-1,s.minDepth,new Qe(e.changes.compose(l.changes),Jc(e.effects,l.effects),l.mapped,l.startSelection,Xe)):o=ns(o,o.length,s.minDepth,e),new et(o,Xe,t,i)}addSelection(e,t,i,s){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Xe;return r.length>0&&t-this.prevTime<s&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&Em(r[r.length-1],e)?this:new et(ef(this.done,e),this.undone,t,i)}addMapping(e){return new et(Is(this.done,e),Is(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,i){let s=e==0?this.done:this.undone;if(s.length==0)return null;let r=s[s.length-1],o=r.selectionsAfter[0]||t.selection;if(i&&r.selectionsAfter.length)return t.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:Lr.of({side:e,rest:Bm(s),selection:o}),userEvent:e==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let l=s.length==1?Xe:s.slice(0,s.length-1);return r.mapped&&(l=Is(l,r.mapped)),t.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:Lr.of({side:e,rest:l,selection:o}),filter:!1,userEvent:e==0?"undo":"redo",scrollIntoView:!0})}else return null}}et.empty=new et(Xe,Xe);const tf=[{key:"Mod-z",run:Kc,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:_r,preventDefault:!0},{linux:"Ctrl-Shift-z",run:_r,preventDefault:!0},{key:"Mod-u",run:qm,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:zm,preventDefault:!0}];function ui(n,e){return y.create(n.ranges.map(e),n.mainIndex)}function rt(n,e){return n.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function Be({state:n,dispatch:e},t){let i=ui(n.selection,t);return i.eq(n.selection,!0)?!1:(e(rt(n,i)),!0)}function ws(n,e){return y.cursor(e?n.to:n.from)}function nf(n,e){return Be(n,t=>t.empty?n.moveByChar(t,e):ws(t,e))}function pe(n){return n.textDirectionAt(n.state.selection.main.head)==I.LTR}const sf=n=>nf(n,!pe(n)),rf=n=>nf(n,pe(n));function of(n,e){return Be(n,t=>t.empty?n.moveByGroup(t,e):ws(t,e))}const _m=n=>of(n,!pe(n)),Nm=n=>of(n,pe(n));function Im(n,e,t){if(e.type.prop(t))return!0;let i=e.to-e.from;return i&&(i>2||/[^\s,.;:]/.test(n.sliceDoc(e.from,e.to)))||e.firstChild}function Qs(n,e,t){let i=re(n).resolveInner(e.head),s=t?q.closedBy:q.openedBy;for(let a=e.head;;){let h=t?i.childAfter(a):i.childBefore(a);if(!h)break;Im(n,h,s)?i=h:a=t?h.to:h.from}let r=i.type.prop(s),o,l;return r&&(o=t?Je(n,i.from,1):Je(n,i.to,-1))&&o.matched?l=t?o.end.to:o.end.from:l=t?i.to:i.from,y.cursor(l,t?-1:1)}const Gm=n=>Be(n,e=>Qs(n.state,e,!pe(n))),Fm=n=>Be(n,e=>Qs(n.state,e,pe(n)));function lf(n,e){return Be(n,t=>{if(!t.empty)return ws(t,e);let i=n.moveVertically(t,e);return i.head!=t.head?i:n.moveToLineBoundary(t,e)})}const af=n=>lf(n,!1),hf=n=>lf(n,!0);function cf(n){let e=n.scrollDOM.clientHeight<n.scrollDOM.scrollHeight-2,t=0,i=0,s;if(e){for(let r of n.state.facet(v.scrollMargins)){let o=r(n);o!=null&&o.top&&(t=Math.max(o==null?void 0:o.top,t)),o!=null&&o.bottom&&(i=Math.max(o==null?void 0:o.bottom,i))}s=n.scrollDOM.clientHeight-t-i}else s=(n.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:t,marginBottom:i,selfScroll:e,height:Math.max(n.defaultLineHeight,s-5)}}function ff(n,e){let t=cf(n),{state:i}=n,s=ui(i.selection,o=>o.empty?n.moveVertically(o,e,t.height):ws(o,e));if(s.eq(i.selection))return!1;let r;if(t.selfScroll){let o=n.coordsAtPos(i.selection.main.head),l=n.scrollDOM.getBoundingClientRect(),a=l.top+t.marginTop,h=l.bottom-t.marginBottom;o&&o.top>a&&o.bottom<h&&(r=v.scrollIntoView(s.main.head,{y:"start",yMargin:o.top-a}))}return n.dispatch(rt(i,s),{effects:r}),!0}const ta=n=>ff(n,!1),Nr=n=>ff(n,!0);function $t(n,e,t){let i=n.lineBlockAt(e.head),s=n.moveToLineBoundary(e,t);if(s.head==e.head&&s.head!=(t?i.to:i.from)&&(s=n.moveToLineBoundary(e,t,!1)),!t&&s.head==i.from&&i.length){let r=/^\s*/.exec(n.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&e.head!=i.from+r&&(s=y.cursor(i.from+r))}return s}const Hm=n=>Be(n,e=>$t(n,e,!0)),Km=n=>Be(n,e=>$t(n,e,!1)),Jm=n=>Be(n,e=>$t(n,e,!pe(n))),eg=n=>Be(n,e=>$t(n,e,pe(n))),tg=n=>Be(n,e=>y.cursor(n.lineBlockAt(e.head).from,1)),ig=n=>Be(n,e=>y.cursor(n.lineBlockAt(e.head).to,-1));function ng(n,e,t){let i=!1,s=ui(n.selection,r=>{let o=Je(n,r.head,-1)||Je(n,r.head,1)||r.head>0&&Je(n,r.head-1,1)||r.head<n.doc.length&&Je(n,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return y.cursor(l)});return i?(e(rt(n,s)),!0):!1}const sg=({state:n,dispatch:e})=>ng(n,e);function qe(n,e){let t=ui(n.state.selection,i=>{let s=e(i);return y.range(i.anchor,s.head,s.goalColumn,s.bidiLevel||void 0)});return t.eq(n.state.selection)?!1:(n.dispatch(rt(n.state,t)),!0)}function uf(n,e){return qe(n,t=>n.moveByChar(t,e))}const df=n=>uf(n,!pe(n)),Of=n=>uf(n,pe(n));function pf(n,e){return qe(n,t=>n.moveByGroup(t,e))}const rg=n=>pf(n,!pe(n)),og=n=>pf(n,pe(n)),lg=n=>qe(n,e=>Qs(n.state,e,!pe(n))),ag=n=>qe(n,e=>Qs(n.state,e,pe(n)));function mf(n,e){return qe(n,t=>n.moveVertically(t,e))}const gf=n=>mf(n,!1),bf=n=>mf(n,!0);function yf(n,e){return qe(n,t=>n.moveVertically(t,e,cf(n).height))}const ia=n=>yf(n,!1),na=n=>yf(n,!0),hg=n=>qe(n,e=>$t(n,e,!0)),cg=n=>qe(n,e=>$t(n,e,!1)),fg=n=>qe(n,e=>$t(n,e,!pe(n))),ug=n=>qe(n,e=>$t(n,e,pe(n))),dg=n=>qe(n,e=>y.cursor(n.lineBlockAt(e.head).from)),Og=n=>qe(n,e=>y.cursor(n.lineBlockAt(e.head).to)),sa=({state:n,dispatch:e})=>(e(rt(n,{anchor:0})),!0),ra=({state:n,dispatch:e})=>(e(rt(n,{anchor:n.doc.length})),!0),oa=({state:n,dispatch:e})=>(e(rt(n,{anchor:n.selection.main.anchor,head:0})),!0),la=({state:n,dispatch:e})=>(e(rt(n,{anchor:n.selection.main.anchor,head:n.doc.length})),!0),pg=({state:n,dispatch:e})=>(e(n.update({selection:{anchor:0,head:n.doc.length},userEvent:"select"})),!0),mg=({state:n,dispatch:e})=>{let t=ks(n).map(({from:i,to:s})=>y.range(i,Math.min(s+1,n.doc.length)));return e(n.update({selection:y.create(t),userEvent:"select"})),!0},gg=({state:n,dispatch:e})=>{let t=ui(n.selection,i=>{var s;let r=re(n).resolveStack(i.from,1);for(let o=r;o;o=o.next){let{node:l}=o;if((l.from<i.from&&l.to>=i.to||l.to>i.to&&l.from<=i.from)&&(!((s=l.parent)===null||s===void 0)&&s.parent))return y.range(l.to,l.from)}return i});return e(rt(n,t)),!0},bg=({state:n,dispatch:e})=>{let t=n.selection,i=null;return t.ranges.length>1?i=y.create([t.main]):t.main.empty||(i=y.create([y.cursor(t.main.head)])),i?(e(rt(n,i)),!0):!1};function rn(n,e){if(n.state.readOnly)return!1;let t="delete.selection",{state:i}=n,s=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=e(r);a<o?(t="delete.backward",a=$n(n,a,!1)):a>o&&(t="delete.forward",a=$n(n,a,!0)),o=Math.min(o,a),l=Math.max(l,a)}else o=$n(n,o,!1),l=$n(n,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:y.cursor(o,o<r.head?-1:1)}});return s.changes.empty?!1:(n.dispatch(i.update(s,{scrollIntoView:!0,userEvent:t,effects:t=="delete.selection"?v.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function $n(n,e,t){if(n instanceof v)for(let i of n.state.facet(v.atomicRanges).map(s=>s(n)))i.between(e,e,(s,r)=>{s<e&&r>e&&(e=t?r:s)});return e}const xf=(n,e,t)=>rn(n,i=>{let s=i.from,{state:r}=n,o=r.doc.lineAt(s),l,a;if(t&&!e&&s>o.from&&s<o.from+200&&!/[^ \t]/.test(l=o.text.slice(0,s-o.from))){if(l[l.length-1]=="	")return s-1;let h=ci(l,r.tabSize),c=h%es(r)||es(r);for(let f=0;f<c&&l[l.length-1-f]==" ";f++)s--;a=s}else a=ue(o.text,s-o.from,e,e)+o.from,a==s&&o.number!=(e?r.doc.lines:1)?a+=e?1:-1:!e&&/[\ufe00-\ufe0f]/.test(o.text.slice(a-o.from,s-o.from))&&(a=ue(o.text,a-o.from,!1,!1)+o.from);return a}),Ir=n=>xf(n,!1,!0),Sf=n=>xf(n,!0,!1),wf=(n,e)=>rn(n,t=>{let i=t.head,{state:s}=n,r=s.doc.lineAt(i),o=s.charCategorizer(i);for(let l=null;;){if(i==(e?r.to:r.from)){i==t.head&&r.number!=(e?s.doc.lines:1)&&(i+=e?1:-1);break}let a=ue(r.text,i-r.from,e)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),c=o(h);if(l!=null&&c!=l)break;(h!=" "||i!=t.head)&&(l=c),i=a}return i}),Qf=n=>wf(n,!1),yg=n=>wf(n,!0),xg=n=>rn(n,e=>{let t=n.lineBlockAt(e.head).to;return e.head<t?t:Math.min(n.state.doc.length,e.head+1)}),Sg=n=>rn(n,e=>{let t=n.moveToLineBoundary(e,!1).head;return e.head>t?t:Math.max(0,e.head-1)}),wg=n=>rn(n,e=>{let t=n.moveToLineBoundary(e,!0).head;return e.head<t?t:Math.min(n.state.doc.length,e.head+1)}),Qg=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:B.of(["",""])},range:y.cursor(i.from)}));return e(n.update(t,{scrollIntoView:!0,userEvent:"input"})),!0},kg=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>{if(!i.empty||i.from==0||i.from==n.doc.length)return{range:i};let s=i.from,r=n.doc.lineAt(s),o=s==r.from?s-1:ue(r.text,s-r.from,!1)+r.from,l=s==r.to?s+1:ue(r.text,s-r.from,!0)+r.from;return{changes:{from:o,to:l,insert:n.doc.slice(s,l).append(n.doc.slice(o,s))},range:y.cursor(l)}});return t.changes.empty?!1:(e(n.update(t,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function ks(n){let e=[],t=-1;for(let i of n.selection.ranges){let s=n.doc.lineAt(i.from),r=n.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=n.doc.lineAt(i.to-1)),t>=s.number){let o=e[e.length-1];o.to=r.to,o.ranges.push(i)}else e.push({from:s.from,to:r.to,ranges:[i]});t=r.number+1}return e}function kf(n,e,t){if(n.readOnly)return!1;let i=[],s=[];for(let r of ks(n)){if(t?r.to==n.doc.length:r.from==0)continue;let o=n.doc.lineAt(t?r.to+1:r.from-1),l=o.length+1;if(t){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+n.lineBreak});for(let a of r.ranges)s.push(y.range(Math.min(n.doc.length,a.anchor+l),Math.min(n.doc.length,a.head+l)))}else{i.push({from:o.from,to:r.from},{from:r.to,insert:n.lineBreak+o.text});for(let a of r.ranges)s.push(y.range(a.anchor-l,a.head-l))}}return i.length?(e(n.update({changes:i,scrollIntoView:!0,selection:y.create(s,n.selection.mainIndex),userEvent:"move.line"})),!0):!1}const Pg=({state:n,dispatch:e})=>kf(n,e,!1),$g=({state:n,dispatch:e})=>kf(n,e,!0);function Pf(n,e,t){if(n.readOnly)return!1;let i=[];for(let s of ks(n))t?i.push({from:s.from,insert:n.doc.slice(s.from,s.to)+n.lineBreak}):i.push({from:s.to,insert:n.lineBreak+n.doc.slice(s.from,s.to)});return e(n.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const vg=({state:n,dispatch:e})=>Pf(n,e,!1),Zg=({state:n,dispatch:e})=>Pf(n,e,!0),Cg=n=>{if(n.state.readOnly)return!1;let{state:e}=n,t=e.changes(ks(e).map(({from:s,to:r})=>(s>0?s--:r<e.doc.length&&r++,{from:s,to:r}))),i=ui(e.selection,s=>{let r;if(n.lineWrapping){let o=n.lineBlockAt(s.head),l=n.coordsAtPos(s.head,s.assoc||1);l&&(r=o.bottom+n.documentTop-l.bottom+n.defaultLineHeight/2)}return n.moveVertically(s,!0,r)}).map(t);return n.dispatch({changes:t,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function Tg(n,e){if(/\(\)|\[\]|\{\}/.test(n.sliceDoc(e-1,e+1)))return{from:e,to:e};let t=re(n).resolveInner(e),i=t.childBefore(e),s=t.childAfter(e),r;return i&&s&&i.to<=e&&s.from>=e&&(r=i.type.prop(q.closedBy))&&r.indexOf(s.name)>-1&&n.doc.lineAt(i.to).from==n.doc.lineAt(s.from).from&&!/\S/.test(n.sliceDoc(i.to,s.from))?{from:i.to,to:s.from}:null}const Rg=$f(!1),Ag=$f(!0);function $f(n){return({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(s=>{let{from:r,to:o}=s,l=e.doc.lineAt(r),a=!n&&r==o&&Tg(e,r);n&&(r=o=(o<=l.to?l:e.doc.lineAt(o)).to);let h=new ys(e,{simulateBreak:r,simulateDoubleBreak:!!a}),c=xo(h,r);for(c==null&&(c=ci(/^\s*/.exec(e.doc.lineAt(r).text)[0],e.tabSize));o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let f=["",Li(e,c)];return a&&f.push(Li(e,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:B.of(f)},range:y.cursor(r+1+f[1].length)}});return t(e.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function vo(n,e){let t=-1;return n.changeByRange(i=>{let s=[];for(let o=i.from;o<=i.to;){let l=n.doc.lineAt(o);l.number>t&&(i.empty||i.to>l.from)&&(e(l,s,i),t=l.number),o=l.to+1}let r=n.changes(s);return{changes:s,range:y.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const Mg=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=Object.create(null),i=new ys(n,{overrideIndentation:r=>{let o=t[r];return o??-1}}),s=vo(n,(r,o,l)=>{let a=xo(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],c=Li(n,a);(h!=c||l.from<r.from+h.length)&&(t[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:c}))});return s.changes.empty||e(n.update(s,{userEvent:"indent"})),!0},vf=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(vo(n,(t,i)=>{i.push({from:t.from,insert:n.facet(tn)})}),{userEvent:"input.indent"})),!0),Zf=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(vo(n,(t,i)=>{let s=/^\s*/.exec(t.text)[0];if(!s)return;let r=ci(s,n.tabSize),o=0,l=Li(n,Math.max(0,r-es(n)));for(;o<s.length&&o<l.length&&s.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:t.from+o,to:t.from+s.length,insert:l.slice(o)})}),{userEvent:"delete.dedent"})),!0),Wg=[{key:"Ctrl-b",run:sf,shift:df,preventDefault:!0},{key:"Ctrl-f",run:rf,shift:Of},{key:"Ctrl-p",run:af,shift:gf},{key:"Ctrl-n",run:hf,shift:bf},{key:"Ctrl-a",run:tg,shift:dg},{key:"Ctrl-e",run:ig,shift:Og},{key:"Ctrl-d",run:Sf},{key:"Ctrl-h",run:Ir},{key:"Ctrl-k",run:xg},{key:"Ctrl-Alt-h",run:Qf},{key:"Ctrl-o",run:Qg},{key:"Ctrl-t",run:kg},{key:"Ctrl-v",run:Nr}],Xg=[{key:"ArrowLeft",run:sf,shift:df,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:_m,shift:rg,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:Jm,shift:fg,preventDefault:!0},{key:"ArrowRight",run:rf,shift:Of,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:Nm,shift:og,preventDefault:!0},{mac:"Cmd-ArrowRight",run:eg,shift:ug,preventDefault:!0},{key:"ArrowUp",run:af,shift:gf,preventDefault:!0},{mac:"Cmd-ArrowUp",run:sa,shift:oa},{mac:"Ctrl-ArrowUp",run:ta,shift:ia},{key:"ArrowDown",run:hf,shift:bf,preventDefault:!0},{mac:"Cmd-ArrowDown",run:ra,shift:la},{mac:"Ctrl-ArrowDown",run:Nr,shift:na},{key:"PageUp",run:ta,shift:ia},{key:"PageDown",run:Nr,shift:na},{key:"Home",run:Km,shift:cg,preventDefault:!0},{key:"Mod-Home",run:sa,shift:oa},{key:"End",run:Hm,shift:hg,preventDefault:!0},{key:"Mod-End",run:ra,shift:la},{key:"Enter",run:Rg},{key:"Mod-a",run:pg},{key:"Backspace",run:Ir,shift:Ir},{key:"Delete",run:Sf},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Qf},{key:"Mod-Delete",mac:"Alt-Delete",run:yg},{mac:"Mod-Backspace",run:Sg},{mac:"Mod-Delete",run:wg}].concat(Wg.map(n=>({mac:n.key,run:n.run,shift:n.shift}))),Cf=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:Gm,shift:lg},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:Fm,shift:ag},{key:"Alt-ArrowUp",run:Pg},{key:"Shift-Alt-ArrowUp",run:vg},{key:"Alt-ArrowDown",run:$g},{key:"Shift-Alt-ArrowDown",run:Zg},{key:"Escape",run:bg},{key:"Mod-Enter",run:Ag},{key:"Alt-l",mac:"Ctrl-l",run:mg},{key:"Mod-i",run:gg,preventDefault:!0},{key:"Mod-[",run:Zf},{key:"Mod-]",run:vf},{key:"Mod-Alt-\\",run:Mg},{key:"Shift-Mod-k",run:Cg},{key:"Shift-Mod-\\",run:sg},{key:"Mod-/",run:Cm},{key:"Alt-A",run:Rm}].concat(Xg),Yg={key:"Tab",run:vf,shift:Zf};function _(){var n=arguments[0];typeof n=="string"&&(n=document.createElement(n));var e=1,t=arguments[1];if(t&&typeof t=="object"&&t.nodeType==null&&!Array.isArray(t)){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var s=t[i];typeof s=="string"?n.setAttribute(i,s):s!=null&&(n[i]=s)}e++}for(;e<arguments.length;e++)Tf(n,arguments[e]);return n}function Tf(n,e){if(typeof e=="string")n.appendChild(document.createTextNode(e));else if(e!=null)if(e.nodeType!=null)n.appendChild(e);else if(Array.isArray(e))for(var t=0;t<e.length;t++)Tf(n,e[t]);else throw new RangeError("Unsupported child node: "+e)}class Dg{constructor(e,t,i){this.from=e,this.to=t,this.diagnostic=i}}class Rt{constructor(e,t,i){this.diagnostics=e,this.panel=t,this.selected=i}static init(e,t,i){let s=e,r=i.facet(tt).markerFilter;r&&(s=r(s,i));let o=A.set(s.map(l=>l.from==l.to||l.from==l.to-1&&i.doc.lineAt(l.from).to==l.from?A.widget({widget:new _g(l),diagnostic:l}).range(l.from):A.mark({attributes:{class:"cm-lintRange cm-lintRange-"+l.severity+(l.markClass?" "+l.markClass:"")},diagnostic:l,inclusive:!0}).range(l.from,l.to)),!0);return new Rt(o,t,ai(o))}}function ai(n,e=null,t=0){let i=null;return n.between(t,1e9,(s,r,{spec:o})=>{if(!(e&&o.diagnostic!=e))return i=new Dg(s,r,o.diagnostic),!1}),i}function Rf(n,e){let t=e.pos,i=e.end||t,s=n.state.facet(tt).hideOn(n,t,i);if(s!=null)return s;let r=n.startState.doc.lineAt(e.pos);return!!(n.effects.some(o=>o.is(Ps))||n.changes.touchesRange(r.from,Math.max(r.to,i)))}function Af(n,e){return n.field(ke,!1)?e:e.concat(M.appendConfig.of(jf))}function qg(n,e){return{effects:Af(n,[Ps.of(e)])}}const Ps=M.define(),Zo=M.define(),Mf=M.define(),ke=ee.define({create(){return new Rt(A.none,null,null)},update(n,e){if(e.docChanged&&n.diagnostics.size){let t=n.diagnostics.map(e.changes),i=null,s=n.panel;if(n.selected){let r=e.changes.mapPos(n.selected.from,1);i=ai(t,n.selected.diagnostic,r)||ai(t,null,r)}!t.size&&s&&e.state.facet(tt).autoPanel&&(s=null),n=new Rt(t,s,i)}for(let t of e.effects)if(t.is(Ps)){let i=e.state.facet(tt).autoPanel?t.value.length?_i.open:null:n.panel;n=Rt.init(t.value,i,e.state)}else t.is(Zo)?n=new Rt(n.diagnostics,t.value?_i.open:null,n.selected):t.is(Mf)&&(n=new Rt(n.diagnostics,n.panel,t.value));return n},provide:n=>[Vi.from(n,e=>e.panel),v.decorations.from(n,e=>e.diagnostics)]});function zg(n){let e=n.field(ke,!1);return e?e.diagnostics.size:0}const jg=A.mark({class:"cm-lintRange cm-lintRange-active",inclusive:!0});function Eg(n,e,t){let{diagnostics:i}=n.state.field(ke),s=[],r=2e8,o=0;i.between(e-(t<0?1:0),e+(t>0?1:0),(a,h,{spec:c})=>{e>=a&&e<=h&&(a==h||(e>a||t>0)&&(e<h||t<0))&&(s.push(c.diagnostic),r=Math.min(a,r),o=Math.max(h,o))});let l=n.state.facet(tt).tooltipFilter;return l&&(s=l(s,n.state)),s.length?{pos:r,end:o,above:n.state.doc.lineAt(r).to<o,create(){return{dom:Wf(n,s)}}}:null}function Wf(n,e){return _("ul",{class:"cm-tooltip-lint"},e.map(t=>Df(n,t,!1)))}const Vg=n=>{let e=n.state.field(ke,!1);(!e||!e.panel)&&n.dispatch({effects:Af(n.state,[Zo.of(!0)])});let t=Ei(n,_i.open);return t&&t.dom.querySelector(".cm-panel-lint ul").focus(),!0},aa=n=>{let e=n.state.field(ke,!1);return!e||!e.panel?!1:(n.dispatch({effects:Zo.of(!1)}),!0)},Bg=n=>{let e=n.state.field(ke,!1);if(!e)return!1;let t=n.state.selection.main,i=e.diagnostics.iter(t.to+1);return!i.value&&(i=e.diagnostics.iter(0),!i.value||i.from==t.from&&i.to==t.to)?!1:(n.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},Ug=[{key:"Mod-Shift-m",run:Vg,preventDefault:!0},{key:"F8",run:Bg}],Xf=F.fromClass(class{constructor(n){this.view=n,this.timeout=-1,this.set=!0;let{delay:e}=n.state.facet(tt);this.lintTime=Date.now()+e,this.run=this.run.bind(this),this.timeout=setTimeout(this.run,e)}run(){clearTimeout(this.timeout);let n=Date.now();if(n<this.lintTime-10)this.timeout=setTimeout(this.run,this.lintTime-n);else{this.set=!1;let{state:e}=this.view,{sources:t}=e.facet(tt);t.length&&Promise.all(t.map(i=>Promise.resolve(i(this.view)))).then(i=>{let s=i.reduce((r,o)=>r.concat(o));this.view.state.doc==e.doc&&this.view.dispatch(qg(this.view.state,s))},i=>{ge(this.view.state,i)})}}update(n){let e=n.state.facet(tt);(n.docChanged||e!=n.startState.facet(tt)||e.needsRefresh&&e.needsRefresh(n))&&(this.lintTime=Date.now()+e.delay,this.set||(this.set=!0,this.timeout=setTimeout(this.run,e.delay)))}force(){this.set&&(this.lintTime=Date.now(),this.run())}destroy(){clearTimeout(this.timeout)}}),tt=C.define({combine(n){return Object.assign({sources:n.map(e=>e.source).filter(e=>e!=null)},Ve(n.map(e=>e.config),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(e,t)=>e?t?i=>e(i)||t(i):e:t}))}});function Lg(n,e={}){return[tt.of({source:n,config:e}),Xf,jf]}function ha(n){let e=n.plugin(Xf);e&&e.force()}function Yf(n){let e=[];if(n)e:for(let{name:t}of n){for(let i=0;i<t.length;i++){let s=t[i];if(/[a-zA-Z]/.test(s)&&!e.some(r=>r.toLowerCase()==s.toLowerCase())){e.push(s);continue e}}e.push("")}return e}function Df(n,e,t){var i;let s=t?Yf(e.actions):[];return _("li",{class:"cm-diagnostic cm-diagnostic-"+e.severity},_("span",{class:"cm-diagnosticText"},e.renderMessage?e.renderMessage(n):e.message),(i=e.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=!1,a=u=>{if(u.preventDefault(),l)return;l=!0;let d=ai(n.state.field(ke).diagnostics,e);d&&r.apply(n,d.from,d.to)},{name:h}=r,c=s[o]?h.indexOf(s[o]):-1,f=c<0?h:[h.slice(0,c),_("u",h.slice(c,c+1)),h.slice(c+1)];return _("button",{type:"button",class:"cm-diagnosticAction",onclick:a,onmousedown:a,"aria-label":` Action: ${h}${c<0?"":` (access key "${s[o]})"`}.`},f)}),e.source&&_("div",{class:"cm-diagnosticSource"},e.source))}class _g extends st{constructor(e){super(),this.diagnostic=e}eq(e){return e.diagnostic==this.diagnostic}toDOM(){return _("span",{class:"cm-lintPoint cm-lintPoint-"+this.diagnostic.severity})}}class ca{constructor(e,t){this.diagnostic=t,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=Df(e,t,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class _i{constructor(e){this.view=e,this.items=[];let t=s=>{if(s.keyCode==27)aa(this.view),this.view.focus();else if(s.keyCode==38||s.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(s.keyCode==40||s.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(s.keyCode==36)this.moveSelection(0);else if(s.keyCode==35)this.moveSelection(this.items.length-1);else if(s.keyCode==13)this.view.focus();else if(s.keyCode>=65&&s.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=Yf(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==s.keyCode){let a=ai(this.view.state.field(ke).diagnostics,r);a&&r.actions[l].apply(e,a.from,a.to)}}else return;s.preventDefault()},i=s=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(s.target)&&this.moveSelection(r)};this.list=_("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:t,onclick:i}),this.dom=_("div",{class:"cm-panel-lint"},this.list,_("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>aa(this.view)},"×")),this.update()}get selectedIndex(){let e=this.view.state.field(ke).selected;if(!e)return-1;for(let t=0;t<this.items.length;t++)if(this.items[t].diagnostic==e.diagnostic)return t;return-1}update(){let{diagnostics:e,selected:t}=this.view.state.field(ke),i=0,s=!1,r=null;for(e.between(0,this.view.state.doc.length,(o,l,{spec:a})=>{let h=-1,c;for(let f=i;f<this.items.length;f++)if(this.items[f].diagnostic==a.diagnostic){h=f;break}h<0?(c=new ca(this.view,a.diagnostic),this.items.splice(i,0,c),s=!0):(c=this.items[h],h>i&&(this.items.splice(i,h-i),s=!0)),t&&c.diagnostic==t.diagnostic?c.dom.hasAttribute("aria-selected")||(c.dom.setAttribute("aria-selected","true"),r=c):c.dom.hasAttribute("aria-selected")&&c.dom.removeAttribute("aria-selected"),i++});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)s=!0,this.items.pop();this.items.length==0&&(this.items.push(new ca(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),s=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:o,panel:l})=>{let a=l.height/this.list.offsetHeight;o.top<l.top?this.list.scrollTop-=(l.top-o.top)/a:o.bottom>l.bottom&&(this.list.scrollTop+=(o.bottom-l.bottom)/a)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),s&&this.sync()}sync(){let e=this.list.firstChild;function t(){let i=e;e=i.nextSibling,i.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;e!=i.dom;)t();e=i.dom.nextSibling}else this.list.insertBefore(i.dom,e);for(;e;)t()}moveSelection(e){if(this.selectedIndex<0)return;let t=this.view.state.field(ke),i=ai(t.diagnostics,this.items[e].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:Mf.of(i)})}static open(e){return new _i(e)}}function jn(n,e='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${e}>${encodeURIComponent(n)}</svg>')`}function vn(n){return jn(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${n}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const Ng=v.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:vn("#d11")},".cm-lintRange-warning":{backgroundImage:vn("orange")},".cm-lintRange-info":{backgroundImage:vn("#999")},".cm-lintRange-hint":{backgroundImage:vn("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});function fa(n){return n=="error"?4:n=="warning"?3:n=="info"?2:1}class Ig extends nt{constructor(e){super(),this.diagnostics=e,this.severity=e.reduce((t,i)=>fa(t)<fa(i.severity)?i.severity:t,"hint")}toDOM(e){let t=document.createElement("div");t.className="cm-lint-marker cm-lint-marker-"+this.severity;let i=this.diagnostics,s=e.state.facet($s).tooltipFilter;return s&&(i=s(i,e.state)),i.length&&(t.onmouseover=()=>Fg(e,t,i)),t}}function Gg(n,e){let t=i=>{let s=e.getBoundingClientRect();if(!(i.clientX>s.left-10&&i.clientX<s.right+10&&i.clientY>s.top-10&&i.clientY<s.bottom+10)){for(let r=i.target;r;r=r.parentNode)if(r.nodeType==1&&r.classList.contains("cm-tooltip-lint"))return;window.removeEventListener("mousemove",t),n.state.field(zf)&&n.dispatch({effects:Co.of(null)})}};window.addEventListener("mousemove",t)}function Fg(n,e,t){function i(){let o=n.elementAtHeight(e.getBoundingClientRect().top+5-n.documentTop);n.coordsAtPos(o.from)&&n.dispatch({effects:Co.of({pos:o.from,above:!1,create(){return{dom:Wf(n,t),getCoords:()=>e.getBoundingClientRect()}}})}),e.onmouseout=e.onmousemove=null,Gg(n,e)}let{hoverTime:s}=n.state.facet($s),r=setTimeout(i,s);e.onmouseout=()=>{clearTimeout(r),e.onmouseout=e.onmousemove=null},e.onmousemove=()=>{clearTimeout(r),r=setTimeout(i,s)}}function Hg(n,e){let t=Object.create(null);for(let s of e){let r=n.lineAt(s.from);(t[r.from]||(t[r.from]=[])).push(s)}let i=[];for(let s in t)i.push(new Ig(t[s]).range(+s));return E.of(i,!0)}const Kg=xc({class:"cm-gutter-lint",markers:n=>n.state.field(qf)}),qf=ee.define({create(){return E.empty},update(n,e){n=n.map(e.changes);let t=e.state.facet($s).markerFilter;for(let i of e.effects)if(i.is(Ps)){let s=i.value;t&&(s=t(s||[],e.state)),n=Hg(e.state.doc,s.slice(0))}return n}}),Co=M.define(),zf=ee.define({create(){return null},update(n,e){return n&&e.docChanged&&(n=Rf(e,n)?null:Object.assign(Object.assign({},n),{pos:e.changes.mapPos(n.pos)})),e.effects.reduce((t,i)=>i.is(Co)?i.value:t,n)},provide:n=>gs.from(n)}),Jg=v.baseTheme({".cm-gutter-lint":{width:"1.4em","& .cm-gutterElement":{padding:".2em"}},".cm-lint-marker":{width:"1em",height:"1em"},".cm-lint-marker-info":{content:jn('<path fill="#aaf" stroke="#77e" stroke-width="6" stroke-linejoin="round" d="M5 5L35 5L35 35L5 35Z"/>')},".cm-lint-marker-warning":{content:jn('<path fill="#fe8" stroke="#fd7" stroke-width="6" stroke-linejoin="round" d="M20 6L37 35L3 35Z"/>')},".cm-lint-marker-error":{content:jn('<circle cx="20" cy="20" r="15" fill="#f87" stroke="#f43" stroke-width="6"/>')}}),jf=[ke,v.decorations.compute([ke],n=>{let{selected:e,panel:t}=n.field(ke);return!e||!t||e.from==e.to?A.none:A.set([jg.range(e.from,e.to)])}),dp(Eg,{hideOn:Rf}),Ng],$s=C.define({combine(n){return Ve(n,{hoverTime:300,markerFilter:null,tooltipFilter:null})}});function e0(n={}){return[$s.of(n),qf,Kg,Jg,zf]}const ua=typeof String.prototype.normalize=="function"?n=>n.normalize("NFKD"):n=>n;class hi{constructor(e,t,i=0,s=e.length,r,o){this.test=o,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=e.iterRange(i,s),this.bufferStart=i,this.normalize=r?l=>r(ua(l)):ua,this.query=this.normalize(t)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return ae(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let e=this.peek();if(e<0)return this.done=!0,this;let t=io(e),i=this.bufferStart+this.bufferPos;this.bufferPos+=We(e);let s=this.normalize(t);for(let r=0,o=i;;r++){let l=s.charCodeAt(r),a=this.match(l,o,this.bufferPos+this.bufferStart);if(r==s.length-1){if(a)return this.value=a,this;break}o==i&&r<t.length&&t.charCodeAt(r)==l&&o++}}}match(e,t,i){let s=null;for(let r=0;r<this.matches.length;r+=2){let o=this.matches[r],l=!1;this.query.charCodeAt(o)==e&&(o==this.query.length-1?s={from:this.matches[r+1],to:i}:(this.matches[r]++,l=!0)),l||(this.matches.splice(r,2),r-=2)}return this.query.charCodeAt(0)==e&&(this.query.length==1?s={from:t,to:i}:this.matches.push(1,t)),s&&this.test&&!this.test(s.from,s.to,this.buffer,this.bufferStart)&&(s=null),s}}typeof Symbol<"u"&&(hi.prototype[Symbol.iterator]=function(){return this});const Ef={from:-1,to:-1,match:/.*/.exec("")},To="gm"+(/x/.unicode==null?"":"u");class Vf{constructor(e,t,i,s=0,r=e.length){if(this.text=e,this.to=r,this.curLine="",this.done=!1,this.value=Ef,/\\[sWDnr]|\n|\r|\[\^/.test(t))return new Bf(e,t,i,s,r);this.re=new RegExp(t,To+(i!=null&&i.ignoreCase?"i":"")),this.test=i==null?void 0:i.test,this.iter=e.iter();let o=e.lineAt(s);this.curLineStart=o.from,this.matchPos=ss(e,s),this.getLine(this.curLineStart)}getLine(e){this.iter.next(e),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let e=this.matchPos-this.curLineStart;;){this.re.lastIndex=e;let t=this.matchPos<=this.to&&this.re.exec(this.curLine);if(t){let i=this.curLineStart+t.index,s=i+t[0].length;if(this.matchPos=ss(this.text,s+(i==s?1:0)),i==this.curLineStart+this.curLine.length&&this.nextLine(),(i<s||i>this.value.to)&&(!this.test||this.test(i,s,t)))return this.value={from:i,to:s,match:t},this;e=this.matchPos-this.curLineStart}else if(this.curLineStart+this.curLine.length<this.to)this.nextLine(),e=0;else return this.done=!0,this}}}const Gs=new WeakMap;class Jt{constructor(e,t){this.from=e,this.text=t}get to(){return this.from+this.text.length}static get(e,t,i){let s=Gs.get(e);if(!s||s.from>=i||s.to<=t){let l=new Jt(t,e.sliceString(t,i));return Gs.set(e,l),l}if(s.from==t&&s.to==i)return s;let{text:r,from:o}=s;return o>t&&(r=e.sliceString(t,o)+r,o=t),s.to<i&&(r+=e.sliceString(s.to,i)),Gs.set(e,new Jt(o,r)),new Jt(t,r.slice(t-o,i-o))}}class Bf{constructor(e,t,i,s,r){this.text=e,this.to=r,this.done=!1,this.value=Ef,this.matchPos=ss(e,s),this.re=new RegExp(t,To+(i!=null&&i.ignoreCase?"i":"")),this.test=i==null?void 0:i.test,this.flat=Jt.get(e,s,this.chunkEnd(s+5e3))}chunkEnd(e){return e>=this.to?this.to:this.text.lineAt(e).to}next(){for(;;){let e=this.re.lastIndex=this.matchPos-this.flat.from,t=this.re.exec(this.flat.text);if(t&&!t[0]&&t.index==e&&(this.re.lastIndex=e+1,t=this.re.exec(this.flat.text)),t){let i=this.flat.from+t.index,s=i+t[0].length;if((this.flat.to>=this.to||t.index+t[0].length<=this.flat.text.length-10)&&(!this.test||this.test(i,s,t)))return this.value={from:i,to:s,match:t},this.matchPos=ss(this.text,s+(i==s?1:0)),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=Jt.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+this.flat.text.length*2))}}}typeof Symbol<"u"&&(Vf.prototype[Symbol.iterator]=Bf.prototype[Symbol.iterator]=function(){return this});function t0(n){try{return new RegExp(n,To),!0}catch{return!1}}function ss(n,e){if(e>=n.length)return e;let t=n.lineAt(e),i;for(;e<t.to&&(i=t.text.charCodeAt(e-t.from))>=56320&&i<57344;)e++;return e}function Gr(n){let e=String(n.state.doc.lineAt(n.state.selection.main.head).number),t=_("input",{class:"cm-textfield",name:"line",value:e}),i=_("form",{class:"cm-gotoLine",onkeydown:r=>{r.keyCode==27?(r.preventDefault(),n.dispatch({effects:rs.of(!1)}),n.focus()):r.keyCode==13&&(r.preventDefault(),s())},onsubmit:r=>{r.preventDefault(),s()}},_("label",n.state.phrase("Go to line"),": ",t)," ",_("button",{class:"cm-button",type:"submit"},n.state.phrase("go")));function s(){let r=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(t.value);if(!r)return;let{state:o}=n,l=o.doc.lineAt(o.selection.main.head),[,a,h,c,f]=r,u=c?+c.slice(1):0,d=h?+h:l.number;if(h&&f){let g=d/100;a&&(g=g*(a=="-"?-1:1)+l.number/o.doc.lines),d=Math.round(o.doc.lines*g)}else h&&a&&(d=d*(a=="-"?-1:1)+l.number);let O=o.doc.line(Math.max(1,Math.min(o.doc.lines,d))),m=y.cursor(O.from+Math.max(0,Math.min(u,O.length)));n.dispatch({effects:[rs.of(!1),v.scrollIntoView(m.from,{y:"center"})],selection:m}),n.focus()}return{dom:i}}const rs=M.define(),da=ee.define({create(){return!0},update(n,e){for(let t of e.effects)t.is(rs)&&(n=t.value);return n},provide:n=>Vi.from(n,e=>e?Gr:null)}),i0=n=>{let e=Ei(n,Gr);if(!e){let t=[rs.of(!0)];n.state.field(da,!1)==null&&t.push(M.appendConfig.of([da,n0])),n.dispatch({effects:t}),e=Ei(n,Gr)}return e&&e.dom.querySelector("input").select(),!0},n0=v.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px","& label":{fontSize:"80%"}}}),s0={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},r0=C.define({combine(n){return Ve(n,s0,{highlightWordAroundCursor:(e,t)=>e||t,minSelectionLength:Math.min,maxMatches:Math.min})}});function o0(n){return[f0,c0]}const l0=A.mark({class:"cm-selectionMatch"}),a0=A.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function Oa(n,e,t,i){return(t==0||n(e.sliceDoc(t-1,t))!=G.Word)&&(i==e.doc.length||n(e.sliceDoc(i,i+1))!=G.Word)}function h0(n,e,t,i){return n(e.sliceDoc(t,t+1))==G.Word&&n(e.sliceDoc(i-1,i))==G.Word}const c0=F.fromClass(class{constructor(n){this.decorations=this.getDeco(n)}update(n){(n.selectionSet||n.docChanged||n.viewportChanged)&&(this.decorations=this.getDeco(n.view))}getDeco(n){let e=n.state.facet(r0),{state:t}=n,i=t.selection;if(i.ranges.length>1)return A.none;let s=i.main,r,o=null;if(s.empty){if(!e.highlightWordAroundCursor)return A.none;let a=t.wordAt(s.head);if(!a)return A.none;o=t.charCategorizer(s.head),r=t.sliceDoc(a.from,a.to)}else{let a=s.to-s.from;if(a<e.minSelectionLength||a>200)return A.none;if(e.wholeWords){if(r=t.sliceDoc(s.from,s.to),o=t.charCategorizer(s.head),!(Oa(o,t,s.from,s.to)&&h0(o,t,s.from,s.to)))return A.none}else if(r=t.sliceDoc(s.from,s.to),!r)return A.none}let l=[];for(let a of n.visibleRanges){let h=new hi(t.doc,r,a.from,a.to);for(;!h.next().done;){let{from:c,to:f}=h.value;if((!o||Oa(o,t,c,f))&&(s.empty&&c<=s.from&&f>=s.to?l.push(a0.range(c,f)):(c>=s.to||f<=s.from)&&l.push(l0.range(c,f)),l.length>e.maxMatches))return A.none}}return A.set(l)}},{decorations:n=>n.decorations}),f0=v.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}}),u0=({state:n,dispatch:e})=>{let{selection:t}=n,i=y.create(t.ranges.map(s=>n.wordAt(s.head)||y.cursor(s.head)),t.mainIndex);return i.eq(t)?!1:(e(n.update({selection:i})),!0)};function d0(n,e){let{main:t,ranges:i}=n.selection,s=n.wordAt(t.head),r=s&&s.from==t.from&&s.to==t.to;for(let o=!1,l=new hi(n.doc,e,i[i.length-1].to);;)if(l.next(),l.done){if(o)return null;l=new hi(n.doc,e,0,Math.max(0,i[i.length-1].from-1)),o=!0}else{if(o&&i.some(a=>a.from==l.value.from))continue;if(r){let a=n.wordAt(l.value.from);if(!a||a.from!=l.value.from||a.to!=l.value.to)continue}return l.value}}const O0=({state:n,dispatch:e})=>{let{ranges:t}=n.selection;if(t.some(r=>r.from===r.to))return u0({state:n,dispatch:e});let i=n.sliceDoc(t[0].from,t[0].to);if(n.selection.ranges.some(r=>n.sliceDoc(r.from,r.to)!=i))return!1;let s=d0(n,i);return s?(e(n.update({selection:n.selection.addRange(y.range(s.from,s.to),!1),effects:v.scrollIntoView(s.to)})),!0):!1},di=C.define({combine(n){return Ve(n,{top:!1,caseSensitive:!1,literal:!1,regexp:!1,wholeWord:!1,createPanel:e=>new $0(e),scrollToMatch:e=>v.scrollIntoView(e)})}});class Uf{constructor(e){this.search=e.search,this.caseSensitive=!!e.caseSensitive,this.literal=!!e.literal,this.regexp=!!e.regexp,this.replace=e.replace||"",this.valid=!!this.search&&(!this.regexp||t0(this.search)),this.unquoted=this.unquote(this.search),this.wholeWord=!!e.wholeWord}unquote(e){return this.literal?e:e.replace(/\\([nrt\\])/g,(t,i)=>i=="n"?`
`:i=="r"?"\r":i=="t"?"	":"\\")}eq(e){return this.search==e.search&&this.replace==e.replace&&this.caseSensitive==e.caseSensitive&&this.regexp==e.regexp&&this.wholeWord==e.wholeWord}create(){return this.regexp?new b0(this):new m0(this)}getCursor(e,t=0,i){let s=e.doc?e:D.create({doc:e});return i==null&&(i=s.doc.length),this.regexp?Lt(this,s,t,i):Ut(this,s,t,i)}}class Lf{constructor(e){this.spec=e}}function Ut(n,e,t,i){return new hi(e.doc,n.unquoted,t,i,n.caseSensitive?void 0:s=>s.toLowerCase(),n.wholeWord?p0(e.doc,e.charCategorizer(e.selection.main.head)):void 0)}function p0(n,e){return(t,i,s,r)=>((r>t||r+s.length<i)&&(r=Math.max(0,t-2),s=n.sliceString(r,Math.min(n.length,i+2))),(e(os(s,t-r))!=G.Word||e(ls(s,t-r))!=G.Word)&&(e(ls(s,i-r))!=G.Word||e(os(s,i-r))!=G.Word))}class m0 extends Lf{constructor(e){super(e)}nextMatch(e,t,i){let s=Ut(this.spec,e,i,e.doc.length).nextOverlapping();return s.done&&(s=Ut(this.spec,e,0,t).nextOverlapping()),s.done?null:s.value}prevMatchInRange(e,t,i){for(let s=i;;){let r=Math.max(t,s-1e4-this.spec.unquoted.length),o=Ut(this.spec,e,r,s),l=null;for(;!o.nextOverlapping().done;)l=o.value;if(l)return l;if(r==t)return null;s-=1e4}}prevMatch(e,t,i){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,i,e.doc.length)}getReplacement(e){return this.spec.unquote(this.spec.replace)}matchAll(e,t){let i=Ut(this.spec,e,0,e.doc.length),s=[];for(;!i.next().done;){if(s.length>=t)return null;s.push(i.value)}return s}highlight(e,t,i,s){let r=Ut(this.spec,e,Math.max(0,t-this.spec.unquoted.length),Math.min(i+this.spec.unquoted.length,e.doc.length));for(;!r.next().done;)s(r.value.from,r.value.to)}}function Lt(n,e,t,i){return new Vf(e.doc,n.search,{ignoreCase:!n.caseSensitive,test:n.wholeWord?g0(e.charCategorizer(e.selection.main.head)):void 0},t,i)}function os(n,e){return n.slice(ue(n,e,!1),e)}function ls(n,e){return n.slice(e,ue(n,e))}function g0(n){return(e,t,i)=>!i[0].length||(n(os(i.input,i.index))!=G.Word||n(ls(i.input,i.index))!=G.Word)&&(n(ls(i.input,i.index+i[0].length))!=G.Word||n(os(i.input,i.index+i[0].length))!=G.Word)}class b0 extends Lf{nextMatch(e,t,i){let s=Lt(this.spec,e,i,e.doc.length).next();return s.done&&(s=Lt(this.spec,e,0,t).next()),s.done?null:s.value}prevMatchInRange(e,t,i){for(let s=1;;s++){let r=Math.max(t,i-s*1e4),o=Lt(this.spec,e,r,i),l=null;for(;!o.next().done;)l=o.value;if(l&&(r==t||l.from>r+10))return l;if(r==t)return null}}prevMatch(e,t,i){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,i,e.doc.length)}getReplacement(e){return this.spec.unquote(this.spec.replace).replace(/\$([$&\d+])/g,(t,i)=>i=="$"?"$":i=="&"?e.match[0]:i!="0"&&+i<e.match.length?e.match[i]:t)}matchAll(e,t){let i=Lt(this.spec,e,0,e.doc.length),s=[];for(;!i.next().done;){if(s.length>=t)return null;s.push(i.value)}return s}highlight(e,t,i,s){let r=Lt(this.spec,e,Math.max(0,t-250),Math.min(i+250,e.doc.length));for(;!r.next().done;)s(r.value.from,r.value.to)}}const Ni=M.define(),Ro=M.define(),gt=ee.define({create(n){return new Fs(Fr(n).create(),null)},update(n,e){for(let t of e.effects)t.is(Ni)?n=new Fs(t.value.create(),n.panel):t.is(Ro)&&(n=new Fs(n.query,t.value?Ao:null));return n},provide:n=>Vi.from(n,e=>e.panel)});class Fs{constructor(e,t){this.query=e,this.panel=t}}const y0=A.mark({class:"cm-searchMatch"}),x0=A.mark({class:"cm-searchMatch cm-searchMatch-selected"}),S0=F.fromClass(class{constructor(n){this.view=n,this.decorations=this.highlight(n.state.field(gt))}update(n){let e=n.state.field(gt);(e!=n.startState.field(gt)||n.docChanged||n.selectionSet||n.viewportChanged)&&(this.decorations=this.highlight(e))}highlight({query:n,panel:e}){if(!e||!n.spec.valid)return A.none;let{view:t}=this,i=new yt;for(let s=0,r=t.visibleRanges,o=r.length;s<o;s++){let{from:l,to:a}=r[s];for(;s<o-1&&a>r[s+1].from-2*250;)a=r[++s].to;n.highlight(t.state,l,a,(h,c)=>{let f=t.state.selection.ranges.some(u=>u.from==h&&u.to==c);i.add(h,c,f?x0:y0)})}return i.finish()}},{decorations:n=>n.decorations});function on(n){return e=>{let t=e.state.field(gt,!1);return t&&t.query.spec.valid?n(e,t):If(e)}}const as=on((n,{query:e})=>{let{to:t}=n.state.selection.main,i=e.nextMatch(n.state,t,t);if(!i)return!1;let s=y.single(i.from,i.to),r=n.state.facet(di);return n.dispatch({selection:s,effects:[Mo(n,i),r.scrollToMatch(s.main,n)],userEvent:"select.search"}),Nf(n),!0}),hs=on((n,{query:e})=>{let{state:t}=n,{from:i}=t.selection.main,s=e.prevMatch(t,i,i);if(!s)return!1;let r=y.single(s.from,s.to),o=n.state.facet(di);return n.dispatch({selection:r,effects:[Mo(n,s),o.scrollToMatch(r.main,n)],userEvent:"select.search"}),Nf(n),!0}),w0=on((n,{query:e})=>{let t=e.matchAll(n.state,1e3);return!t||!t.length?!1:(n.dispatch({selection:y.create(t.map(i=>y.range(i.from,i.to))),userEvent:"select.search.matches"}),!0)}),Q0=({state:n,dispatch:e})=>{let t=n.selection;if(t.ranges.length>1||t.main.empty)return!1;let{from:i,to:s}=t.main,r=[],o=0;for(let l=new hi(n.doc,n.sliceDoc(i,s));!l.next().done;){if(r.length>1e3)return!1;l.value.from==i&&(o=r.length),r.push(y.range(l.value.from,l.value.to))}return e(n.update({selection:y.create(r,o),userEvent:"select.search.matches"})),!0},pa=on((n,{query:e})=>{let{state:t}=n,{from:i,to:s}=t.selection.main;if(t.readOnly)return!1;let r=e.nextMatch(t,i,i);if(!r)return!1;let o=[],l,a,h=[];if(r.from==i&&r.to==s&&(a=t.toText(e.getReplacement(r)),o.push({from:r.from,to:r.to,insert:a}),r=e.nextMatch(t,r.from,r.to),h.push(v.announce.of(t.phrase("replaced match on line $",t.doc.lineAt(i).number)+"."))),r){let c=o.length==0||o[0].from>=r.to?0:r.to-r.from-a.length;l=y.single(r.from-c,r.to-c),h.push(Mo(n,r)),h.push(t.facet(di).scrollToMatch(l.main,n))}return n.dispatch({changes:o,selection:l,effects:h,userEvent:"input.replace"}),!0}),k0=on((n,{query:e})=>{if(n.state.readOnly)return!1;let t=e.matchAll(n.state,1e9).map(s=>{let{from:r,to:o}=s;return{from:r,to:o,insert:e.getReplacement(s)}});if(!t.length)return!1;let i=n.state.phrase("replaced $ matches",t.length)+".";return n.dispatch({changes:t,effects:v.announce.of(i),userEvent:"input.replace.all"}),!0});function Ao(n){return n.state.facet(di).createPanel(n)}function Fr(n,e){var t,i,s,r,o;let l=n.selection.main,a=l.empty||l.to>l.from+100?"":n.sliceDoc(l.from,l.to);if(e&&!a)return e;let h=n.facet(di);return new Uf({search:((t=e==null?void 0:e.literal)!==null&&t!==void 0?t:h.literal)?a:a.replace(/\n/g,"\\n"),caseSensitive:(i=e==null?void 0:e.caseSensitive)!==null&&i!==void 0?i:h.caseSensitive,literal:(s=e==null?void 0:e.literal)!==null&&s!==void 0?s:h.literal,regexp:(r=e==null?void 0:e.regexp)!==null&&r!==void 0?r:h.regexp,wholeWord:(o=e==null?void 0:e.wholeWord)!==null&&o!==void 0?o:h.wholeWord})}function _f(n){let e=Ei(n,Ao);return e&&e.dom.querySelector("[main-field]")}function Nf(n){let e=_f(n);e&&e==n.root.activeElement&&e.select()}const If=n=>{let e=n.state.field(gt,!1);if(e&&e.panel){let t=_f(n);if(t&&t!=n.root.activeElement){let i=Fr(n.state,e.query.spec);i.valid&&n.dispatch({effects:Ni.of(i)}),t.focus(),t.select()}}else n.dispatch({effects:[Ro.of(!0),e?Ni.of(Fr(n.state,e.query.spec)):M.appendConfig.of(Z0)]});return!0},Gf=n=>{let e=n.state.field(gt,!1);if(!e||!e.panel)return!1;let t=Ei(n,Ao);return t&&t.dom.contains(n.root.activeElement)&&n.focus(),n.dispatch({effects:Ro.of(!1)}),!0},P0=[{key:"Mod-f",run:If,scope:"editor search-panel"},{key:"F3",run:as,shift:hs,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:as,shift:hs,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:Gf,scope:"editor search-panel"},{key:"Mod-Shift-l",run:Q0},{key:"Mod-Alt-g",run:i0},{key:"Mod-d",run:O0,preventDefault:!0}];class $0{constructor(e){this.view=e;let t=this.query=e.state.field(gt).query.spec;this.commit=this.commit.bind(this),this.searchField=_("input",{value:t.search,placeholder:Ze(e,"Find"),"aria-label":Ze(e,"Find"),class:"cm-textfield",name:"search",form:"","main-field":"true",onchange:this.commit,onkeyup:this.commit}),this.replaceField=_("input",{value:t.replace,placeholder:Ze(e,"Replace"),"aria-label":Ze(e,"Replace"),class:"cm-textfield",name:"replace",form:"",onchange:this.commit,onkeyup:this.commit}),this.caseField=_("input",{type:"checkbox",name:"case",form:"",checked:t.caseSensitive,onchange:this.commit}),this.reField=_("input",{type:"checkbox",name:"re",form:"",checked:t.regexp,onchange:this.commit}),this.wordField=_("input",{type:"checkbox",name:"word",form:"",checked:t.wholeWord,onchange:this.commit});function i(s,r,o){return _("button",{class:"cm-button",name:s,onclick:r,type:"button"},o)}this.dom=_("div",{onkeydown:s=>this.keydown(s),class:"cm-search"},[this.searchField,i("next",()=>as(e),[Ze(e,"next")]),i("prev",()=>hs(e),[Ze(e,"previous")]),i("select",()=>w0(e),[Ze(e,"all")]),_("label",null,[this.caseField,Ze(e,"match case")]),_("label",null,[this.reField,Ze(e,"regexp")]),_("label",null,[this.wordField,Ze(e,"by word")]),...e.state.readOnly?[]:[_("br"),this.replaceField,i("replace",()=>pa(e),[Ze(e,"replace")]),i("replaceAll",()=>k0(e),[Ze(e,"replace all")])],_("button",{name:"close",onclick:()=>Gf(e),"aria-label":Ze(e,"close"),type:"button"},["×"])])}commit(){let e=new Uf({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,wholeWord:this.wordField.checked,replace:this.replaceField.value});e.eq(this.query)||(this.query=e,this.view.dispatch({effects:Ni.of(e)}))}keydown(e){vO(this.view,e,"search-panel")?e.preventDefault():e.keyCode==13&&e.target==this.searchField?(e.preventDefault(),(e.shiftKey?hs:as)(this.view)):e.keyCode==13&&e.target==this.replaceField&&(e.preventDefault(),pa(this.view))}update(e){for(let t of e.transactions)for(let i of t.effects)i.is(Ni)&&!i.value.eq(this.query)&&this.setQuery(i.value)}setQuery(e){this.query=e,this.searchField.value=e.search,this.replaceField.value=e.replace,this.caseField.checked=e.caseSensitive,this.reField.checked=e.regexp,this.wordField.checked=e.wholeWord}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(di).top}}function Ze(n,e){return n.state.phrase(e)}const Zn=30,Cn=/[\s\.,:;?!]/;function Mo(n,{from:e,to:t}){let i=n.state.doc.lineAt(e),s=n.state.doc.lineAt(t).to,r=Math.max(i.from,e-Zn),o=Math.min(s,t+Zn),l=n.state.sliceDoc(r,o);if(r!=i.from){for(let a=0;a<Zn;a++)if(!Cn.test(l[a+1])&&Cn.test(l[a])){l=l.slice(a);break}}if(o!=s){for(let a=l.length-1;a>l.length-Zn;a--)if(!Cn.test(l[a-1])&&Cn.test(l[a])){l=l.slice(0,a);break}}return v.announce.of(`${n.state.phrase("current match")}. ${l} ${n.state.phrase("on line")} ${i.number}.`)}const v0=v.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}}),Z0=[gt,Pt.low(S0),v0];class Ff{constructor(e,t,i){this.state=e,this.pos=t,this.explicit=i,this.abortListeners=[]}tokenBefore(e){let t=re(this.state).resolveInner(this.pos,-1);for(;t&&e.indexOf(t.name)<0;)t=t.parent;return t?{from:t.from,to:this.pos,text:this.state.sliceDoc(t.from,this.pos),type:t.type}:null}matchBefore(e){let t=this.state.doc.lineAt(this.pos),i=Math.max(t.from,this.pos-250),s=t.text.slice(i-t.from,this.pos-t.from),r=s.search(Kf(e,!1));return r<0?null:{from:i+r,to:this.pos,text:s.slice(r)}}get aborted(){return this.abortListeners==null}addEventListener(e,t){e=="abort"&&this.abortListeners&&this.abortListeners.push(t)}}function ma(n){let e=Object.keys(n).join(""),t=/\w/.test(e);return t&&(e=e.replace(/\w/g,"")),`[${t?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function C0(n){let e=Object.create(null),t=Object.create(null);for(let{label:s}of n){e[s[0]]=!0;for(let r=1;r<s.length;r++)t[s[r]]=!0}let i=ma(e)+ma(t)+"*$";return[new RegExp("^"+i),new RegExp(i)]}function Hf(n){let e=n.map(s=>typeof s=="string"?{label:s}:s),[t,i]=e.every(s=>/^\w+$/.test(s.label))?[/\w*$/,/\w+$/]:C0(e);return s=>{let r=s.matchBefore(i);return r||s.explicit?{from:r?r.from:s.pos,options:e,validFor:t}:null}}function T0(n,e){return t=>{for(let i=re(t.state).resolveInner(t.pos,-1);i;i=i.parent){if(n.indexOf(i.name)>-1)return null;if(i.type.isTop)break}return e(t)}}class ga{constructor(e,t,i,s){this.completion=e,this.source=t,this.match=i,this.score=s}}function bt(n){return n.selection.main.from}function Kf(n,e){var t;let{source:i}=n,s=e&&i[0]!="^",r=i[i.length-1]!="$";return!s&&!r?n:new RegExp(`${s?"^":""}(?:${i})${r?"$":""}`,(t=n.flags)!==null&&t!==void 0?t:n.ignoreCase?"i":"")}const Wo=ht.define();function R0(n,e,t,i){let{main:s}=n.selection,r=t-s.from,o=i-s.from;return Object.assign(Object.assign({},n.changeByRange(l=>l!=s&&t!=i&&n.sliceDoc(l.from+r,l.from+o)!=n.sliceDoc(t,i)?{range:l}:{changes:{from:l.from+r,to:i==s.from?l.to:l.from+o,insert:e},range:y.cursor(l.from+r+e.length)})),{scrollIntoView:!0,userEvent:"input.complete"})}const ba=new WeakMap;function A0(n){if(!Array.isArray(n))return n;let e=ba.get(n);return e||ba.set(n,e=Hf(n)),e}const cs=M.define(),Ii=M.define();class M0{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let t=0;t<e.length;){let i=ae(e,t),s=We(i);this.chars.push(i);let r=e.slice(t,t+s),o=r.toUpperCase();this.folded.push(ae(o==r?r.toLowerCase():o,0)),t+=s}this.astral=e.length!=this.chars.length}ret(e,t){return this.score=e,this.matched=t,this}match(e){if(this.pattern.length==0)return this.ret(-100,[]);if(e.length<this.pattern.length)return null;let{chars:t,folded:i,any:s,precise:r,byWord:o}=this;if(t.length==1){let S=ae(e,0),P=We(S),Q=P==e.length?0:-100;if(S!=t[0])if(S==i[0])Q+=-200;else return null;return this.ret(Q,[0,P])}let l=e.indexOf(this.pattern);if(l==0)return this.ret(e.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=t.length,h=0;if(l<0){for(let S=0,P=Math.min(e.length,200);S<P&&h<a;){let Q=ae(e,S);(Q==t[h]||Q==i[h])&&(s[h++]=S),S+=We(Q)}if(h<a)return null}let c=0,f=0,u=!1,d=0,O=-1,m=-1,g=/[a-z]/.test(e),b=!0;for(let S=0,P=Math.min(e.length,200),Q=0;S<P&&f<a;){let x=ae(e,S);l<0&&(c<a&&x==t[c]&&(r[c++]=S),d<a&&(x==t[d]||x==i[d]?(d==0&&(O=S),m=S+1,d++):d=0));let Z,$=x<255?x>=48&&x<=57||x>=97&&x<=122?2:x>=65&&x<=90?1:0:(Z=io(x))!=Z.toLowerCase()?1:Z!=Z.toUpperCase()?2:0;(!S||$==1&&g||Q==0&&$!=0)&&(t[f]==x||i[f]==x&&(u=!0)?o[f++]=S:o.length&&(b=!1)),Q=$,S+=We(x)}return f==a&&o[0]==0&&b?this.result(-100+(u?-200:0),o,e):d==a&&O==0?this.ret(-200-e.length+(m==e.length?0:-100),[0,m]):l>-1?this.ret(-700-e.length,[l,l+this.pattern.length]):d==a?this.ret(-900-e.length,[O,m]):f==a?this.result(-100+(u?-200:0)+-700+(b?0:-1100),o,e):t.length==2?null:this.result((s[0]?-700:0)+-200+-1100,s,e)}result(e,t,i){let s=[],r=0;for(let o of t){let l=o+(this.astral?We(ae(i,o)):1);r&&s[r-1]==o?s[r-1]=l:(s[r++]=o,s[r++]=l)}return this.ret(e-i.length,s)}}class W0{constructor(e){this.pattern=e,this.matched=[],this.score=0,this.folded=e.toLowerCase()}match(e){if(e.length<this.pattern.length)return null;let t=e.slice(0,this.pattern.length),i=t==this.pattern?0:t.toLowerCase()==this.folded?-200:null;return i==null?null:(this.matched=[0,t.length],this.score=i+(e.length==this.pattern.length?0:-100),this)}}const fe=C.define({combine(n){return Ve(n,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:X0,filterStrict:!1,compareCompletions:(e,t)=>e.label.localeCompare(t.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,tooltipClass:(e,t)=>i=>ya(e(i),t(i)),optionClass:(e,t)=>i=>ya(e(i),t(i)),addToOptions:(e,t)=>e.concat(t),filterStrict:(e,t)=>e||t})}});function ya(n,e){return n?e?n+" "+e:n:e}function X0(n,e,t,i,s,r){let o=n.textDirection==I.RTL,l=o,a=!1,h="top",c,f,u=e.left-s.left,d=s.right-e.right,O=i.right-i.left,m=i.bottom-i.top;if(l&&u<Math.min(O,d)?l=!1:!l&&d<Math.min(O,u)&&(l=!0),O<=(l?u:d))c=Math.max(s.top,Math.min(t.top,s.bottom-m))-e.top,f=Math.min(400,l?u:d);else{a=!0,f=Math.min(400,(o?e.right:s.right-e.left)-30);let S=s.bottom-e.bottom;S>=m||S>e.top?c=t.bottom-e.top:(h="bottom",c=e.bottom-t.top)}let g=(e.bottom-e.top)/r.offsetHeight,b=(e.right-e.left)/r.offsetWidth;return{style:`${h}: ${c/g}px; max-width: ${f/b}px`,class:"cm-completionInfo-"+(a?o?"left-narrow":"right-narrow":l?"left":"right")}}function Y0(n){let e=n.addToOptions.slice();return n.icons&&e.push({render(t){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),t.type&&i.classList.add(...t.type.split(/\s+/g).map(s=>"cm-completionIcon-"+s)),i.setAttribute("aria-hidden","true"),i},position:20}),e.push({render(t,i,s,r){let o=document.createElement("span");o.className="cm-completionLabel";let l=t.displayLabel||t.label,a=0;for(let h=0;h<r.length;){let c=r[h++],f=r[h++];c>a&&o.appendChild(document.createTextNode(l.slice(a,c)));let u=o.appendChild(document.createElement("span"));u.appendChild(document.createTextNode(l.slice(c,f))),u.className="cm-completionMatchedText",a=f}return a<l.length&&o.appendChild(document.createTextNode(l.slice(a))),o},position:50},{render(t){if(!t.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=t.detail,i},position:80}),e.sort((t,i)=>t.position-i.position).map(t=>t.render)}function Hs(n,e,t){if(n<=t)return{from:0,to:n};if(e<0&&(e=0),e<=n>>1){let s=Math.floor(e/t);return{from:s*t,to:(s+1)*t}}let i=Math.floor((n-e)/t);return{from:n-(i+1)*t,to:n-i*t}}class D0{constructor(e,t,i){this.view=e,this.stateField=t,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:a=>this.placeInfo(a),key:this},this.space=null,this.currentClass="";let s=e.state.field(t),{options:r,selected:o}=s.open,l=e.state.facet(fe);this.optionContent=Y0(l),this.optionClass=l.optionClass,this.tooltipClass=l.tooltipClass,this.range=Hs(r.length,o,l.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(e.state),this.dom.addEventListener("mousedown",a=>{let{options:h}=e.state.field(t).open;for(let c=a.target,f;c&&c!=this.dom;c=c.parentNode)if(c.nodeName=="LI"&&(f=/-(\d+)$/.exec(c.id))&&+f[1]<h.length){this.applyCompletion(e,h[+f[1]]),a.preventDefault();return}}),this.dom.addEventListener("focusout",a=>{let h=e.state.field(this.stateField,!1);h&&h.tooltip&&e.state.facet(fe).closeOnBlur&&a.relatedTarget!=e.contentDOM&&e.dispatch({effects:Ii.of(null)})}),this.showOptions(r,s.id)}mount(){this.updateSel()}showOptions(e,t){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(e,t,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)})}update(e){var t;let i=e.state.field(this.stateField),s=e.startState.field(this.stateField);if(this.updateTooltipClass(e.state),i!=s){let{options:r,selected:o,disabled:l}=i.open;(!s.open||s.open.options!=r)&&(this.range=Hs(r.length,o,e.state.facet(fe).maxRenderedOptions),this.showOptions(r,i.id)),this.updateSel(),l!=((t=s.open)===null||t===void 0?void 0:t.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!l)}}updateTooltipClass(e){let t=this.tooltipClass(e);if(t!=this.currentClass){for(let i of this.currentClass.split(" "))i&&this.dom.classList.remove(i);for(let i of t.split(" "))i&&this.dom.classList.add(i);this.currentClass=t}}positioned(e){this.space=e,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected>-1&&t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=Hs(t.options.length,t.selected,this.view.state.facet(fe).maxRenderedOptions),this.showOptions(t.options,e.id)),this.updateSelectedOption(t.selected)){this.destroyInfo();let{completion:i}=t.options[t.selected],{info:s}=i;if(!s)return;let r=typeof s=="string"?document.createTextNode(s):s(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(o,i)}).catch(o=>ge(this.view.state,o,"completion info")):this.addInfoPane(r,i)}}addInfoPane(e,t){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",e.nodeType!=null)i.appendChild(e),this.infoDestroy=null;else{let{dom:s,destroy:r}=e;i.appendChild(s),this.infoDestroy=r||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(e){let t=null;for(let i=this.list.firstChild,s=this.range.from;i;i=i.nextSibling,s++)i.nodeName!="LI"||!i.id?s--:s==e?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),t=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return t&&z0(this.list,t),t}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),s=e.getBoundingClientRect(),r=this.space;if(!r){let o=this.dom.ownerDocument.defaultView||window;r={left:0,top:0,right:o.innerWidth,bottom:o.innerHeight}}return s.top>Math.min(r.bottom,t.bottom)-10||s.bottom<Math.max(r.top,t.top)+10?null:this.view.state.facet(fe).positionInfo(this.view,t,s,i,r,this.dom)}placeInfo(e){this.info&&(e?(e.style&&(this.info.style.cssText=e.style),this.info.className="cm-tooltip cm-completionInfo "+(e.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(e,t,i){const s=document.createElement("ul");s.id=t,s.setAttribute("role","listbox"),s.setAttribute("aria-expanded","true"),s.setAttribute("aria-label",this.view.state.phrase("Completions"));let r=null;for(let o=i.from;o<i.to;o++){let{completion:l,match:a}=e[o],{section:h}=l;if(h){let u=typeof h=="string"?h:h.name;if(u!=r&&(o>i.from||i.from==0))if(r=u,typeof h!="string"&&h.header)s.appendChild(h.header(h));else{let d=s.appendChild(document.createElement("completion-section"));d.textContent=u}}const c=s.appendChild(document.createElement("li"));c.id=t+"-"+o,c.setAttribute("role","option");let f=this.optionClass(l);f&&(c.className=f);for(let u of this.optionContent){let d=u(l,this.view.state,this.view,a);d&&c.appendChild(d)}}return i.from&&s.classList.add("cm-completionListIncompleteTop"),i.to<e.length&&s.classList.add("cm-completionListIncompleteBottom"),s}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function q0(n,e){return t=>new D0(t,n,e)}function z0(n,e){let t=n.getBoundingClientRect(),i=e.getBoundingClientRect(),s=t.height/n.offsetHeight;i.top<t.top?n.scrollTop-=(t.top-i.top)/s:i.bottom>t.bottom&&(n.scrollTop+=(i.bottom-t.bottom)/s)}function xa(n){return(n.boost||0)*100+(n.apply?10:0)+(n.info?5:0)+(n.type?1:0)}function j0(n,e){let t=[],i=null,s=h=>{t.push(h);let{section:c}=h.completion;if(c){i||(i=[]);let f=typeof c=="string"?c:c.name;i.some(u=>u.name==f)||i.push(typeof c=="string"?{name:f}:c)}},r=e.facet(fe);for(let h of n)if(h.hasResult()){let c=h.result.getMatch;if(h.result.filter===!1)for(let f of h.result.options)s(new ga(f,h.source,c?c(f):[],1e9-t.length));else{let f=e.sliceDoc(h.from,h.to),u,d=r.filterStrict?new W0(f):new M0(f);for(let O of h.result.options)if(u=d.match(O.label)){let m=O.displayLabel?c?c(O,u.matched):[]:u.matched;s(new ga(O,h.source,m,u.score+(O.boost||0)))}}}if(i){let h=Object.create(null),c=0,f=(u,d)=>{var O,m;return((O=u.rank)!==null&&O!==void 0?O:1e9)-((m=d.rank)!==null&&m!==void 0?m:1e9)||(u.name<d.name?-1:1)};for(let u of i.sort(f))c-=1e5,h[u.name]=c;for(let u of t){let{section:d}=u.completion;d&&(u.score+=h[typeof d=="string"?d:d.name])}}let o=[],l=null,a=r.compareCompletions;for(let h of t.sort((c,f)=>f.score-c.score||a(c.completion,f.completion))){let c=h.completion;!l||l.label!=c.label||l.detail!=c.detail||l.type!=null&&c.type!=null&&l.type!=c.type||l.apply!=c.apply||l.boost!=c.boost?o.push(h):xa(h.completion)>xa(l)&&(o[o.length-1]=h),l=h.completion}return o}class It{constructor(e,t,i,s,r,o){this.options=e,this.attrs=t,this.tooltip=i,this.timestamp=s,this.selected=r,this.disabled=o}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new It(this.options,Sa(t,e),this.tooltip,this.timestamp,e,this.disabled)}static build(e,t,i,s,r){let o=j0(e,t);if(!o.length)return s&&e.some(a=>a.state==1)?new It(s.options,s.attrs,s.tooltip,s.timestamp,s.selected,!0):null;let l=t.facet(fe).selectOnOpen?0:-1;if(s&&s.selected!=l&&s.selected!=-1){let a=s.options[s.selected].completion;for(let h=0;h<o.length;h++)if(o[h].completion==a){l=h;break}}return new It(o,Sa(i,l),{pos:e.reduce((a,h)=>h.hasResult()?Math.min(a,h.from):a,1e8),create:L0,above:r.aboveCursor},s?s.timestamp:Date.now(),l,!1)}map(e){return new It(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}}class fs{constructor(e,t,i){this.active=e,this.id=t,this.open=i}static start(){return new fs(B0,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(e){let{state:t}=e,i=t.facet(fe),r=(i.override||t.languageDataAt("autocomplete",bt(t)).map(A0)).map(l=>(this.active.find(h=>h.source==l)||new me(l,this.active.some(h=>h.state!=0)?1:0)).update(e,i));r.length==this.active.length&&r.every((l,a)=>l==this.active[a])&&(r=this.active);let o=this.open;o&&e.docChanged&&(o=o.map(e.changes)),e.selection||r.some(l=>l.hasResult()&&e.changes.touchesRange(l.from,l.to))||!E0(r,this.active)?o=It.build(r,t,this.id,o,i):o&&o.disabled&&!r.some(l=>l.state==1)&&(o=null),!o&&r.every(l=>l.state!=1)&&r.some(l=>l.hasResult())&&(r=r.map(l=>l.hasResult()?new me(l.source,0):l));for(let l of e.effects)l.is(eu)&&(o=o&&o.setSelected(l.value,this.id));return r==this.active&&o==this.open?this:new fs(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:V0}}function E0(n,e){if(n==e)return!0;for(let t=0,i=0;;){for(;t<n.length&&!n[t].hasResult;)t++;for(;i<e.length&&!e[i].hasResult;)i++;let s=t==n.length,r=i==e.length;if(s||r)return s==r;if(n[t++].result!=e[i++].result)return!1}}const V0={"aria-autocomplete":"list"};function Sa(n,e){let t={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":n};return e>-1&&(t["aria-activedescendant"]=n+"-"+e),t}const B0=[];function Hr(n,e){if(n.isUserEvent("input.complete")){let t=n.annotation(Wo);if(t&&e.activateOnCompletion(t))return"input"}return n.isUserEvent("input.type")?"input":n.isUserEvent("delete.backward")?"delete":null}class me{constructor(e,t,i=-1){this.source=e,this.state=t,this.explicitPos=i}hasResult(){return!1}update(e,t){let i=Hr(e,t),s=this;i?s=s.handleUserEvent(e,i,t):e.docChanged?s=s.handleChange(e):e.selection&&s.state!=0&&(s=new me(s.source,0));for(let r of e.effects)if(r.is(cs))s=new me(s.source,1,r.value?bt(e.state):-1);else if(r.is(Ii))s=new me(s.source,0);else if(r.is(Jf))for(let o of r.value)o.source==s.source&&(s=o);return s}handleUserEvent(e,t,i){return t=="delete"||!i.activateOnTyping?this.map(e.changes):new me(this.source,1)}handleChange(e){return e.changes.touchesRange(bt(e.startState))?new me(this.source,0):this.map(e.changes)}map(e){return e.empty||this.explicitPos<0?this:new me(this.source,this.state,e.mapPos(this.explicitPos))}}class ei extends me{constructor(e,t,i,s,r){super(e,2,t),this.result=i,this.from=s,this.to=r}hasResult(){return!0}handleUserEvent(e,t,i){var s;let r=this.result;r.map&&!e.changes.empty&&(r=r.map(r,e.changes));let o=e.changes.mapPos(this.from),l=e.changes.mapPos(this.to,1),a=bt(e.state);if((this.explicitPos<0?a<=o:a<this.from)||a>l||!r||t=="delete"&&bt(e.startState)==this.from)return new me(this.source,t=="input"&&i.activateOnTyping?1:0);let h=this.explicitPos<0?-1:e.changes.mapPos(this.explicitPos);return U0(r.validFor,e.state,o,l)?new ei(this.source,h,r,o,l):r.update&&(r=r.update(r,o,l,new Ff(e.state,a,h>=0)))?new ei(this.source,h,r,r.from,(s=r.to)!==null&&s!==void 0?s:bt(e.state)):new me(this.source,1,h)}handleChange(e){return e.changes.touchesRange(this.from,this.to)?new me(this.source,0):this.map(e.changes)}map(e){return e.empty?this:(this.result.map?this.result.map(this.result,e):this.result)?new ei(this.source,this.explicitPos<0?-1:e.mapPos(this.explicitPos),this.result,e.mapPos(this.from),e.mapPos(this.to,1)):new me(this.source,0)}}function U0(n,e,t,i){if(!n)return!1;let s=e.sliceDoc(t,i);return typeof n=="function"?n(s,t,i,e):Kf(n,!0).test(s)}const Jf=M.define({map(n,e){return n.map(t=>t.map(e))}}),eu=M.define(),we=ee.define({create(){return fs.start()},update(n,e){return n.update(e)},provide:n=>[gs.from(n,e=>e.tooltip),v.contentAttributes.from(n,e=>e.attrs)]});function Xo(n,e){const t=e.completion.apply||e.completion.label;let i=n.state.field(we).active.find(s=>s.source==e.source);return i instanceof ei?(typeof t=="string"?n.dispatch(Object.assign(Object.assign({},R0(n.state,t,i.from,i.to)),{annotations:Wo.of(e.completion)})):t(n,e.completion,i.from,i.to),!0):!1}const L0=q0(we,Xo);function Tn(n,e="option"){return t=>{let i=t.state.field(we,!1);if(!i||!i.open||i.open.disabled||Date.now()-i.open.timestamp<t.state.facet(fe).interactionDelay)return!1;let s=1,r;e=="page"&&(r=bc(t,i.open.tooltip))&&(s=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+s*(n?1:-1):n?0:o-1;return l<0?l=e=="page"?0:o-1:l>=o&&(l=e=="page"?o-1:0),t.dispatch({effects:eu.of(l)}),!0}}const _0=n=>{let e=n.state.field(we,!1);return n.state.readOnly||!e||!e.open||e.open.selected<0||e.open.disabled||Date.now()-e.open.timestamp<n.state.facet(fe).interactionDelay?!1:Xo(n,e.open.options[e.open.selected])},N0=n=>n.state.field(we,!1)?(n.dispatch({effects:cs.of(!0)}),!0):!1,I0=n=>{let e=n.state.field(we,!1);return!e||!e.active.some(t=>t.state!=0)?!1:(n.dispatch({effects:Ii.of(null)}),!0)};class G0{constructor(e,t){this.active=e,this.context=t,this.time=Date.now(),this.updates=[],this.done=void 0}}const F0=50,H0=1e3,K0=F.fromClass(class{constructor(n){this.view=n,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let e of n.state.field(we).active)e.state==1&&this.startQuery(e)}update(n){let e=n.state.field(we),t=n.state.facet(fe);if(!n.selectionSet&&!n.docChanged&&n.startState.field(we)==e)return;let i=n.transactions.some(r=>(r.selection||r.docChanged)&&!Hr(r,t));for(let r=0;r<this.running.length;r++){let o=this.running[r];if(i||o.updates.length+n.transactions.length>F0&&Date.now()-o.time>H0){for(let l of o.context.abortListeners)try{l()}catch(a){ge(this.view.state,a)}o.context.abortListeners=null,this.running.splice(r--,1)}else o.updates.push(...n.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),n.transactions.some(r=>r.effects.some(o=>o.is(cs)))&&(this.pendingStart=!0);let s=this.pendingStart?50:t.activateOnTypingDelay;if(this.debounceUpdate=e.active.some(r=>r.state==1&&!this.running.some(o=>o.active.source==r.source))?setTimeout(()=>this.startUpdate(),s):-1,this.composing!=0)for(let r of n.transactions)Hr(r,t)=="input"?this.composing=2:this.composing==2&&r.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:n}=this.view,e=n.field(we);for(let t of e.active)t.state==1&&!this.running.some(i=>i.active.source==t.source)&&this.startQuery(t)}startQuery(n){let{state:e}=this.view,t=bt(e),i=new Ff(e,t,n.explicitPos==t),s=new G0(n,i);this.running.push(s),Promise.resolve(n.source(i)).then(r=>{s.context.aborted||(s.done=r||null,this.scheduleAccept())},r=>{this.view.dispatch({effects:Ii.of(null)}),ge(this.view.state,r)})}scheduleAccept(){this.running.every(n=>n.done!==void 0)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(fe).updateSyncTime))}accept(){var n;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let e=[],t=this.view.state.facet(fe);for(let i=0;i<this.running.length;i++){let s=this.running[i];if(s.done===void 0)continue;if(this.running.splice(i--,1),s.done){let o=new ei(s.active.source,s.active.explicitPos,s.done,s.done.from,(n=s.done.to)!==null&&n!==void 0?n:bt(s.updates.length?s.updates[0].startState:this.view.state));for(let l of s.updates)o=o.update(l,t);if(o.hasResult()){e.push(o);continue}}let r=this.view.state.field(we).active.find(o=>o.source==s.active.source);if(r&&r.state==1)if(s.done==null){let o=new me(s.active.source,0);for(let l of s.updates)o=o.update(l,t);o.state!=1&&e.push(o)}else this.startQuery(r)}e.length&&this.view.dispatch({effects:Jf.of(e)})}},{eventHandlers:{blur(n){let e=this.view.state.field(we,!1);if(e&&e.tooltip&&this.view.state.facet(fe).closeOnBlur){let t=e.open&&bc(this.view,e.open.tooltip);(!t||!t.dom.contains(n.relatedTarget))&&setTimeout(()=>this.view.dispatch({effects:Ii.of(null)}),10)}},compositionstart(){this.composing=1},compositionend(){this.composing==3&&setTimeout(()=>this.view.dispatch({effects:cs.of(!1)}),20),this.composing=0}}}),J0=typeof navigator=="object"&&/Win/.test(navigator.platform),e1=Pt.highest(v.domEventHandlers({keydown(n,e){let t=e.state.field(we,!1);if(!t||!t.open||t.open.disabled||t.open.selected<0||n.key.length>1||n.ctrlKey&&!(J0&&n.altKey)||n.metaKey)return!1;let i=t.open.options[t.open.selected],s=t.active.find(o=>o.source==i.source),r=i.completion.commitCharacters||s.result.commitCharacters;return r&&r.indexOf(n.key)>-1&&Xo(e,i),!1}})),tu=v.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class t1{constructor(e,t,i,s){this.field=e,this.line=t,this.from=i,this.to=s}}class Yo{constructor(e,t,i){this.field=e,this.from=t,this.to=i}map(e){let t=e.mapPos(this.from,-1,ce.TrackDel),i=e.mapPos(this.to,1,ce.TrackDel);return t==null||i==null?null:new Yo(this.field,t,i)}}class Do{constructor(e,t){this.lines=e,this.fieldPositions=t}instantiate(e,t){let i=[],s=[t],r=e.doc.lineAt(t),o=/^\s*/.exec(r.text)[0];for(let a of this.lines){if(i.length){let h=o,c=/^\t*/.exec(a)[0].length;for(let f=0;f<c;f++)h+=e.facet(tn);s.push(t+h.length-c),a=h+a.slice(c)}i.push(a),t+=a.length+1}let l=this.fieldPositions.map(a=>new Yo(a.field,s[a.line]+a.from,s[a.line]+a.to));return{text:i,ranges:l}}static parse(e){let t=[],i=[],s=[],r;for(let o of e.split(/\r\n?|\n/)){for(;r=/[#$]\{(?:(\d+)(?::([^}]*))?|((?:\\[{}]|[^}])*))\}/.exec(o);){let l=r[1]?+r[1]:null,a=r[2]||r[3]||"",h=-1,c=a.replace(/\\[{}]/g,f=>f[1]);for(let f=0;f<t.length;f++)(l!=null?t[f].seq==l:c&&t[f].name==c)&&(h=f);if(h<0){let f=0;for(;f<t.length&&(l==null||t[f].seq!=null&&t[f].seq<l);)f++;t.splice(f,0,{seq:l,name:c}),h=f;for(let u of s)u.field>=h&&u.field++}s.push(new t1(h,i.length,r.index,r.index+c.length)),o=o.slice(0,r.index)+a+o.slice(r.index+r[0].length)}o=o.replace(/\\([{}])/g,(l,a,h)=>{for(let c of s)c.line==i.length&&c.from>h&&(c.from--,c.to--);return a}),i.push(o)}return new Do(i,s)}}let i1=A.widget({widget:new class extends st{toDOM(){let n=document.createElement("span");return n.className="cm-snippetFieldPosition",n}ignoreEvent(){return!1}}}),n1=A.mark({class:"cm-snippetField"});class Oi{constructor(e,t){this.ranges=e,this.active=t,this.deco=A.set(e.map(i=>(i.from==i.to?i1:n1).range(i.from,i.to)))}map(e){let t=[];for(let i of this.ranges){let s=i.map(e);if(!s)return null;t.push(s)}return new Oi(t,this.active)}selectionInsideField(e){return e.ranges.every(t=>this.ranges.some(i=>i.field==this.active&&i.from<=t.from&&i.to>=t.to))}}const ln=M.define({map(n,e){return n&&n.map(e)}}),s1=M.define(),Gi=ee.define({create(){return null},update(n,e){for(let t of e.effects){if(t.is(ln))return t.value;if(t.is(s1)&&n)return new Oi(n.ranges,t.value)}return n&&e.docChanged&&(n=n.map(e.changes)),n&&e.selection&&!n.selectionInsideField(e.selection)&&(n=null),n},provide:n=>v.decorations.from(n,e=>e?e.deco:A.none)});function qo(n,e){return y.create(n.filter(t=>t.field==e).map(t=>y.range(t.from,t.to)))}function r1(n){let e=Do.parse(n);return(t,i,s,r)=>{let{text:o,ranges:l}=e.instantiate(t.state,s),a={changes:{from:s,to:r,insert:B.of(o)},scrollIntoView:!0,annotations:i?[Wo.of(i),K.userEvent.of("input.complete")]:void 0};if(l.length&&(a.selection=qo(l,0)),l.some(h=>h.field>0)){let h=new Oi(l,0),c=a.effects=[ln.of(h)];t.state.field(Gi,!1)===void 0&&c.push(M.appendConfig.of([Gi,c1,f1,tu]))}t.dispatch(t.state.update(a))}}function iu(n){return({state:e,dispatch:t})=>{let i=e.field(Gi,!1);if(!i||n<0&&i.active==0)return!1;let s=i.active+n,r=n>0&&!i.ranges.some(o=>o.field==s+n);return t(e.update({selection:qo(i.ranges,s),effects:ln.of(r?null:new Oi(i.ranges,s)),scrollIntoView:!0})),!0}}const o1=({state:n,dispatch:e})=>n.field(Gi,!1)?(e(n.update({effects:ln.of(null)})),!0):!1,l1=iu(1),a1=iu(-1),h1=[{key:"Tab",run:l1,shift:a1},{key:"Escape",run:o1}],wa=C.define({combine(n){return n.length?n[0]:h1}}),c1=Pt.highest(fi.compute([wa],n=>n.facet(wa)));function Se(n,e){return Object.assign(Object.assign({},e),{apply:r1(n)})}const f1=v.domEventHandlers({mousedown(n,e){let t=e.state.field(Gi,!1),i;if(!t||(i=e.posAtCoords({x:n.clientX,y:n.clientY}))==null)return!1;let s=t.ranges.find(r=>r.from<=i&&r.to>=i);return!s||s.field==t.active?!1:(e.dispatch({selection:qo(t.ranges,s.field),effects:ln.of(t.ranges.some(r=>r.field>s.field)?new Oi(t.ranges,s.field):null),scrollIntoView:!0}),!0)}}),Fi={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},Mt=M.define({map(n,e){let t=e.mapPos(n,-1,ce.TrackAfter);return t??void 0}}),zo=new class extends Yt{};zo.startSide=1;zo.endSide=-1;const nu=ee.define({create(){return E.empty},update(n,e){if(n=n.map(e.changes),e.selection){let t=e.state.doc.lineAt(e.selection.main.head);n=n.update({filter:i=>i>=t.from&&i<=t.to})}for(let t of e.effects)t.is(Mt)&&(n=n.update({add:[zo.range(t.value,t.value+1)]}));return n}});function u1(){return[O1,nu]}const Ks="()[]{}<>";function su(n){for(let e=0;e<Ks.length;e+=2)if(Ks.charCodeAt(e)==n)return Ks.charAt(e+1);return io(n<128?n:n+1)}function ru(n,e){return n.languageDataAt("closeBrackets",e)[0]||Fi}const d1=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),O1=v.inputHandler.of((n,e,t,i)=>{if((d1?n.composing:n.compositionStarted)||n.state.readOnly)return!1;let s=n.state.selection.main;if(i.length>2||i.length==2&&We(ae(i,0))==1||e!=s.from||t!=s.to)return!1;let r=g1(n.state,i);return r?(n.dispatch(r),!0):!1}),p1=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let i=ru(n,n.selection.main.head).brackets||Fi.brackets,s=null,r=n.changeByRange(o=>{if(o.empty){let l=b1(n.doc,o.head);for(let a of i)if(a==l&&vs(n.doc,o.head)==su(ae(a,0)))return{changes:{from:o.head-a.length,to:o.head+a.length},range:y.cursor(o.head-a.length)}}return{range:s=o}});return s||e(n.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!s},m1=[{key:"Backspace",run:p1}];function g1(n,e){let t=ru(n,n.selection.main.head),i=t.brackets||Fi.brackets;for(let s of i){let r=su(ae(s,0));if(e==s)return r==s?S1(n,s,i.indexOf(s+s+s)>-1,t):y1(n,s,r,t.before||Fi.before);if(e==r&&ou(n,n.selection.main.from))return x1(n,s,r)}return null}function ou(n,e){let t=!1;return n.field(nu).between(0,n.doc.length,i=>{i==e&&(t=!0)}),t}function vs(n,e){let t=n.sliceString(e,e+2);return t.slice(0,We(ae(t,0)))}function b1(n,e){let t=n.sliceString(e-2,e);return We(ae(t,0))==t.length?t:t.slice(1)}function y1(n,e,t,i){let s=null,r=n.changeByRange(o=>{if(!o.empty)return{changes:[{insert:e,from:o.from},{insert:t,from:o.to}],effects:Mt.of(o.to+e.length),range:y.range(o.anchor+e.length,o.head+e.length)};let l=vs(n.doc,o.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:e+t,from:o.head},effects:Mt.of(o.head+e.length),range:y.cursor(o.head+e.length)}:{range:s=o}});return s?null:n.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function x1(n,e,t){let i=null,s=n.changeByRange(r=>r.empty&&vs(n.doc,r.head)==t?{changes:{from:r.head,to:r.head+t.length,insert:t},range:y.cursor(r.head+t.length)}:i={range:r});return i?null:n.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function S1(n,e,t,i){let s=i.stringPrefixes||Fi.stringPrefixes,r=null,o=n.changeByRange(l=>{if(!l.empty)return{changes:[{insert:e,from:l.from},{insert:e,from:l.to}],effects:Mt.of(l.to+e.length),range:y.range(l.anchor+e.length,l.head+e.length)};let a=l.head,h=vs(n.doc,a),c;if(h==e){if(Qa(n,a))return{changes:{insert:e+e,from:a},effects:Mt.of(a+e.length),range:y.cursor(a+e.length)};if(ou(n,a)){let u=t&&n.sliceDoc(a,a+e.length*3)==e+e+e?e+e+e:e;return{changes:{from:a,to:a+u.length,insert:u},range:y.cursor(a+u.length)}}}else{if(t&&n.sliceDoc(a-2*e.length,a)==e+e&&(c=ka(n,a-2*e.length,s))>-1&&Qa(n,c))return{changes:{insert:e+e+e+e,from:a},effects:Mt.of(a+e.length),range:y.cursor(a+e.length)};if(n.charCategorizer(a)(h)!=G.Word&&ka(n,a,s)>-1&&!w1(n,a,e,s))return{changes:{insert:e+e,from:a},effects:Mt.of(a+e.length),range:y.cursor(a+e.length)}}return{range:r=l}});return r?null:n.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function Qa(n,e){let t=re(n).resolveInner(e+1);return t.parent&&t.from==e}function w1(n,e,t,i){let s=re(n).resolveInner(e,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=n.sliceDoc(s.from,Math.min(s.to,s.from+t.length+r)),a=l.indexOf(t);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let c=s.firstChild;for(;c&&c.from==s.from&&c.to-c.from>t.length+a;){if(n.sliceDoc(c.to-t.length,c.to)==t)return!1;c=c.firstChild}return!0}let h=s.to==e&&s.parent;if(!h)break;s=h}return!1}function ka(n,e,t){let i=n.charCategorizer(e);if(i(n.sliceDoc(e-1,e))!=G.Word)return e;for(let s of t){let r=e-s.length;if(n.sliceDoc(r,e)==s&&i(n.sliceDoc(r-1,r))!=G.Word)return r}return-1}function Q1(n={}){return[e1,we,fe.of(n),K0,k1,tu]}const lu=[{key:"Ctrl-Space",run:N0},{key:"Escape",run:I0},{key:"ArrowDown",run:Tn(!0)},{key:"ArrowUp",run:Tn(!1)},{key:"PageDown",run:Tn(!0,"page")},{key:"PageUp",run:Tn(!1,"page")},{key:"Enter",run:_0}],k1=Pt.highest(fi.computeN([fe],n=>n.facet(fe).defaultKeymap?[lu]:[])),P1=[Sp(),kp(),mc(),Hc(),um(),dc(),DO(),D.allowMultipleSelections.of(!0),Jp(),ko(Uc,{fallback:!0}),wm(),u1(),Q1(),ip(),rp(),IO(),o0(),fi.of([...m1,...Cf,...P0,...tf,...am,...lu,...Ug])],$1=[mc(),Hc(),dc(),ko(Uc,{fallback:!0}),fi.of([...Cf,...tf])];/**
 * vue-codemirror6
 *
 * @description CodeMirror6 Component for vue2 and vue3.
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022-2024 By Masashi Yoshikawa All rights reserved.
 * @license MIT
 * @version 1.3.4
 * @see {@link https://github.com/logue/vue-codemirror6}
 */const v1=n=>n?Object.entries(n).reduce((e,[t,i])=>(t=t.charAt(0).toUpperCase()+t.slice(1),t=`on${t}`,{...e,[t]:i}),{}):{};function Pa(n,e={},t){const{props:i,domProps:s,on:r,...o}=e,l=r?v1(r):{};return Qu(n,{...o,...i,...s,...l},t)}const Z1=n=>typeof n=="function"?n():n;var $a=Na({name:"CodeMirror",model:{prop:"modelValue",event:"update:modelValue"},props:{modelValue:{type:String,default:""},theme:{type:Object,default:()=>({})},dark:{type:Boolean,default:!1},basic:{type:Boolean,default:!1},minimal:{type:Boolean,default:!1},placeholder:{type:String,default:void 0},wrap:{type:Boolean,default:!1},tab:{type:Boolean,default:!1},indentUnit:{type:String,default:void 0},allowMultipleSelections:{type:Boolean,default:!1},tabSize:{type:Number,default:void 0},lineSeparator:{type:String,default:void 0},readonly:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},extensions:{type:Array,default:()=>[]},phrases:{type:Object,default:()=>{}},lang:{type:Object,default:()=>{}},linter:{type:Function,default:void 0},linterConfig:{type:Object,default:()=>({})},forceLinting:{type:Boolean,default:!1},gutter:{type:Boolean,default:!1},gutterConfig:{type:Object,default:()=>{}},tag:{type:String,default:"div"}},emits:{"update:modelValue":(n="")=>!0,update:n=>!0,ready:n=>!0,focus:n=>!0,change:n=>!0,destroy:()=>!0},setup(n,e){const t=Qi(),i=Qi(n.modelValue),s=yu(new v),r=Ct({get:()=>s.value.hasFocus,set:w=>{w&&s.value.focus()}}),o=Ct({get:()=>s.value.state.selection,set:w=>s.value.dispatch({selection:w})}),l=Ct({get:()=>s.value.state.selection.main.head,set:w=>s.value.dispatch({selection:{anchor:w}})}),a=Ct({get:()=>s.value.state.toJSON(),set:w=>s.value.setState(D.fromJSON(w))}),h=Qi(0),c=Qi(0),f=Ct(()=>{const w=new ni,R=new ni;if(n.basic&&n.minimal)throw"[Vue CodeMirror] Both basic and minimal cannot be specified.";return[n.basic&&!n.minimal?P1:void 0,n.minimal&&!n.basic?$1:void 0,v.updateListener.of(V=>{var oe;e.emit("focus",s.value.hasFocus),h.value=(oe=s.value.state.doc)==null?void 0:oe.length,!(V.changes.empty||!V.docChanged)&&(n.linter&&(n.forceLinting&&ha(s.value),c.value=n.linter(s.value).length),e.emit("update",V))}),v.theme(n.theme,{dark:n.dark}),n.wrap?v.lineWrapping:void 0,n.tab?fi.of([Yg]):void 0,n.indentUnit?tn.of(n.indentUnit):void 0,D.allowMultipleSelections.of(n.allowMultipleSelections),n.tabSize?R.of(D.tabSize.of(n.tabSize)):void 0,n.phrases?D.phrases.of(n.phrases):void 0,D.readOnly.of(n.readonly),v.editable.of(!n.disabled),n.lineSeparator?D.lineSeparator.of(n.lineSeparator):void 0,n.lang?w.of(n.lang):void 0,n.linter?Lg(n.linter,n.linterConfig):void 0,n.linter&&n.gutter?e0(n.gutterConfig):void 0,n.placeholder?KO(n.placeholder):void 0,...n.extensions].filter(V=>!!V)});nr(f,w=>{var R;(R=s.value)==null||R.dispatch({effects:M.reconfigure.of(w)})},{immediate:!0}),nr(()=>n.modelValue,async w=>{if(s.value.composing||s.value.state.doc.toJSON().join(n.lineSeparator??`
`)===w)return;const R=!s.value.state.selection.ranges.every(V=>V.anchor<w.length&&V.head<w.length);s.value.dispatch({changes:{from:0,to:s.value.state.doc.length,insert:w},selection:R?{anchor:0,head:0}:s.value.state.selection,scrollIntoView:!0})},{immediate:!0}),xu(async()=>{let w=i.value;t.value&&(t.value.childNodes[0]&&(i.value!==""&&console.warn("[CodeMirror.vue] The <code-mirror> tag contains child elements that overwrite the `v-model` values."),w=t.value.childNodes[0].innerText.trim()),s.value=new v({parent:t.value,state:D.create({doc:w,extensions:f.value}),dispatch:R=>{s.value.update([R]),!(R.changes.empty||!R.docChanged)&&(e.emit("update:modelValue",R.state.doc.toString()??""),e.emit("change",R.state))}}),await Su(),e.emit("ready",{view:s.value,state:s.value.state,container:t.value}))}),wu(()=>{s.value.destroy(),e.emit("destroy")});const u=()=>{!n.linter||!s.value||(n.forceLinting&&ha(s.value),c.value=zg(s.value.state))},d=()=>{var w,R;(w=s.value)==null||w.dispatch({effects:M.reconfigure.of([])}),(R=s.value)==null||R.dispatch({effects:M.appendConfig.of(f.value)})},O=(w,R)=>s.value.state.sliceDoc(w,R),m=w=>s.value.state.doc.line(w+1).text,g=()=>s.value.state.doc.lines,b=()=>s.value.state.selection.main.head,S=()=>{let w;return(w=s.value.state.selection.ranges)!==null&&w!==void 0?w:[]},P=()=>{let w;return(w=s.value.state.sliceDoc(s.value.state.selection.main.from,s.value.state.selection.main.to))!==null&&w!==void 0?w:""},Q=()=>{const w=s.value.state;return w?w.selection.ranges.map(R=>w.sliceDoc(R.from,R.to)):[]},x=()=>s.value.state.selection.ranges.some(w=>!w.empty),Z=(w,R,V)=>s.value.dispatch({changes:{from:R,to:V,insert:w}}),$=w=>s.value.dispatch(s.value.state.replaceSelection(w)),W=w=>s.value.dispatch({selection:{anchor:w}}),z=(w,R)=>s.value.dispatch({selection:{anchor:w,head:R}}),j=(w,R)=>s.value.dispatch({selection:y.create(w,R)}),Y=w=>s.value.dispatch({selection:y.create(o.value.ranges.map(R=>R.extend(w(R))))}),X={editor:t,view:s,cursor:l,selection:o,focus:r,length:h,json:a,diagnosticCount:c,dom:s.value.contentDOM,lint:u,forceReconfigure:d,getRange:O,getLine:m,lineCount:g,getCursor:b,listSelections:S,getSelection:P,getSelections:Q,somethingSelected:x,replaceRange:Z,replaceSelection:$,setCursor:W,setSelection:z,setSelections:j,extendSelectionsBy:Y};return e.expose(X),X},render(){return Pa(this.$props.tag,{ref:"editor",class:"vue-codemirror"},this.$slots.default?Pa("aside",{style:"display: none;","aria-hidden":"true"},Z1(this.$slots.default)):void 0)}});const C1="#e5c07b",va="#e06c75",T1="#56b6c2",R1="#ffffff",En="#abb2bf",Kr="#7d8799",A1="#61afef",M1="#98c379",Za="#d19a66",W1="#c678dd",X1="#21252b",Ca="#2c313a",Ta="#282c34",Js="#353a42",Y1="#3E4451",Ra="#528bff",D1=v.theme({"&":{color:En,backgroundColor:Ta},".cm-content":{caretColor:Ra},".cm-cursor, .cm-dropCursor":{borderLeftColor:Ra},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Y1},".cm-panels":{backgroundColor:X1,color:En},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:"1px solid #457dff"},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:"#6199ff2f"},".cm-activeLine":{backgroundColor:"#6699ff0b"},".cm-selectionMatch":{backgroundColor:"#aafe661a"},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bad0f847"},".cm-gutters":{backgroundColor:Ta,color:Kr,border:"none"},".cm-activeLineGutter":{backgroundColor:Ca},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Js},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Js,borderBottomColor:Js},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Ca,color:En}}},{dark:!0}),q1=sn.define([{tag:p.keyword,color:W1},{tag:[p.name,p.deleted,p.character,p.propertyName,p.macroName],color:va},{tag:[p.function(p.variableName),p.labelName],color:A1},{tag:[p.color,p.constant(p.name),p.standard(p.name)],color:Za},{tag:[p.definition(p.name),p.separator],color:En},{tag:[p.typeName,p.className,p.number,p.changed,p.annotation,p.modifier,p.self,p.namespace],color:C1},{tag:[p.operator,p.operatorKeyword,p.url,p.escape,p.regexp,p.link,p.special(p.string)],color:T1},{tag:[p.meta,p.comment],color:Kr},{tag:p.strong,fontWeight:"bold"},{tag:p.emphasis,fontStyle:"italic"},{tag:p.strikethrough,textDecoration:"line-through"},{tag:p.link,color:Kr,textDecoration:"underline"},{tag:p.heading,fontWeight:"bold",color:va},{tag:[p.atom,p.bool,p.special(p.variableName)],color:Za},{tag:[p.processingInstruction,p.string,p.inserted],color:M1},{tag:p.invalid,color:R1}]),Aa=[D1,ko(q1)];var Ma={};class us{constructor(e,t,i,s,r,o,l,a,h,c=0,f){this.p=e,this.stack=t,this.state=i,this.reducePos=s,this.pos=r,this.score=o,this.buffer=l,this.bufferBase=a,this.curContext=h,this.lookAhead=c,this.parent=f}toString(){return`[${this.stack.filter((e,t)=>t%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(e,t,i=0){let s=e.parser.context;return new us(e,[],t,i,i,0,[],0,s?new Wa(s,s.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(e,t){this.stack.push(this.state,t,this.bufferBase+this.buffer.length),this.state=e}reduce(e){var t;let i=e>>19,s=e&65535,{parser:r}=this.p;this.reducePos<this.pos-25&&this.setLookAhead(this.pos);let o=r.dynamicPrecedence(s);if(o&&(this.score+=o),i==0){this.pushState(r.getGoto(this.state,s,!0),this.reducePos),s<r.minRepeatTerm&&this.storeNode(s,this.reducePos,this.reducePos,4,!0),this.reduceContext(s,this.reducePos);return}let l=this.stack.length-(i-1)*3-(e&262144?6:0),a=l?this.stack[l-2]:this.p.ranges[0].from,h=this.reducePos-a;h>=2e3&&!(!((t=this.p.parser.nodeSet.types[s])===null||t===void 0)&&t.isAnonymous)&&(a==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=h):this.p.lastBigReductionSize<h&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=a,this.p.lastBigReductionSize=h));let c=l?this.stack[l-1]:0,f=this.bufferBase+this.buffer.length-c;if(s<r.minRepeatTerm||e&131072){let u=r.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(s,a,u,f+4,!0)}if(e&262144)this.state=this.stack[l];else{let u=this.stack[l-3];this.state=r.getGoto(u,s,!0)}for(;this.stack.length>l;)this.stack.pop();this.reduceContext(s,a)}storeNode(e,t,i,s=4,r=!1){if(e==0&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let o=this,l=this.buffer.length;if(l==0&&o.parent&&(l=o.bufferBase-o.parent.bufferBase,o=o.parent),l>0&&o.buffer[l-4]==0&&o.buffer[l-1]>-1){if(t==i)return;if(o.buffer[l-2]>=t){o.buffer[l-2]=i;return}}}if(!r||this.pos==i)this.buffer.push(e,t,i,s);else{let o=this.buffer.length;if(o>0&&this.buffer[o-4]!=0)for(;o>0&&this.buffer[o-2]>i;)this.buffer[o]=this.buffer[o-4],this.buffer[o+1]=this.buffer[o-3],this.buffer[o+2]=this.buffer[o-2],this.buffer[o+3]=this.buffer[o-1],o-=4,s>4&&(s-=4);this.buffer[o]=e,this.buffer[o+1]=t,this.buffer[o+2]=i,this.buffer[o+3]=s}}shift(e,t,i,s){if(e&131072)this.pushState(e&65535,this.pos);else if(e&262144)this.pos=s,this.shiftContext(t,i),t<=this.p.parser.maxNode&&this.buffer.push(t,i,s,4);else{let r=e,{parser:o}=this.p;(s>this.pos||t<=o.maxNode)&&(this.pos=s,o.stateFlag(r,1)||(this.reducePos=s)),this.pushState(r,i),this.shiftContext(t,i),t<=o.maxNode&&this.buffer.push(t,i,s,4)}}apply(e,t,i,s){e&65536?this.reduce(e):this.shift(e,t,i,s)}useNode(e,t){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=e)&&(this.p.reused.push(e),i++);let s=this.pos;this.reducePos=this.pos=s+e.length,this.pushState(t,s),this.buffer.push(i,s,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,e,this,this.p.stream.reset(this.pos-e.length)))}split(){let e=this,t=e.buffer.length;for(;t>0&&e.buffer[t-2]>e.reducePos;)t-=4;let i=e.buffer.slice(t),s=e.bufferBase+t;for(;e&&s==e.bufferBase;)e=e.parent;return new us(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,s,this.curContext,this.lookAhead,e)}recoverByDelete(e,t){let i=e<=this.p.parser.maxNode;i&&this.storeNode(e,this.pos,t,4),this.storeNode(0,this.pos,t,i?8:4),this.pos=this.reducePos=t,this.score-=190}canShift(e){for(let t=new z1(this);;){let i=this.p.parser.stateSlot(t.state,4)||this.p.parser.hasAction(t.state,e);if(i==0)return!1;if(!(i&65536))return!0;t.reduce(i)}}recoverByInsert(e){if(this.stack.length>=300)return[];let t=this.p.parser.nextStates(this.state);if(t.length>8||this.stack.length>=120){let s=[];for(let r=0,o;r<t.length;r+=2)(o=t[r+1])!=this.state&&this.p.parser.hasAction(o,e)&&s.push(t[r],o);if(this.stack.length<120)for(let r=0;s.length<8&&r<t.length;r+=2){let o=t[r+1];s.some((l,a)=>a&1&&l==o)||s.push(t[r],o)}t=s}let i=[];for(let s=0;s<t.length&&i.length<4;s+=2){let r=t[s+1];if(r==this.state)continue;let o=this.split();o.pushState(r,this.pos),o.storeNode(0,o.pos,o.pos,4,!0),o.shiftContext(t[s],this.pos),o.reducePos=this.pos,o.score-=200,i.push(o)}return i}forceReduce(){let{parser:e}=this.p,t=e.stateSlot(this.state,5);if(!(t&65536))return!1;if(!e.validAction(this.state,t)){let i=t>>19,s=t&65535,r=this.stack.length-i*3;if(r<0||e.getGoto(this.stack[r],s,!1)<0){let o=this.findForcedReduction();if(o==null)return!1;t=o}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(t),!0}findForcedReduction(){let{parser:e}=this.p,t=[],i=(s,r)=>{if(!t.includes(s))return t.push(s),e.allActions(s,o=>{if(!(o&393216))if(o&65536){let l=(o>>19)-r;if(l>1){let a=o&65535,h=this.stack.length-l*3;if(h>=0&&e.getGoto(this.stack[h],a,!1)>=0)return l<<19|65536|a}}else{let l=i(o,r+1);if(l!=null)return l}})};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(this.stack.length!=3)return!1;let{parser:e}=this.p;return e.data[e.stateSlot(this.state,1)]==65535&&!e.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(e){if(this.state!=e.state||this.stack.length!=e.stack.length)return!1;for(let t=0;t<this.stack.length;t+=3)if(this.stack[t]!=e.stack[t])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(e){return this.p.parser.dialect.flags[e]}shiftContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,e,this,this.p.stream.reset(t)))}reduceContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,e,this,this.p.stream.reset(t)))}emitContext(){let e=this.buffer.length-1;(e<0||this.buffer[e]!=-3)&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let e=this.buffer.length-1;(e<0||this.buffer[e]!=-4)&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(e){if(e!=this.curContext.context){let t=new Wa(this.curContext.tracker,e);t.hash!=this.curContext.hash&&this.emitContext(),this.curContext=t}}setLookAhead(e){e>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=e)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class Wa{constructor(e,t){this.tracker=e,this.context=t,this.hash=e.strict?e.hash(t):0}}class z1{constructor(e){this.start=e,this.state=e.state,this.stack=e.stack,this.base=this.stack.length}reduce(e){let t=e&65535,i=e>>19;i==0?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=(i-1)*3;let s=this.start.p.parser.getGoto(this.stack[this.base-3],t,!0);this.state=s}}class ds{constructor(e,t,i){this.stack=e,this.pos=t,this.index=i,this.buffer=e.buffer,this.index==0&&this.maybeNext()}static create(e,t=e.bufferBase+e.buffer.length){return new ds(e,t,t-e.bufferBase)}maybeNext(){let e=this.stack.parent;e!=null&&(this.index=this.stack.bufferBase-e.bufferBase,this.stack=e,this.buffer=e.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,this.index==0&&this.maybeNext()}fork(){return new ds(this.stack,this.pos,this.index)}}function Ci(n,e=Uint16Array){if(typeof n!="string")return n;let t=null;for(let i=0,s=0;i<n.length;){let r=0;for(;;){let o=n.charCodeAt(i++),l=!1;if(o==126){r=65535;break}o>=92&&o--,o>=34&&o--;let a=o-32;if(a>=46&&(a-=46,l=!0),r+=a,l)break;r*=46}t?t[s++]=r:t=new e(r)}return t}class Vn{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const Xa=new Vn;class j1{constructor(e,t){this.input=e,this.ranges=t,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=Xa,this.rangeIndex=0,this.pos=this.chunkPos=t[0].from,this.range=t[0],this.end=t[t.length-1].to,this.readNext()}resolveOffset(e,t){let i=this.range,s=this.rangeIndex,r=this.pos+e;for(;r<i.from;){if(!s)return null;let o=this.ranges[--s];r-=i.from-o.to,i=o}for(;t<0?r>i.to:r>=i.to;){if(s==this.ranges.length-1)return null;let o=this.ranges[++s];r+=o.from-i.to,i=o}return r}clipPos(e){if(e>=this.range.from&&e<this.range.to)return e;for(let t of this.ranges)if(t.to>e)return Math.max(e,t.from);return this.end}peek(e){let t=this.chunkOff+e,i,s;if(t>=0&&t<this.chunk.length)i=this.pos+e,s=this.chunk.charCodeAt(t);else{let r=this.resolveOffset(e,1);if(r==null)return-1;if(i=r,i>=this.chunk2Pos&&i<this.chunk2Pos+this.chunk2.length)s=this.chunk2.charCodeAt(i-this.chunk2Pos);else{let o=this.rangeIndex,l=this.range;for(;l.to<=i;)l=this.ranges[++o];this.chunk2=this.input.chunk(this.chunk2Pos=i),i+this.chunk2.length>l.to&&(this.chunk2=this.chunk2.slice(0,l.to-i)),s=this.chunk2.charCodeAt(0)}}return i>=this.token.lookAhead&&(this.token.lookAhead=i+1),s}acceptToken(e,t=0){let i=t?this.resolveOffset(t,-1):this.pos;if(i==null||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=e,this.token.end=i}acceptTokenTo(e,t){this.token.value=e,this.token.end=t}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:e,chunkPos:t}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=e,this.chunk2Pos=t,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let e=this.input.chunk(this.pos),t=this.pos+e.length;this.chunk=t>this.range.to?e.slice(0,this.range.to-this.pos):e,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(e=1){for(this.chunkOff+=e;this.pos+e>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();e-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=e,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(e,t){if(t?(this.token=t,t.start=e,t.lookAhead=e+1,t.value=t.extended=-1):this.token=Xa,this.pos!=e){if(this.pos=e,e==this.end)return this.setDone(),this;for(;e<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;e>=this.range.to;)this.range=this.ranges[++this.rangeIndex];e>=this.chunkPos&&e<this.chunkPos+this.chunk.length?this.chunkOff=e-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(e,t){if(e>=this.chunkPos&&t<=this.chunkPos+this.chunk.length)return this.chunk.slice(e-this.chunkPos,t-this.chunkPos);if(e>=this.chunk2Pos&&t<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(e-this.chunk2Pos,t-this.chunk2Pos);if(e>=this.range.from&&t<=this.range.to)return this.input.read(e,t);let i="";for(let s of this.ranges){if(s.from>=t)break;s.to>e&&(i+=this.input.read(Math.max(s.from,e),Math.min(s.to,t)))}return i}}class ti{constructor(e,t){this.data=e,this.id=t}token(e,t){let{parser:i}=t.p;au(this.data,e,t,this.id,i.data,i.tokenPrecTable)}}ti.prototype.contextual=ti.prototype.fallback=ti.prototype.extend=!1;class Jr{constructor(e,t,i){this.precTable=t,this.elseToken=i,this.data=typeof e=="string"?Ci(e):e}token(e,t){let i=e.pos,s=0;for(;;){let r=e.next<0,o=e.resolveOffset(1,1);if(au(this.data,e,t,0,this.data,this.precTable),e.token.value>-1)break;if(this.elseToken==null)return;if(r||s++,o==null)break;e.reset(o,e.token)}s&&(e.reset(i,e.token),e.acceptToken(this.elseToken,s))}}Jr.prototype.contextual=ti.prototype.fallback=ti.prototype.extend=!1;class Zs{constructor(e,t={}){this.token=e,this.contextual=!!t.contextual,this.fallback=!!t.fallback,this.extend=!!t.extend}}function au(n,e,t,i,s,r){let o=0,l=1<<i,{dialect:a}=t.p.parser;e:for(;l&n[o];){let h=n[o+1];for(let d=o+3;d<h;d+=2)if((n[d+1]&l)>0){let O=n[d];if(a.allows(O)&&(e.token.value==-1||e.token.value==O||E1(O,e.token.value,s,r))){e.acceptToken(O);break}}let c=e.next,f=0,u=n[o+2];if(e.next<0&&u>f&&n[h+u*3-3]==65535){o=n[h+u*3-1];continue e}for(;f<u;){let d=f+u>>1,O=h+d+(d<<1),m=n[O],g=n[O+1]||65536;if(c<m)u=d;else if(c>=g)f=d+1;else{o=n[O+2],e.advance();continue e}}break}}function Ya(n,e,t){for(let i=e,s;(s=n[i])!=65535;i++)if(s==t)return i-e;return-1}function E1(n,e,t,i){let s=Ya(t,i,e);return s<0||Ya(t,i,n)<s}const Ce=typeof process<"u"&&Ma&&/\bparse\b/.test(Ma.LOG);let er=null;function Da(n,e,t){let i=n.cursor(se.IncludeAnonymous);for(i.moveTo(e);;)if(!(t<0?i.childBefore(e):i.childAfter(e)))for(;;){if((t<0?i.to<e:i.from>e)&&!i.type.isError)return t<0?Math.max(0,Math.min(i.to-1,e-25)):Math.min(n.length,Math.max(i.from+1,e+25));if(t<0?i.prevSibling():i.nextSibling())break;if(!i.parent())return t<0?0:n.length}}class V1{constructor(e,t){this.fragments=e,this.nodeSet=t,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let e=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(e){for(this.safeFrom=e.openStart?Da(e.tree,e.from+e.offset,1)-e.offset:e.from,this.safeTo=e.openEnd?Da(e.tree,e.to+e.offset,-1)-e.offset:e.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(e.tree),this.start.push(-e.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(e){if(e<this.nextStart)return null;for(;this.fragment&&this.safeTo<=e;)this.nextFragment();if(!this.fragment)return null;for(;;){let t=this.trees.length-1;if(t<0)return this.nextFragment(),null;let i=this.trees[t],s=this.index[t];if(s==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let r=i.children[s],o=this.start[t]+i.positions[s];if(o>e)return this.nextStart=o,null;if(r instanceof J){if(o==e){if(o<this.safeFrom)return null;let l=o+r.length;if(l<=this.safeTo){let a=r.prop(q.lookAhead);if(!a||l+a<this.fragment.to)return r}}this.index[t]++,o+r.length>=Math.max(this.safeFrom,e)&&(this.trees.push(r),this.start.push(o),this.index.push(0))}else this.index[t]++,this.nextStart=o+r.length}}}class B1{constructor(e,t){this.stream=t,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=e.tokenizers.map(i=>new Vn)}getActions(e){let t=0,i=null,{parser:s}=e.p,{tokenizers:r}=s,o=s.stateSlot(e.state,3),l=e.curContext?e.curContext.hash:0,a=0;for(let h=0;h<r.length;h++){if(!(1<<h&o))continue;let c=r[h],f=this.tokens[h];if(!(i&&!c.fallback)&&((c.contextual||f.start!=e.pos||f.mask!=o||f.context!=l)&&(this.updateCachedToken(f,c,e),f.mask=o,f.context=l),f.lookAhead>f.end+25&&(a=Math.max(f.lookAhead,a)),f.value!=0)){let u=t;if(f.extended>-1&&(t=this.addActions(e,f.extended,f.end,t)),t=this.addActions(e,f.value,f.end,t),!c.extend&&(i=f,t>u))break}}for(;this.actions.length>t;)this.actions.pop();return a&&e.setLookAhead(a),!i&&e.pos==this.stream.end&&(i=new Vn,i.value=e.p.parser.eofTerm,i.start=i.end=e.pos,t=this.addActions(e,i.value,i.end,t)),this.mainToken=i,this.actions}getMainToken(e){if(this.mainToken)return this.mainToken;let t=new Vn,{pos:i,p:s}=e;return t.start=i,t.end=Math.min(i+1,s.stream.end),t.value=i==s.stream.end?s.parser.eofTerm:0,t}updateCachedToken(e,t,i){let s=this.stream.clipPos(i.pos);if(t.token(this.stream.reset(s,e),i),e.value>-1){let{parser:r}=i.p;for(let o=0;o<r.specialized.length;o++)if(r.specialized[o]==e.value){let l=r.specializers[o](this.stream.read(e.start,e.end),i);if(l>=0&&i.p.parser.dialect.allows(l>>1)){l&1?e.extended=l>>1:e.value=l>>1;break}}}else e.value=0,e.end=this.stream.clipPos(s+1)}putAction(e,t,i,s){for(let r=0;r<s;r+=3)if(this.actions[r]==e)return s;return this.actions[s++]=e,this.actions[s++]=t,this.actions[s++]=i,s}addActions(e,t,i,s){let{state:r}=e,{parser:o}=e.p,{data:l}=o;for(let a=0;a<2;a++)for(let h=o.stateSlot(r,a?2:1);;h+=3){if(l[h]==65535)if(l[h+1]==1)h=ot(l,h+2);else{s==0&&l[h+1]==2&&(s=this.putAction(ot(l,h+2),t,i,s));break}l[h]==t&&(s=this.putAction(ot(l,h+1),t,i,s))}return s}}class U1{constructor(e,t,i,s){this.parser=e,this.input=t,this.ranges=s,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new j1(t,s),this.tokens=new B1(e,this.stream),this.topTerm=e.top[1];let{from:r}=s[0];this.stacks=[us.start(this,e.top[0],r)],this.fragments=i.length&&this.stream.end-r>e.bufferLength*4?new V1(i,e.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let e=this.stacks,t=this.minStackPos,i=this.stacks=[],s,r;if(this.bigReductionCount>300&&e.length==1){let[o]=e;for(;o.forceReduce()&&o.stack.length&&o.stack[o.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let o=0;o<e.length;o++){let l=e[o];for(;;){if(this.tokens.mainToken=null,l.pos>t)i.push(l);else{if(this.advanceStack(l,i,e))continue;{s||(s=[],r=[]),s.push(l);let a=this.tokens.getMainToken(l);r.push(a.value,a.end)}}break}}if(!i.length){let o=s&&N1(s);if(o)return Ce&&console.log("Finish with "+this.stackID(o)),this.stackToTree(o);if(this.parser.strict)throw Ce&&s&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+t);this.recovering||(this.recovering=5)}if(this.recovering&&s){let o=this.stoppedAt!=null&&s[0].pos>this.stoppedAt?s[0]:this.runRecovery(s,r,i);if(o)return Ce&&console.log("Force-finish "+this.stackID(o)),this.stackToTree(o.forceAll())}if(this.recovering){let o=this.recovering==1?1:this.recovering*3;if(i.length>o)for(i.sort((l,a)=>a.score-l.score);i.length>o;)i.pop();i.some(l=>l.reducePos>t)&&this.recovering--}else if(i.length>1){e:for(let o=0;o<i.length-1;o++){let l=i[o];for(let a=o+1;a<i.length;a++){let h=i[a];if(l.sameState(h)||l.buffer.length>500&&h.buffer.length>500)if((l.score-h.score||l.buffer.length-h.buffer.length)>0)i.splice(a--,1);else{i.splice(o--,1);continue e}}}i.length>12&&i.splice(12,i.length-12)}this.minStackPos=i[0].pos;for(let o=1;o<i.length;o++)i[o].pos<this.minStackPos&&(this.minStackPos=i[o].pos);return null}stopAt(e){if(this.stoppedAt!=null&&this.stoppedAt<e)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=e}advanceStack(e,t,i){let s=e.pos,{parser:r}=this,o=Ce?this.stackID(e)+" -> ":"";if(this.stoppedAt!=null&&s>this.stoppedAt)return e.forceReduce()?e:null;if(this.fragments){let h=e.curContext&&e.curContext.tracker.strict,c=h?e.curContext.hash:0;for(let f=this.fragments.nodeAt(s);f;){let u=this.parser.nodeSet.types[f.type.id]==f.type?r.getGoto(e.state,f.type.id):-1;if(u>-1&&f.length&&(!h||(f.prop(q.contextHash)||0)==c))return e.useNode(f,u),Ce&&console.log(o+this.stackID(e)+` (via reuse of ${r.getName(f.type.id)})`),!0;if(!(f instanceof J)||f.children.length==0||f.positions[0]>0)break;let d=f.children[0];if(d instanceof J&&f.positions[0]==0)f=d;else break}}let l=r.stateSlot(e.state,4);if(l>0)return e.reduce(l),Ce&&console.log(o+this.stackID(e)+` (via always-reduce ${r.getName(l&65535)})`),!0;if(e.stack.length>=8400)for(;e.stack.length>6e3&&e.forceReduce(););let a=this.tokens.getActions(e);for(let h=0;h<a.length;){let c=a[h++],f=a[h++],u=a[h++],d=h==a.length||!i,O=d?e:e.split(),m=this.tokens.mainToken;if(O.apply(c,f,m?m.start:O.pos,u),Ce&&console.log(o+this.stackID(O)+` (via ${c&65536?`reduce of ${r.getName(c&65535)}`:"shift"} for ${r.getName(f)} @ ${s}${O==e?"":", split"})`),d)return!0;O.pos>s?t.push(O):i.push(O)}return!1}advanceFully(e,t){let i=e.pos;for(;;){if(!this.advanceStack(e,null,null))return!1;if(e.pos>i)return qa(e,t),!0}}runRecovery(e,t,i){let s=null,r=!1;for(let o=0;o<e.length;o++){let l=e[o],a=t[o<<1],h=t[(o<<1)+1],c=Ce?this.stackID(l)+" -> ":"";if(l.deadEnd&&(r||(r=!0,l.restart(),Ce&&console.log(c+this.stackID(l)+" (restarted)"),this.advanceFully(l,i))))continue;let f=l.split(),u=c;for(let d=0;f.forceReduce()&&d<10&&(Ce&&console.log(u+this.stackID(f)+" (via force-reduce)"),!this.advanceFully(f,i));d++)Ce&&(u=this.stackID(f)+" -> ");for(let d of l.recoverByInsert(a))Ce&&console.log(c+this.stackID(d)+" (via recover-insert)"),this.advanceFully(d,i);this.stream.end>l.pos?(h==l.pos&&(h++,a=0),l.recoverByDelete(a,h),Ce&&console.log(c+this.stackID(l)+` (via recover-delete ${this.parser.getName(a)})`),qa(l,i)):(!s||s.score<l.score)&&(s=l)}return s}stackToTree(e){return e.close(),J.build({buffer:ds.create(e),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:e.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(e){let t=(er||(er=new WeakMap)).get(e);return t||er.set(e,t=String.fromCodePoint(this.nextStackID++)),t+e}}function qa(n,e){for(let t=0;t<e.length;t++){let i=e[t];if(i.pos==n.pos&&i.sameState(n)){e[t].score<n.score&&(e[t]=n);return}}e.push(n)}class L1{constructor(e,t,i){this.source=e,this.flags=t,this.disabled=i}allows(e){return!this.disabled||this.disabled[e]==0}}const tr=n=>n;class _1{constructor(e){this.start=e.start,this.shift=e.shift||tr,this.reduce=e.reduce||tr,this.reuse=e.reuse||tr,this.hash=e.hash||(()=>0),this.strict=e.strict!==!1}}class Hi extends vc{constructor(e){if(super(),this.wrappers=[],e.version!=14)throw new RangeError(`Parser version (${e.version}) doesn't match runtime version (14)`);let t=e.nodeNames.split(" ");this.minRepeatTerm=t.length;for(let l=0;l<e.repeatNodeCount;l++)t.push("");let i=Object.keys(e.topRules).map(l=>e.topRules[l][1]),s=[];for(let l=0;l<t.length;l++)s.push([]);function r(l,a,h){s[l].push([a,a.deserialize(String(h))])}if(e.nodeProps)for(let l of e.nodeProps){let a=l[0];typeof a=="string"&&(a=q[a]);for(let h=1;h<l.length;){let c=l[h++];if(c>=0)r(c,a,l[h++]);else{let f=l[h+-c];for(let u=-c;u>0;u--)r(l[h++],a,f);h++}}}this.nodeSet=new Oo(t.map((l,a)=>$e.define({name:a>=this.minRepeatTerm?void 0:l,id:a,props:s[a],top:i.indexOf(a)>-1,error:a==0,skipped:e.skippedNodes&&e.skippedNodes.indexOf(a)>-1}))),e.propSources&&(this.nodeSet=this.nodeSet.extend(...e.propSources)),this.strict=!1,this.bufferLength=Qc;let o=Ci(e.tokenData);this.context=e.context,this.specializerSpecs=e.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let l=0;l<this.specializerSpecs.length;l++)this.specialized[l]=this.specializerSpecs[l].term;this.specializers=this.specializerSpecs.map(za),this.states=Ci(e.states,Uint32Array),this.data=Ci(e.stateData),this.goto=Ci(e.goto),this.maxTerm=e.maxTerm,this.tokenizers=e.tokenizers.map(l=>typeof l=="number"?new ti(o,l):l),this.topRules=e.topRules,this.dialects=e.dialects||{},this.dynamicPrecedences=e.dynamicPrecedences||null,this.tokenPrecTable=e.tokenPrec,this.termNames=e.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(e,t,i){let s=new U1(this,e,t,i);for(let r of this.wrappers)s=r(s,e,t,i);return s}getGoto(e,t,i=!1){let s=this.goto;if(t>=s[0])return-1;for(let r=s[t+1];;){let o=s[r++],l=o&1,a=s[r++];if(l&&i)return a;for(let h=r+(o>>1);r<h;r++)if(s[r]==e)return a;if(l)return-1}}hasAction(e,t){let i=this.data;for(let s=0;s<2;s++)for(let r=this.stateSlot(e,s?2:1),o;;r+=3){if((o=i[r])==65535)if(i[r+1]==1)o=i[r=ot(i,r+2)];else{if(i[r+1]==2)return ot(i,r+2);break}if(o==t||o==0)return ot(i,r+1)}return 0}stateSlot(e,t){return this.states[e*6+t]}stateFlag(e,t){return(this.stateSlot(e,0)&t)>0}validAction(e,t){return!!this.allActions(e,i=>i==t?!0:null)}allActions(e,t){let i=this.stateSlot(e,4),s=i?t(i):void 0;for(let r=this.stateSlot(e,1);s==null;r+=3){if(this.data[r]==65535)if(this.data[r+1]==1)r=ot(this.data,r+2);else break;s=t(ot(this.data,r+1))}return s}nextStates(e){let t=[];for(let i=this.stateSlot(e,1);;i+=3){if(this.data[i]==65535)if(this.data[i+1]==1)i=ot(this.data,i+2);else break;if(!(this.data[i+2]&1)){let s=this.data[i+1];t.some((r,o)=>o&1&&r==s)||t.push(this.data[i],s)}}return t}configure(e){let t=Object.assign(Object.create(Hi.prototype),this);if(e.props&&(t.nodeSet=this.nodeSet.extend(...e.props)),e.top){let i=this.topRules[e.top];if(!i)throw new RangeError(`Invalid top rule name ${e.top}`);t.top=i}return e.tokenizers&&(t.tokenizers=this.tokenizers.map(i=>{let s=e.tokenizers.find(r=>r.from==i);return s?s.to:i})),e.specializers&&(t.specializers=this.specializers.slice(),t.specializerSpecs=this.specializerSpecs.map((i,s)=>{let r=e.specializers.find(l=>l.from==i.external);if(!r)return i;let o=Object.assign(Object.assign({},i),{external:r.to});return t.specializers[s]=za(o),o})),e.contextTracker&&(t.context=e.contextTracker),e.dialect&&(t.dialect=this.parseDialect(e.dialect)),e.strict!=null&&(t.strict=e.strict),e.wrap&&(t.wrappers=t.wrappers.concat(e.wrap)),e.bufferLength!=null&&(t.bufferLength=e.bufferLength),t}hasWrappers(){return this.wrappers.length>0}getName(e){return this.termNames?this.termNames[e]:String(e<=this.maxNode&&this.nodeSet.types[e].name||e)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(e){let t=this.dynamicPrecedences;return t==null?0:t[e]||0}parseDialect(e){let t=Object.keys(this.dialects),i=t.map(()=>!1);if(e)for(let r of e.split(" ")){let o=t.indexOf(r);o>=0&&(i[o]=!0)}let s=null;for(let r=0;r<t.length;r++)if(!i[r])for(let o=this.dialects[t[r]],l;(l=this.data[o++])!=65535;)(s||(s=new Uint8Array(this.maxTerm+1)))[l]=1;return new L1(e,i,s)}static deserialize(e){return new Hi(e)}}function ot(n,e){return n[e]|n[e+1]<<16}function N1(n){let e=null;for(let t of n){let i=t.p.stoppedAt;(t.pos==t.p.stream.end||i!=null&&t.pos>i)&&t.p.parser.stateFlag(t.state,2)&&(!e||e.score<t.score)&&(e=t)}return e}function za(n){if(n.external){let e=n.extend?1:0;return(t,i)=>n.external(t,i)<<1|e}return n.get}const I1=bo({String:p.string,Number:p.number,"True False":p.bool,PropertyName:p.propertyName,Null:p.null,",":p.separator,"[ ]":p.squareBracket,"{ }":p.brace}),G1=Hi.deserialize({version:14,states:"$bOVQPOOOOQO'#Cb'#CbOnQPO'#CeOvQPO'#CjOOQO'#Cp'#CpQOQPOOOOQO'#Cg'#CgO}QPO'#CfO!SQPO'#CrOOQO,59P,59PO![QPO,59PO!aQPO'#CuOOQO,59U,59UO!iQPO,59UOVQPO,59QOqQPO'#CkO!nQPO,59^OOQO1G.k1G.kOVQPO'#ClO!vQPO,59aOOQO1G.p1G.pOOQO1G.l1G.lOOQO,59V,59VOOQO-E6i-E6iOOQO,59W,59WOOQO-E6j-E6j",stateData:"#O~OcOS~OQSORSOSSOTSOWQO]ROePO~OVXOeUO~O[[O~PVOg^O~Oh_OVfX~OVaO~OhbO[iX~O[dO~Oh_OVfa~OhbO[ia~O",goto:"!kjPPPPPPkPPkqwPPk{!RPPP!XP!ePP!hXSOR^bQWQRf_TVQ_Q`WRg`QcZRicQTOQZRQe^RhbRYQR]R",nodeNames:"⚠ JsonText True False Null Number String } { Object Property PropertyName ] [ Array",maxTerm:25,nodeProps:[["isolate",-2,6,11,""],["openedBy",7,"{",12,"["],["closedBy",8,"}",13,"]"]],propSources:[I1],skippedNodes:[0],repeatNodeCount:2,tokenData:"(|~RaXY!WYZ!W]^!Wpq!Wrs!]|}$u}!O$z!Q!R%T!R![&c![!]&t!}#O&y#P#Q'O#Y#Z'T#b#c'r#h#i(Z#o#p(r#q#r(w~!]Oc~~!`Wpq!]qr!]rs!xs#O!]#O#P!}#P;'S!];'S;=`$o<%lO!]~!}Oe~~#QXrs!]!P!Q!]#O#P!]#U#V!]#Y#Z!]#b#c!]#f#g!]#h#i!]#i#j#m~#pR!Q![#y!c!i#y#T#Z#y~#|R!Q![$V!c!i$V#T#Z$V~$YR!Q![$c!c!i$c#T#Z$c~$fR!Q![!]!c!i!]#T#Z!]~$rP;=`<%l!]~$zOh~~$}Q!Q!R%T!R![&c~%YRT~!O!P%c!g!h%w#X#Y%w~%fP!Q![%i~%nRT~!Q![%i!g!h%w#X#Y%w~%zR{|&T}!O&T!Q![&Z~&WP!Q![&Z~&`PT~!Q![&Z~&hST~!O!P%c!Q![&c!g!h%w#X#Y%w~&yOg~~'OO]~~'TO[~~'WP#T#U'Z~'^P#`#a'a~'dP#g#h'g~'jP#X#Y'm~'rOR~~'uP#i#j'x~'{P#`#a(O~(RP#`#a(U~(ZOS~~(^P#f#g(a~(dP#i#j(g~(jP#X#Y(m~(rOQ~~(wOW~~(|OV~",tokenizers:[0],topRules:{JsonText:[0,1]},tokenPrec:0}),F1=Ui.define({name:"json",parser:G1.configure({props:[So.add({Object:Xi({except:/^\s*\}/}),Array:Xi({except:/^\s*\]/})}),Qo.add({"Object Array":Xc})]}),languageData:{closeBrackets:{brackets:["[","{",'"']},indentOnInput:/^\s*[\}\]]$/}});function ja(){return new Ac(F1)}const H1=312,Ea=1,K1=2,J1=3,eb=4,tb=313,ib=315,nb=316,sb=5,rb=6,ob=0,eo=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],hu=125,lb=59,to=47,ab=42,hb=43,cb=45,fb=60,ub=44,db=63,Ob=46,pb=new _1({start:!1,shift(n,e){return e==sb||e==rb||e==ib?n:e==nb},strict:!1}),mb=new Zs((n,e)=>{let{next:t}=n;(t==hu||t==-1||e.context)&&n.acceptToken(tb)},{contextual:!0,fallback:!0}),gb=new Zs((n,e)=>{let{next:t}=n,i;eo.indexOf(t)>-1||t==to&&((i=n.peek(1))==to||i==ab)||t!=hu&&t!=lb&&t!=-1&&!e.context&&n.acceptToken(H1)},{contextual:!0}),bb=new Zs((n,e)=>{let{next:t}=n;if(t==hb||t==cb){if(n.advance(),t==n.next){n.advance();let i=!e.context&&e.canShift(Ea);n.acceptToken(i?Ea:K1)}}else t==db&&n.peek(1)==Ob&&(n.advance(),n.advance(),(n.next<48||n.next>57)&&n.acceptToken(J1))},{contextual:!0});function ir(n,e){return n>=65&&n<=90||n>=97&&n<=122||n==95||n>=192||!e&&n>=48&&n<=57}const yb=new Zs((n,e)=>{if(n.next!=fb||!e.dialectEnabled(ob)||(n.advance(),n.next==to))return;let t=0;for(;eo.indexOf(n.next)>-1;)n.advance(),t++;if(ir(n.next,!0)){for(n.advance(),t++;ir(n.next,!1);)n.advance(),t++;for(;eo.indexOf(n.next)>-1;)n.advance(),t++;if(n.next==ub)return;for(let i=0;;i++){if(i==7){if(!ir(n.next,!0))return;break}if(n.next!="extends".charCodeAt(i))break;n.advance(),t++}}n.acceptToken(eb,-t)}),xb=bo({"get set async static":p.modifier,"for while do if else switch try catch finally return throw break continue default case":p.controlKeyword,"in of await yield void typeof delete instanceof":p.operatorKeyword,"let var const using function class extends":p.definitionKeyword,"import export from":p.moduleKeyword,"with debugger as new":p.keyword,TemplateString:p.special(p.string),super:p.atom,BooleanLiteral:p.bool,this:p.self,null:p.null,Star:p.modifier,VariableName:p.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":p.function(p.variableName),VariableDefinition:p.definition(p.variableName),Label:p.labelName,PropertyName:p.propertyName,PrivatePropertyName:p.special(p.propertyName),"CallExpression/MemberExpression/PropertyName":p.function(p.propertyName),"FunctionDeclaration/VariableDefinition":p.function(p.definition(p.variableName)),"ClassDeclaration/VariableDefinition":p.definition(p.className),PropertyDefinition:p.definition(p.propertyName),PrivatePropertyDefinition:p.definition(p.special(p.propertyName)),UpdateOp:p.updateOperator,"LineComment Hashbang":p.lineComment,BlockComment:p.blockComment,Number:p.number,String:p.string,Escape:p.escape,ArithOp:p.arithmeticOperator,LogicOp:p.logicOperator,BitOp:p.bitwiseOperator,CompareOp:p.compareOperator,RegExp:p.regexp,Equals:p.definitionOperator,Arrow:p.function(p.punctuation),": Spread":p.punctuation,"( )":p.paren,"[ ]":p.squareBracket,"{ }":p.brace,"InterpolationStart InterpolationEnd":p.special(p.brace),".":p.derefOperator,", ;":p.separator,"@":p.meta,TypeName:p.typeName,TypeDefinition:p.definition(p.typeName),"type enum interface implements namespace module declare":p.definitionKeyword,"abstract global Privacy readonly override":p.modifier,"is keyof unique infer":p.operatorKeyword,JSXAttributeValue:p.attributeValue,JSXText:p.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":p.angleBracket,"JSXIdentifier JSXNameSpacedName":p.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":p.attributeName,"JSXBuiltin/JSXIdentifier":p.standard(p.tagName)}),Sb={__proto__:null,export:20,as:25,from:33,default:36,async:41,function:42,extends:54,this:58,true:66,false:66,null:78,void:82,typeof:86,super:102,new:136,delete:148,yield:157,await:161,class:166,public:229,private:229,protected:229,readonly:231,instanceof:250,satisfies:253,in:254,const:256,import:290,keyof:345,unique:349,infer:355,is:391,abstract:411,implements:413,type:415,let:418,var:420,using:423,interface:429,enum:433,namespace:439,module:441,declare:445,global:449,for:468,of:477,while:480,with:484,do:488,if:492,else:494,switch:498,case:504,try:510,catch:514,finally:518,return:522,throw:526,break:530,continue:534,debugger:538},wb={__proto__:null,async:123,get:125,set:127,declare:189,public:191,private:191,protected:191,static:193,abstract:195,override:197,readonly:203,accessor:205,new:395},Qb={__proto__:null,"<":187},kb=Hi.deserialize({version:14,states:"$=dO%TQ^OOO%[Q^OOO'_Q`OOP(lOWOOO*zQ?NdO'#CiO+RO!bO'#CjO+aO#tO'#CjO+oO!0LbO'#D^O.QQ^O'#DdO.bQ^O'#DoO%[Q^O'#DwO0fQ^O'#EPOOQ?Mr'#EX'#EXO1PQWO'#EUOOQO'#Em'#EmOOQO'#Ih'#IhO1XQWO'#GpO1dQWO'#ElO1iQWO'#ElO3hQ?NdO'#JmO6[Q?NdO'#JnO6uQWO'#F[O6zQ&jO'#FsOOQ?Mr'#Fe'#FeO7VO,YO'#FeO7eQ7[O'#FzO9RQWO'#FyOOQ?Mr'#Jn'#JnOOQ?Mp'#Jm'#JmO9WQWO'#GtOOQU'#KZ'#KZO9cQWO'#IUO9hQ?MxO'#IVOOQU'#JZ'#JZOOQU'#IZ'#IZQ`Q^OOO`Q^OOO9pQMnO'#DsO9wQ^O'#D{O:OQ^O'#D}O9^QWO'#GpO:VQ7[O'#CoO:eQWO'#EkO:pQWO'#EvO:uQ7[O'#FdO;dQWO'#GpOOQO'#K['#K[O;iQWO'#K[O;wQWO'#GxO;wQWO'#GyO;wQWO'#G{O9^QWO'#HOO<nQWO'#HRO>VQWO'#CeO>gQWO'#H_O>oQWO'#HeO>oQWO'#HgO`Q^O'#HiO>oQWO'#HkO>oQWO'#HnO>tQWO'#HtO>yQ?MyO'#HzO%[Q^O'#H|O?UQ?MyO'#IOO?aQ?MyO'#IQO9hQ?MxO'#ISO?lQ?NdO'#CiO@nQ`O'#DiQOQWOOO%[Q^O'#D}OAUQWO'#EQO:VQ7[O'#EkOAaQWO'#EkOAlQpO'#FdOOQU'#Cg'#CgOOQ?Mp'#Dn'#DnOOQ?Mp'#Jq'#JqO%[Q^O'#JqOOQO'#Jt'#JtOOQO'#Id'#IdOBlQ`O'#EdOOQ?Mp'#Ec'#EcOOQ?Mp'#Jx'#JxOChQ?NQO'#EdOCrQ`O'#ETOOQO'#Js'#JsODWQ`O'#JtOEeQ`O'#ETOCrQ`O'#EdPErO#@ItO'#CbPOOO)CDx)CDxOOOO'#I['#I[OE}O!bO,59UOOQ?Mr,59U,59UOOOO'#I]'#I]OF]O#tO,59UO%[Q^O'#D`OOOO'#I_'#I_OFkO!0LbO,59xOOQ?Mr,59x,59xOFyQ^O'#I`OG^QWO'#JoOI]QrO'#JoO+}Q^O'#JoOIdQWO,5:OOIzQWO'#EmOJXQWO'#KOOJdQWO'#J}OJdQWO'#J}OJlQWO,5;ZOJqQWO'#J|OOQ?Mv,5:Z,5:ZOJxQ^O,5:ZOLvQ?NdO,5:cOMgQWO,5:kONQQ?MxO'#J{ONXQWO'#JzO9WQWO'#JzONmQWO'#JzONuQWO,5;YONzQWO'#JzO!#PQrO'#JnOOQ?Mr'#Ci'#CiO%[Q^O'#EPO!#oQrO,5:pOOQQ'#Ju'#JuOOQO-E<f-E<fO9^QWO,5=[O!$VQWO,5=[O!$[Q^O,5;WO!&_Q7[O'#EhO!'xQWO,5;WO!'}Q^O'#DvO!(XQ`O,5;aO!(aQ`O,5;aO%[Q^O,5;aOOQU'#FS'#FSOOQU'#FU'#FUO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bO%[Q^O,5;bOOQU'#FY'#FYO!(oQ^O,5;sOOQ?Mr,5;x,5;xOOQ?Mr,5;y,5;yOOQ?Mr,5;{,5;{O%[Q^O'#IlO!*rQ?MxO,5<gO!&_Q7[O,5;bO!+aQ7[O,5;bO!-RQ7[O'#EZO%[Q^O,5;vOOQ?Mr,5;z,5;zO!-YQ&jO'#FiO!.VQ&jO'#KSO!-qQ&jO'#KSO!.^Q&jO'#KSOOQO'#KS'#KSO!.rQ&jO,5<ROOOS,5<_,5<_O!/TQ^O'#FuOOOS'#Ik'#IkO7VO,YO,5<PO!/[Q&jO'#FwOOQ?Mr,5<P,5<PO!/{Q!LQO'#CvOOQ?Mr'#Cz'#CzO!0`QWO'#CzO!0eO!0LbO'#DOO!1RQ7[O,5<dO!1YQWO,5<fO!2uQ$ISO'#GVO!3SQWO'#GWO!3XQWO'#GWO!4wQ$ISO'#G[O!5sQ`O'#G`OOQO'#Gk'#GkO!+hQ7[O'#GjOOQO'#Gm'#GmO!+hQ7[O'#GlO!6fQ!LQO'#JgOOQ?Mr'#Jg'#JgO!6pQWO'#JfO!7OQWO'#JeO!7WQWO'#CuOOQ?Mr'#Cx'#CxOOQ?Mr'#DS'#DSOOQ?Mr'#DU'#DUO1SQWO'#DWO!+hQ7[O'#F}O!+hQ7[O'#GPO!7`QWO'#GRO!7eQWO'#GSO!3XQWO'#GYO!+hQ7[O'#G_O!7jQWO'#EnO!8XQWO,5<eOOQ?Mp'#Cr'#CrO!8aQWO'#EoO!9ZQ`O'#EpOOQ?Mp'#J|'#J|O!9bQ?MxO'#K]O9hQ?MxO,5=`O`Q^O,5>pOOQU'#Jc'#JcOOQU,5>q,5>qOOQU-E<X-E<XO!;aQ?NdO,5:_O!9UQ`O,5:]O!=zQ?NdO,5:gO%[Q^O,5:gO!@bQ?NdO,5:iOOQO,5@v,5@vO!ARQ7[O,5=[O!AaQ?MxO'#JdO9RQWO'#JdO!ArQ?MxO,59ZO!A}Q`O,59ZO!BVQ7[O,59ZO:VQ7[O,59ZO!BbQWO,5;WO!BjQWO'#H^O!COQWO'#K`O%[Q^O,5;|O!9UQ`O,5<OO!CWQWO,5=wO!C]QWO,5=wO!CbQWO,5=wO9hQ?MxO,5=wO;wQWO,5=gOOQO'#Cv'#CvO!CpQ`O,5=dO!CxQ7[O,5=eO!DTQWO,5=gO!DYQpO,5=jO!DbQWO'#K[O>tQWO'#HTO9^QWO'#HVO!DgQWO'#HVO:VQ7[O'#HXO!DlQWO'#HXOOQU,5=m,5=mO!DqQWO'#HYO!ESQWO'#CoO!EXQWO,59PO!EcQWO,59PO!GhQ^O,59POOQU,59P,59PO!GxQ?MxO,59PO%[Q^O,59PO!JTQ^O'#HaOOQU'#Hb'#HbOOQU'#Hc'#HcO`Q^O,5=yO!JkQWO,5=yO`Q^O,5>PO`Q^O,5>RO!JpQWO,5>TO`Q^O,5>VO!JuQWO,5>YO!JzQ^O,5>`OOQU,5>f,5>fO%[Q^O,5>fO9hQ?MxO,5>hOOQU,5>j,5>jO# UQWO,5>jOOQU,5>l,5>lO# UQWO,5>lOOQU,5>n,5>nO# rQ`O'#D[O%[Q^O'#JqO# |Q`O'#JqO#!kQ`O'#DjO#!|Q`O'#DjO#%_Q^O'#DjO#%fQWO'#JpO#%nQWO,5:TO#%sQWO'#EqO#&RQWO'#KPO#&ZQWO,5;[O#&`Q`O'#DjO#&mQ`O'#ESOOQ?Mr,5:l,5:lO%[Q^O,5:lO#&tQWO,5:lO>tQWO,5;VO!A}Q`O,5;VO!BVQ7[O,5;VO:VQ7[O,5;VO#&|QWO,5@]O#'RQ(CYO,5:pOOQO-E<b-E<bO#(XQ?NQO,5;OOCrQ`O,5:oO#(cQ`O,5:oOCrQ`O,5;OO!ArQ?MxO,5:oOOQ?Mp'#Eg'#EgOOQO,5;O,5;OO%[Q^O,5;OO#(pQ?MxO,5;OO#({Q?MxO,5;OO!A}Q`O,5:oOOQO,5;U,5;UO#)ZQ?MxO,5;OPOOO'#IY'#IYP#)oO#@ItO,58|POOO,58|,58|OOOO-E<Y-E<YOOQ?Mr1G.p1G.pOOOO-E<Z-E<ZO#)zQpO,59zOOOO-E<]-E<]OOQ?Mr1G/d1G/dO#*PQrO,5>zO+}Q^O,5>zOOQO,5?Q,5?QO#*ZQ^O'#I`OOQO-E<^-E<^O#*hQWO,5@ZO#*pQrO,5@ZO#*wQWO,5@iOOQ?Mr1G/j1G/jO%[Q^O,5@jO#+PQWO'#IfOOQO-E<d-E<dO#*wQWO,5@iOOQ?Mp1G0u1G0uOOQ?Mv1G/u1G/uOOQ?Mv1G0V1G0VO%[Q^O,5@gO#+eQ?MxO,5@gO#+vQ?MxO,5@gO#+}QWO,5@fO9WQWO,5@fO#,VQWO,5@fO#,eQWO'#IiO#+}QWO,5@fOOQ?Mp1G0t1G0tO!(XQ`O,5:rO!(dQ`O,5:rOOQQ,5:t,5:tO#-VQYO,5:tO#-_Q7[O1G2vO9^QWO1G2vOOQ?Mr1G0r1G0rO#-mQ?NdO1G0rO#.rQ?NbO,5;SOOQ?Mr'#GU'#GUO#/`Q?NdO'#JgO!$[Q^O1G0rO#1hQrO'#JrO%[Q^O'#JrO#1rQWO,5:bOOQ?Mr'#D['#D[OOQ?Mr1G0{1G0{O%[Q^O1G0{OOQ?Mr1G1e1G1eO#1wQWO1G0{O#4]Q?NdO1G0|O#4dQ?NdO1G0|O#6zQ?NdO1G0|O#7RQ?NdO1G0|O#9YQ?NdO1G0|O#9pQ?NdO1G0|O#<gQ?NdO1G0|O#<nQ?NdO1G0|O#?OQ?NdO1G0|O#?]Q?NdO1G0|O#AWQ?NdO1G0|O#DWQ07bO'#CiO#FRQ07bO1G1_O#FYQ07bO'#JnO#FmQ?NdO,5?WOOQ?Mp-E<j-E<jO#GaQ?NdO1G0|OOQ?Mr1G0|1G0|O#IiQ7[O'#JwO#IsQWO,5:uO#IxQ?NdO1G1bO#JlQ&jO,5<VO#JtQ&jO,5<WO#J|Q&jO'#FnO#KeQWO'#FmOOQO'#KT'#KTOOQO'#Ij'#IjO#KjQ&jO1G1mOOQ?Mr1G1m1G1mOOOS1G1x1G1xO#K{Q07bO'#JmO#LVQWO,5<aO!(oQ^O,5<aOOOS-E<i-E<iOOQ?Mr1G1k1G1kO#L[Q`O'#KSOOQ?Mr,5<c,5<cO#LdQ`O,5<cOOQ?Mr,59f,59fO!&_Q7[O'#DQOOOO'#I^'#I^O#LiO!0LbO,59jOOQ?Mr,59j,59jO%[Q^O1G2OO!7eQWO'#InO#LtQ7[O,5<xOOQ?Mr,5<u,5<uO!+hQ7[O'#IqO#MdQ7[O,5=UO!+hQ7[O'#IsO#NVQ7[O,5=WO!&_Q7[O,5=YOOQO1G2Q1G2QO#NaQpO'#CrO#NtQ$ISO'#EoO$ sQ`O'#G`O$!aQpO,5<qO$!hQWO'#KWO9WQWO'#KWO$!vQWO,5<sO!+hQ7[O,5<rO$!{QWO'#GXO$#^QWO,5<rO$#cQpO'#GUO$#pQpO'#KXO$#zQWO'#KXO!&_Q7[O'#KXO$$PQWO,5<vO$$UQ`O'#GaO!5nQ`O'#GaO$$gQWO'#GcO$$lQWO'#GeO!3XQWO'#GhO$$qQ?MxO'#IpO$$|Q`O,5<zOOQ?Mv,5<z,5<zO$%TQ`O'#GaO$%cQ`O'#GbO$%kQ`O'#GbO$%pQ7[O,5=UO$&QQ7[O,5=WOOQ?Mr,5=Z,5=ZO!+hQ7[O,5@QO!+hQ7[O,5@QO$&bQWO'#IuO$&mQWO,5@PO$&uQWO,59aO$'iQ!LSO,59rOOQ?Mr'#Jk'#JkO$([Q7[O,5<iO$(}Q7[O,5<kO@fQWO,5<mOOQ?Mr,5<n,5<nO$)XQWO,5<tO$)^Q7[O,5<yO$)nQWO'#JzO!$[Q^O1G2PO$)sQWO1G2PO9WQWO'#J}O9WQWO'#EqO%[Q^O'#EqO9WQWO'#IwO$)xQ?MxO,5@wOOQU1G2z1G2zOOQU1G4[1G4[OOQ?Mr1G/y1G/yOOQ?Mr1G/w1G/wO$+zQ?NdO1G0ROOQU1G2v1G2vO!&_Q7[O1G2vO%[Q^O1G2vO#-bQWO1G2vO$.OQ7[O'#EhOOQ?Mp,5@O,5@OO$.YQ?MxO,<EMAIL>!ArQ?MxO1G.uO!A}Q`O1G.uO!BVQ7[O1G.uO$.kQWO1G0rO$.pQWO'#CiO$.{QWO'#KaO$/TQWO,5=xO$/YQWO'#KaO$/_QWO'#KaO$/mQWO'#I}O$/{QWO,5@zO$0TQrO1G1hOOQ?Mr1G1j1G1jO9^QWO1G3cO@fQWO1G3cO$0[QWO1G3cO$0aQWO1G3cOOQU1G3c1G3cO!DTQWO1G3RO!&_Q7[O1G3OO$0fQWO1G3OOOQU1G3P1G3PO!&_Q7[O1G3PO$0kQWO1G3PO$0sQ`O'#G}OOQU1G3R1G3RO!5nQ`O'#IyO!DYQpO1G3UOOQU1G3U1G3UOOQU,5=o,5=oO$0{Q7[O,5=qO9^QWO,5=qO$$lQWO,5=sO9RQWO,5=sO!A}Q`O,5=sO!BVQ7[O,5=sO:VQ7[O,5=sO$1ZQWO'#K_O$1fQWO,5=tOOQU1G.k1G.kO$1kQ?<EMAIL>$1vQWO1G.kO9hQ?MxO1G.kO$4OQrO,5@|O$4]QWO,5@|O9WQWO,5@|O$4hQ^O,5={O$4oQWO,5={OOQU1G3e1G3eO`Q^O1G3eOOQU1G3k1G3kOOQU1G3m1G3mO>oQWO1G3oO$4tQ^O1G3qO$8xQ^O'#HpOOQU1G3t1G3tO$9VQWO'#HvO>tQWO'#HxOOQU1G3z1G3zO$9_Q^O1G3zO9hQ?MxO1G4QOOQU1G4S1G4SOOQ?Mp'#G]'#G]O9hQ?MxO1G4UO9hQ?MxO1G4WO$=fQWO,5@]O!(oQ^O,5;]O9WQWO,5;]O>tQWO,5:UO!(oQ^O,5:UO!A}Q`O,5:UO$=kQ07bO,5:UOOQO,5;],5;]O$=uQ`O'#IaO$>]QWO,5@[OOQ?Mr1G/o1G/oO$>eQ`O'#IgO$>oQWO,5@kOOQ?Mp1G0v1G0vO#!|Q`O,5:UOOQO'#Ic'#IcO$>wQ`O,5:nOOQ?Mv,5:n,5:nO#&wQWO1G0WOOQ?Mr1G0W1G0WO%[Q^O1G0WOOQ?Mr1G0q1G0qO>tQWO1G0qO!A}Q`O1G0qO!BVQ7[O1G0qOOQ?Mp1G5w1G5wO!ArQ?MxO1G0ZOOQO1G0j1G0jO%[Q^O1G0jO$?OQ?MxO1G0jO$?ZQ?MxO1G0jO!A}Q`O1G0ZOCrQ`O1G0ZO$?iQ?MxO1G0jOOQO1G0Z1G0ZO$?}Q?NdO1G0jPOOO-E<W-E<WPOOO1G.h1G.hOOOO1G/f1G/fO$@XQpO,5<gO$@aQrO1G4fOOQO1G4l1G4lO%[Q^O,5>zO$@kQWO1G5uO$@sQWO1G6TO$@{QrO1G6UO9WQWO,5?QO$AVQ?NdO1G6RO%[Q^O1G6RO$AgQ?MxO1G6RO$AxQWO1G6QO$AxQWO1G6QO9WQWO1G6QO$BQQWO,5?TO9WQWO,5?TOOQO,5?T,5?TO$BfQWO,5?TO$)nQWO,5?TOOQO-E<g-E<gOOQQ1G0^1G0^OOQQ1G0`1G0`O#-YQWO1G0`OOQU7+(b7+(bO!&_Q7[O7+(bO%[Q^O7+(bO$BtQWO7+(bO$CPQ7[O7+(bO$C_Q?NdO,5=UO$EgQ?NdO,5=WO$GoQ?NdO,5=UO$I}Q?NdO,5=WO$L]Q?NdO,59rO$NbQ?NdO,5<iO%!jQ?NdO,5<kO%$rQ?NdO,5<yOOQ?Mr7+&^7+&^O%'QQ?NdO7+&^O%'tQ^O'#IbO%(RQWO,5@^O%(ZQrO,5@^OOQ?Mr1G/|1G/|O%(eQWO7+&gOOQ?Mr7+&g7+&gO%(jQ07bO,5:cO%[Q^O7+&yO%(tQ07bO,5:_O%)RQ07bO,5:gO%)]Q07bO,5:iO%)gQ7[O'#IeO%)qQWO,5@cOOQ?Mr1G0a1G0aOOQO1G1q1G1qOOQO1G1r1G1rO%)yQtO,5<YO!(oQ^O,5<XOOQO-E<h-E<hOOQ?Mr7+'X7+'XOOOS7+'d7+'dOOOS1G1{1G1{O%*UQWO1G1{OOQ?Mr1G1}1G1}O%*ZQpO,59lOOOO-E<[-E<[OOQ?Mr1G/U1G/UO%*bQ?NdO7+'jOOQ?Mr,5?Y,5?YO%+UQpO,5?YOOQ?Mr1G2d1G2dP!&_Q7[O'#InPOQ?Mr-E<l-E<lO%+tQ7[O,5?]OOQ?Mr-E<o-E<oO%,gQ7[O,5?_OOQ?Mr-E<q-E<qO%,qQpO1G2tO%,xQpO'#CrO%-`Q7[O'#J}O%-gQ^O'#EqOOQ?Mr1G2]1G2]O%-qQWO'#ImO%.VQWO,5@rO%.VQWO,5@rO%._QWO,5@rO%.jQWO,5@rOOQO1G2_1G2_O%.xQ7[O1G2^O!+hQ7[O1G2^O%/YQ$ISO'#IoO%/gQWO,5@sO!&_Q7[O,5@sO%/oQpO,5@sOOQ?Mr1G2b1G2bOOQ?Mp,5<{,5<{OOQ?Mp,5<|,5<|O$)nQWO,5<|OCcQWO,5<|O!A}Q`O,5<{OOQO'#Gd'#GdO%/yQWO,5<}OOQ?Mp,5=P,5=PO$)nQWO,5=SOOQO,5?[,5?[OOQO-E<n-E<nOOQ?Mv1G2f1G2fO!5nQ`O,5<{O%0RQWO,5<|O$$gQWO,5<}O!5nQ`O,5<|O!+hQ7[O'#IqO%0uQ7[O1G2pO!+hQ7[O'#IsO%1hQ7[O1G2rO%1rQ7[O1G5lO%1|Q7[O1G5lOOQO,5?a,5?aOOQO-E<s-E<sOOQO1G.{1G.{O!9UQ`O,59tO%[Q^O,59tOOQ?Mr,5<h,5<hO%2ZQWO1G2XO!+hQ7[O1G2`O%2`Q?NdO7+'kOOQ?Mr7+'k7+'kO!$[Q^O7+'kO%3SQWO,5;]OOQ?Mp,5?c,5?cOOQ?Mp-E<u-E<uO%3XQpO'#KYO#&wQWO7+(bO4UQrO7+(bO$BwQWO7+(bO%3cQ?NbO'#CiO%3vQ?NbO,5=QO%4hQWO,5=QOOQ?Mp1G5j1G5jOOQU7+$a7+$aO!ArQ?MxO7+$aO!A}Q`O7+$aO!$[Q^O7+&^O%4mQWO'#I|O%5UQWO,5@{OOQO1G3d1G3dO9^QWO,5@{O%5UQWO,5@{O%5^QWO,5@{OOQO,5?i,5?iOOQO-E<{-E<{OOQ?Mr7+'S7+'SO%5cQWO7+(}O9hQ?MxO7+(}O9^QWO7+(}O@fQWO7+(}OOQU7+(m7+(mO%5hQ?NbO7+(jO!&_Q7[O7+(jO%5rQpO7+(kOOQU7+(k7+(kO!&_Q7[O7+(kO%5yQWO'#K^O%6UQWO,5=iOOQO,5?e,5?eOOQO-E<w-E<wOOQU7+(p7+(pO%7eQ`O'#HWOOQU1G3]1G3]O!&_Q7[O1G3]O%[Q^O1G3]O%7lQWO1G3]O%7wQ7[O1G3]O9hQ?MxO1G3_O$$lQWO1G3_O9RQWO1G3_O!A}Q`O1G3_O!BVQ7[O1G3_O%8VQWO'#I{O%8kQWO,5@yO%8sQ`O,5@yOOQ?Mp1G3`1G3`OOQU7+$V7+$VO@fQWO7+$VO9hQ?MxO7+$VO%9OQWO7+$VO%[Q^O1G6hO%[Q^O1G6iO%9TQ?MxO1G6hO%9_Q^O1G3gO%9fQWO1G3gO%9kQ^O1G3gOOQU7+)P7+)PO9hQ?MxO7+)ZO`Q^O7+)]OOQU'#Kd'#KdOOQU'#JO'#JOO%9rQ^O,5>[OOQU,5>[,5>[O%[Q^O'#HqO%:PQWO'#HsOOQU,5>b,5>bO9WQWO,5>bOOQU,5>d,5>dOOQU7+)f7+)fOOQU7+)l7+)lOOQU7+)p7+)pOOQU7+)r7+)rO%:UQ`O1G5wO%:jQ07bO1G0wO%:tQWO1G0wOOQO1G/p1G/pO%;PQ07bO1G/pO>tQWO1G/pO!(oQ^O'#DjOOQO,5>{,5>{OOQO-E<_-E<_OOQO,5?R,5?ROOQO-E<e-E<eO!A}Q`O1G/pOOQO-E<a-E<aOOQ?Mv1G0Y1G0YOOQ?Mr7+%r7+%rO#&wQWO7+%rOOQ?Mr7+&]7+&]O>tQWO7+&]O!A}Q`O7+&]OOQO7+%u7+%uO$?}Q?NdO7+&UOOQO7+&U7+&UO%[Q^O7+&UO%;ZQ?MxO7+&UO!ArQ?MxO7+%uO!A}Q`O7+%uO%;fQ?MxO7+&UO%;tQ?NdO7++mO%[Q^O7++mO%<UQWO7++lO%<UQWO7++lOOQO1G4o1G4oO9WQWO1G4oO%<^QWO1G4oOOQQ7+%z7+%zO#&wQWO<<K|O4UQrO<<K|O%<lQWO<<K|OOQU<<K|<<K|O!&_Q7[O<<K|O%[Q^O<<K|O%<tQWO<<K|O%=PQ?NdO,5?]O%?XQ?NdO,5?_O%AaQ?NdO1G2^O%CoQ?NdO1G2pO%EwQ?NdO1G2rO%HPQrO,5>|O%[Q^O,5>|OOQO-E<`-E<`O%HZQWO1G5xOOQ?Mr<<JR<<JRO%HcQ07bO1G0rO%JjQ07bO1G0|O%JqQ07bO1G0|O%LrQ07bO1G0|O%LyQ07bO1G0|O%NkQ07bO1G0|O& RQ07bO1G0|O&#cQ07bO1G0|O&#jQ07bO1G0|O&%eQ07bO1G0|O&%rQ07bO1G0|O&'mQ07bO1G0|O&(QQ?NdO<<JeO&)VQ07bO1G0|O&*xQ07bO'#JgO&,{Q07bO1G1bO&-YQ07bO1G0RO&-dQ7[O,5?POOQO-E<c-E<cO!(oQ^O'#FpOOQO'#KU'#KUOOQO1G1t1G1tO&-nQWO1G1sO&-sQ07bO,5?WOOOS7+'g7+'gOOOO1G/W1G/WOOQ?Mr1G4t1G4tO!+hQ7[O7+(`O&0TQrO'#CiO&0_QWO,5?XO9WQWO,5?XOOQO-E<k-E<kO&0mQWO1G6^O&0mQWO1G6^O&0uQWO1G6^O&1QQ7[O7+'xO&1bQpO,5?ZO&1lQWO,5?ZO!&_Q7[O,5?ZOOQO-E<m-E<mO&1qQpO1G6_O&1{QWO1G6_OOQ?Mp1G2h1G2hO$)nQWO1G2hOOQ?Mp1G2g1G2gO&2TQWO1G2iO!&_Q7[O1G2iOOQ?Mp1G2n1G2nO!A}Q`O1G2gOCcQWO1G2hO&2YQWO1G2iO&2bQWO1G2hO&3UQ7[O,5?]OOQ?Mr-E<p-E<pO&3wQ7[O,5?_OOQ?Mr-E<r-E<rO!+hQ7[O7++WOOQ?Mr1G/`1G/`O&4RQWO1G/`OOQ?Mr7+'s7+'sO&4WQ7[O7+'zO&4hQ?NdO<<KVOOQ?Mr<<KV<<KVO&5[QWO1G0wO!&_Q7[O'#IvO&5aQWO,5@tO&7cQrO<<K|O!&_Q7[O1G2lOOQU<<G{<<G{O!ArQ?MxO<<G{O&7jQ?NdO<<IxOOQ?Mr<<Ix<<IxOOQO,5?h,5?hO&8^QWO,5?hO&8cQWO,5?hOOQO-E<z-E<zO&8qQWO1G6gO&8qQWO1G6gO9^QWO1G6gO@fQWO<<LiOOQU<<Li<<LiO&8yQWO<<LiO9hQ?MxO<<LiOOQU<<LU<<LUO%5hQ?NbO<<LUOOQU<<LV<<LVO%5rQpO<<LVO&9OQ`O'#IxO&9ZQWO,5@xO!(oQ^O,5@xOOQU1G3T1G3TO%-gQ^O'#JqOOQO'#Iz'#IzO9hQ?MxO'#IzO&9cQ`O,5=rOOQU,5=r,5=rO&9jQ`O'#EdO&:OQWO7+(wO&:TQWO7+(wOOQU7+(w7+(wO!&_Q7[O7+(wO%[Q^O7+(wO&:]QWO7+(wOOQU7+(y7+(yO9hQ?MxO7+(yO$$lQWO7+(yO9RQWO7+(yO!A}Q`O7+(yO&:hQWO,5?gOOQO-E<y-E<yOOQO'#HZ'#HZO&:sQWO1G6eO9hQ?MxO<<GqOOQU<<Gq<<GqO@fQWO<<GqO&:{QWO7+,SO&;QQWO7+,TO%[Q^O7+,SO%[Q^O7+,TOOQU7+)R7+)RO&;VQWO7+)RO&;[Q^O7+)RO&;cQWO7+)ROOQU<<Lu<<LuOOQU<<Lw<<LwOOQU-E<|-E<|OOQU1G3v1G3vO&;hQWO,5>]OOQU,5>_,5>_O&;mQWO1G3|O9WQWO7+&cO!(oQ^O7+&cOOQO7+%[7+%[O&;rQ07bO1G6UO>tQWO7+%[OOQ?Mr<<I^<<I^OOQ?Mr<<Iw<<IwO>tQWO<<IwOOQO<<Ip<<IpO$?}Q?NdO<<IpO%[Q^O<<IpOOQO<<Ia<<IaO!ArQ?MxO<<IaO&;|Q?MxO<<IpO&<XQ?NdO<= XO&<iQWO<= WOOQO7+*Z7+*ZO9WQWO7+*ZOOQUANAhANAhO&<qQrOANAhO!&_Q7[OANAhO#&wQWOANAhO4UQrOANAhO&<xQWOANAhO%[Q^OANAhO&=QQ?NdO7+'xO&?`Q?NdO,5?]O&AhQ?NdO,5?_O&CpQ?NdO7+'zO&FOQrO1G4hO&FYQ07bO7+&^O&HZQ07bO,5=UO&J_Q07bO,5=WO&JoQ07bO,5=UO&KPQ07bO,5=WO&KaQ07bO,59rO&MdQ07bO,5<iO' dQ07bO,5<kO'#dQ07bO,5<yO'%VQ07bO7+'jO'%dQ07bO7+'kO'%qQWO,5<[OOQO7+'_7+'_O'%vQ7[O<<KzOOQO1G4s1G4sO'%}QWO1G4sO'&YQWO1G4sO'&hQWO7++xO'&hQWO7++xO!&_Q7[O1G4uO'&pQpO1G4uO'&zQWO7++yOOQ?Mp7+(S7+(SO$)nQWO7+(TO''SQpO7+(TOOQ?Mp7+(R7+(RO$)nQWO7+(SO''ZQWO7+(TO!&_Q7[O7+(TOCcQWO7+(SO''`Q7[O<<NrOOQ?Mr7+$z7+$zO''jQpO,5?bOOQO-E<t-E<tO''tQ?NbO7+(WOOQUAN=gAN=gO9^QWO1G5SOOQO1G5S1G5SO'(UQWO1G5SO'(ZQWO7+,RO'(ZQWO7+,RO9hQ?MxOANBTO@fQWOANBTOOQUANBTANBTOOQUANApANApOOQUANAqANAqO'(cQWO,5?dOOQO-E<v-E<vO'(nQ07bO1G6dOOQO,5?f,5?fOOQO-E<x-E<xOOQU1G3^1G3^O%-gQ^O,5<}OOQU<<Lc<<LcO!&_Q7[O<<LcO&:OQWO<<LcO'(xQWO<<LcO%[Q^O<<LcOOQU<<Le<<LeO9hQ?MxO<<LeO$$lQWO<<LeO9RQWO<<LeO')QQ`O1G5RO')]QWO7+,POOQUAN=]AN=]O9hQ?MxOAN=]OOQU<= n<= nOOQU<= o<= oO')eQWO<= nO')jQWO<= oOOQU<<Lm<<LmO')oQWO<<LmO')tQ^O<<LmOOQU1G3w1G3wO>tQWO7+)hO'){QWO<<I}O'*WQ07bO<<I}OOQO<<Hv<<HvOOQ?MrAN?cAN?cOOQOAN?[AN?[O$?}Q?NdOAN?[OOQOAN>{AN>{O%[Q^OAN?[OOQO<<Mu<<MuOOQUG27SG27SO!&_Q7[OG27SO#&wQWOG27SO'*bQrOG27SO4UQrOG27SO'*iQWOG27SO'*qQ07bO<<JeO'+OQ07bO1G2^O',qQ07bO,5?]O'.qQ07bO,5?_O'0qQ07bO1G2pO'2qQ07bO1G2rO'4qQ07bO<<KVO'5OQ07bO<<IxOOQO1G1v1G1vO!+hQ7[OANAfOOQO7+*_7+*_O'5]QWO7+*_O'5hQWO<= dO'5pQpO7+*aOOQ?Mp<<Ko<<KoO$)nQWO<<KoOOQ?Mp<<Kn<<KnO'5zQpO<<KoO$)nQWO<<KnOOQO7+*n7+*nO9^QWO7+*nO'6RQWO<= mOOQUG27oG27oO9hQ?MxOG27oO!(oQ^O1G5OO'6ZQWO7+,OO&:OQWOANA}OOQUANA}ANA}O!&_Q7[OANA}O'6cQWOANA}OOQUANBPANBPO9hQ?MxOANBPO$$lQWOANBPOOQO'#H['#H[OOQO7+*m7+*mOOQUG22wG22wOOQUANEYANEYOOQUANEZANEZOOQUANBXANBXO'6kQWOANBXOOQU<<MS<<MSO!(oQ^OAN?iOOQOG24vG24vO$?}Q?NdOG24vO#&wQWOLD,nOOQULD,nLD,nO!&_Q7[OLD,nO'6pQrOLD,nO'6wQ07bO7+'xO'8jQ07bO,5?]O':jQ07bO,5?_O'<jQ07bO7+'zO'>]Q7[OG27QOOQO<<My<<MyOOQ?MpANAZANAZO$)nQWOANAZOOQ?MpANAYANAYOOQO<<NY<<NYOOQULD-ZLD-ZO'>mQ07bO7+*jOOQUG27iG27iO&:OQWOG27iO!&_Q7[OG27iOOQUG27kG27kO9hQ?MxOG27kOOQUG27sG27sO'>wQ07bOG25TOOQOLD*bLD*bOOQU!$(!Y!$(!YO#&wQWO!$(!YO!&_Q7[O!$(!YO'?RQ?NdOG27QOOQ?MpG26uG26uOOQULD-TLD-TO&:OQWOLD-TOOQULD-VLD-VOOQU!)9Et!)9EtO#&wQWO!)9EtOOQU!$(!o!$(!oOOQU!.K;`!.K;`O'AaQ07bOG27QO!(oQ^O'#DwO1PQWO'#EUO'CSQrO'#JmO'CZQMnO'#DsO'CbQ^O'#D{O'CiQrO'#CiO'FPQrO'#CiO!(oQ^O'#D}O'FaQ^O,5;WO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O,5;bO!(oQ^O'#IlO'HdQWO,5<gO'HlQ7[O,5;bO'JVQ7[O,5;bO!(oQ^O,5;vO!&_Q7[O'#GjO'HlQ7[O'#GjO!&_Q7[O'#GlO'HlQ7[O'#GlO1SQWO'#DWO1SQWO'#DWO!&_Q7[O'#F}O'HlQ7[O'#F}O!&_Q7[O'#GPO'HlQ7[O'#GPO!&_Q7[O'#G_O'HlQ7[O'#G_O!(oQ^O,5:gO'J^Q`O'#D[O!(oQ^O,5@jO'FaQ^O1G0rO'JhQ07bO'#CiO!(oQ^O1G2OO!&_Q7[O'#IqO'HlQ7[O'#IqO!&_Q7[O'#IsO'HlQ7[O'#IsO'JrQpO'#CrO!&_Q7[O,5<rO'HlQ7[O,5<rO'FaQ^O1G2PO!(oQ^O7+&yO!&_Q7[O1G2^O'HlQ7[O1G2^O!&_Q7[O'#IqO'HlQ7[O'#IqO!&_Q7[O'#IsO'HlQ7[O'#IsO!&_Q7[O1G2`O'HlQ7[O1G2`O'FaQ^O7+'kO'FaQ^O7+&^O!&_Q7[OANAfO'HlQ7[OANAfO'KVQWO'#ElO'K[QWO'#ElO'KdQWO'#F[O'KiQWO'#EvO'KnQWO'#KOO'KyQWO'#J|O'LUQWO,5;WO'LZQ7[O,5<dO'LbQWO'#GWO'LgQWO'#GWO'LlQWO,5<eO'LtQWO,5;WO'L|Q07bO1G1_O'MTQWO,5<rO'MYQWO,5<rO'M_QWO,5<tO'MdQWO,5<tO'MiQWO1G2PO'MnQWO1G0rO'MsQ7[O<<KzO'MzQ7[O<<KzO7eQ7[O'#FzO9RQWO'#FyOAaQWO'#EkO!(oQ^O,5;sO!3XQWO'#GWO!3XQWO'#GWO!3XQWO'#GYO!3XQWO'#GYO!+hQ7[O7+(`O!+hQ7[O7+(`O%,qQpO1G2tO%,qQpO1G2tO!&_Q7[O,5=YO!&_Q7[O,5=Y",stateData:"( O~O'wOS'xOSTOS'yRQ~OPYOQYOSfOY!VOaqOdzOeyOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![XO!fuO!iZO!lYO!mYO!nYO!pvO!rwO!uxO!y]O#t!PO$V|O%e}O%g!QO%i!OO%j!OO%k!OO%n!RO%p!SO%s!TO%t!TO%v!UO&S!WO&Y!XO&[!YO&^!ZO&`![O&c!]O&i!^O&o!_O&q!`O&s!aO&u!bO&w!cO(OSO(QTO(TUO([VO(j[O(yiO~OWtO~P`OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(O!dO(QTO(TUO([VO(j[O(yiO~Oa!wOp!nO!P!oO!_!yO!`!vO!a!vO!y:lO#Q!pO#R!pO#S!xO#T!pO#U!pO#X!zO#Y!zO(P!lO(QTO(TUO(`!mO(j!sO~O'y!{O~OP]XR]X[]Xa]Xo]X}]X!P]X!Y]X!i]X!m]X#O]X#P]X#]]X#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X'u]X([]X(m]X(t]X(u]X~O!d%PX~P(qO_!}O(Q#PO(R!}O(S#PO~O_#QO(S#PO(T#PO(U#QO~Ou#SO!R#TO(]#TO(^#VO~OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(O:pO(QTO(TUO([VO(j[O(yiO~O!X#ZO!Y#WO!V(cP!V(qP~P+}O!Z#cO~P`OPYOQYOSfOd!jOe!iOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(QTO(TUO([VO(j[O(yiO~Om#mO!X#iO!y]O#f#lO#g#iO(O:qO!h(nP~P.iO!i#oO(O#nO~O!u#sO!y]O%e#tO~O#h#uO~O!d#vO#h#uO~OP$]OR#zO[$cOo$QO}#yO!P#{O!Y$`O!i#xO!m$]O#O$SO#k$OO#l$PO#m$PO#n$PO#o$RO#p$SO#q$SO#r$bO#s$SO#u$TO#w$VO#y$XO#z$YO([VO(m$ZO(t#|O(u#}O~Oa(aX'u(aX's(aX!h(aX!V(aX![(aX%f(aX!d(aX~P1qO#P$dO#]$eO$P$eOP(bXR(bX[(bXo(bX}(bX!P(bX!Y(bX!i(bX!m(bX#O(bX#k(bX#l(bX#m(bX#n(bX#o(bX#p(bX#q(bX#r(bX#s(bX#u(bX#w(bX#y(bX#z(bX([(bX(m(bX(t(bX(u(bX![(bX%f(bX~Oa(bX'u(bX's(bX!V(bX!h(bXs(bX!d(bX~P4UO#]$eO~O$[$hO$^$gO$e$mO~OSfO![$nO$h$oO$j$qO~Oh%WOm%XOo$uOp$tOq$tOw%YOy%ZO{%[O!P$|O![$}O!f%aO!i$yO#g%bO$V%_O$r%]O$t%^O$w%`O(O$sO(QTO(TUO([$vO(t%OO(u%QOg(XP~O!i%cO~O!P%fO![%gO(O%eO~O!d%kO~Oa%lO'u%lO~O}%pO~P%[O(P!lO~P%[O%k%tO~P%[Oh%WO!i%cO(O%eO(P!lO~Oe%{O!i%cO(O%eO~O#s$SO~O}&QO![%}O!i&PO%g&TO(O%eO(P!lO(QTO(TUO`)SP~O!u#sO~O%p&VO!P)OX![)OX(O)OX~O(O&WO~O!r&]O#t!PO%g!QO%i!OO%j!OO%k!OO%n!RO%p!SO%s!TO%t!TO~Od&bOe&aO!u&_O%e&`O%x&^O~P;|Od&eOeyO![&dO!r&]O!uxO!y]O#t!PO%e}O%i!OO%j!OO%k!OO%n!RO%p!SO%s!TO%t!TO%v!UO~Ob&hO#]&kO%g&fO(P!lO~P=RO!i&lO!r&pO~O!i#oO~O![XO~Oa%lO't&xO'u%lO~Oa%lO't&{O'u%lO~Oa%lO't&}O'u%lO~O's]X!V]Xs]X!h]X&W]X![]X%f]X!d]X~P(qO!_'[O!`'TO!a'TO(P!lO(QTO(TUO~Op'RO!P'QO!X'UO(`'PO!Z(dP!Z(sP~P@YOk'_O![']O(O%eO~Oe'dO!i%cO(O%eO~O}&QO!i&PO~Op!nO!P!oO!y:lO#Q!pO#R!pO#T!pO#U!pO(P!lO(QTO(TUO(`!mO(j!sO~O!_'jO!`'iO!a'iO#S!pO#X'kO#Y'kO~PAtOa%lOh%WO!d#vO!i%cO'u%lO(m'mO~O!m'qO#]'oO~PCSOp!nO!P!oO(QTO(TUO(`!mO(j!sO~O![XOp(hX!P(hX!_(hX!`(hX!a(hX!y(hX#Q(hX#R(hX#S(hX#T(hX#U(hX#X(hX#Y(hX(P(hX(Q(hX(T(hX(`(hX(j(hX~O!`'iO!a'iO(P!lO~PCrO'z'uO'{'uO'|'wO~O_!}O(Q'yO(R!}O(S'yO~O_#QO(S'yO(T'yO(U#QO~Ou#SO!R#TO(]#TO(^'}O~O!X(PO!V'SX!V'YX!Y'SX!Y'YX~P+}O!Y(RO!V(cX~OP$]OR#zO[$cOo$QO}#yO!P#{O!Y(RO!i#xO!m$]O#O$SO#k$OO#l$PO#m$PO#n$PO#o$RO#p$SO#q$SO#r$bO#s$SO#u$TO#w$VO#y$XO#z$YO([VO(m$ZO(t#|O(u#}O~O!V(cX~PGfO!V(WO~O!V(pX!Y(pX!d(pX!h(pX(m(pX~O#](pX#h#aX!Z(pX~PIiO#](XO!V(rX!Y(rX~O!Y(YO!V(qX~O!V(]O~O#]$eO~PIiO!Z(^O~P`OR#zO}#yO!P#{O!i#xO([VOP!ka[!kao!ka!Y!ka!m!ka#O!ka#k!ka#l!ka#m!ka#n!ka#o!ka#p!ka#q!ka#r!ka#s!ka#u!ka#w!ka#y!ka#z!ka(m!ka(t!ka(u!ka~Oa!ka'u!ka's!ka!V!ka!h!kas!ka![!ka%f!ka!d!ka~PKPO!h(_O~O!d#vO#](`O(m'mO!Y(oXa(oX'u(oX~O!h(oX~PMlO!P%fO![%gO!y]O#f(eO#g(dO(O%eO~O!Y(fO!h(nX~O!h(hO~O!P%fO![%gO#g(dO(O%eO~OP(bXR(bX[(bXo(bX}(bX!P(bX!Y(bX!i(bX!m(bX#O(bX#k(bX#l(bX#m(bX#n(bX#o(bX#p(bX#q(bX#r(bX#s(bX#u(bX#w(bX#y(bX#z(bX([(bX(m(bX(t(bX(u(bX~O!d#vO!h(bX~P! YOR(jO}(iO!i#xO#P$dO!y!xa!P!xa~O!u!xa%e!xa![!xa#f!xa#g!xa(O!xa~P!#ZO!u(nO~OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![XO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(O!dO(QTO(TUO([VO(j[O(yiO~Oh%WOm%XOo$uOp$tOq$tOw%YOy%ZO{;YO!P$|O![$}O!f<jO!i$yO#g;`O$V%_O$r;[O$t;^O$w%`O(O(rO(QTO(TUO([$vO(t%OO(u%QO~O#h(tO~O!X(vO!h(fP~P%[O(`(xO(j[O~O!P(zO!i#xO(`(xO(j[O~OP:kOQ:kOSfOd<fOe!iOmkOo:kOpkOqkOwkOy:kO{:kO!PWO!TkO!UkO![!eO!f:nO!iZO!l:kO!m:kO!n:kO!p:oO!r:rO!u!hO$V!kO(O)YO(QTO(TUO([VO(j[O(y<dO~O!Y$`Oa$oa'u$oa's$oa!h$oa!V$oa![$oa%f$oa!d$oa~O#t)`O~P!&_Oh%WOm%XOo$uOp$tOq$tOw%YOy%ZO{%[O!P$|O![$}O!f%aO!i$yO#g%bO$V%_O$r%]O$t%^O$w%`O(O(rO(QTO(TUO([$vO(t%OO(u%QO~Og(kP~P!+hO})eO!d)dO![$]X$Y$]X$[$]X$^$]X$e$]X~O!d)dO![(vX$Y(vX$[(vX$^(vX$e(vX~O})eO~P!-qO})eO![(vX$Y(vX$[(vX$^(vX$e(vX~O![)gO$Y)kO$[)fO$^)fO$e)lO~O!X)oO~P!(oO$[$hO$^$gO$e)sO~Ok$xX}$xX!P$xX#P$xX(t$xX(u$xX~OgjXg$xXkjX!YjX#]jX~P!/gOp)uO~Ou)vO(])wO(^)yO~Ok*SO}){O!P)|O(t%OO(u%QO~Og)zO~P!0pOg*TO~Oh%WOm%XOo$uOp$tOq$tOw%YOy%ZO{;YO!P*VO![*WO!f<jO!i$yO#g;`O$V%_O$r;[O$t;^O$w%`O(QTO(TUO([$vO(t%OO(u%QO~O!X*ZO(O*UO!h(zP~P!1_O#h*]O~O!i*^O~Oh%WOm%XOo$uOp$tOq$tOw%YOy%ZO{;YO!P$|O![$}O!f<jO!i$yO#g;`O$V%_O$r;[O$t;^O$w%`O(O*`O(QTO(TUO([$vO(t%OO(u%QO~O!X*cO!V({P~P!3^Oo*oO!P*gO!_*mO!`*fO!a*fO!i*^O#X*nO%]*iO(P!lO(`!mO~O!Z*lO~P!5RO#P$dOk(ZX}(ZX!P(ZX(t(ZX(u(ZX!Y(ZX#](ZX~Og(ZX#}(ZX~P!5zOk*tO#]*sOg(YX!Y(YX~O!Y*uOg(XX~O(O&WOg(XP~O!i*|O~O(O(rO~Om+QO!P%fO!X#iO![%gO!y]O#f#lO#g#iO(O%eO!h(nP~O!d#vO#h+RO~O!P%fO!X+TO!Y(YO![%gO(O%eO!V(qP~Op'XO!P+VO!X+UO(QTO(TUO(`(xO~O!Z(sP~P!8uO!Y+WOa)PX'u)PX~OP$]OR#zO[$cOo$QO}#yO!P#{O!i#xO!m$]O#O$SO#k$OO#l$PO#m$PO#n$PO#o$RO#p$SO#q$SO#r$bO#s$SO#u$TO#w$VO#y$XO#z$YO([VO(m$ZO(t#|O(u#}O~Oa!ga!Y!ga'u!ga's!ga!V!ga!h!gas!ga![!ga%f!ga!d!ga~P!9mOR#zO}#yO!P#{O!i#xO([VOP!oa[!oao!oa!Y!oa!m!oa#O!oa#k!oa#l!oa#m!oa#n!oa#o!oa#p!oa#q!oa#r!oa#s!oa#u!oa#w!oa#y!oa#z!oa(m!oa(t!oa(u!oa~Oa!oa'u!oa's!oa!V!oa!h!oas!oa![!oa%f!oa!d!oa~P!<TOR#zO}#yO!P#{O!i#xO([VOP!qa[!qao!qa!Y!qa!m!qa#O!qa#k!qa#l!qa#m!qa#n!qa#o!qa#p!qa#q!qa#r!qa#s!qa#u!qa#w!qa#y!qa#z!qa(m!qa(t!qa(u!qa~Oa!qa'u!qa's!qa!V!qa!h!qas!qa![!qa%f!qa!d!qa~P!>kOh%WOk+aO![']O%f+`O~O!d+cOa(WX![(WX'u(WX!Y(WX~Oa%lO![XO'u%lO~Oh%WO!i%cO~Oh%WO!i%cO(O%eO~O!d#vO#h(tO~Ob+nO%g+oO(O+kO(QTO(TUO!Z)TP~O!Y+pO`)SX~O[+tO~O`+uO~O![%}O(O%eO(P!lO`)SP~Oh%WO#]+zO~Oh%WOk+}O![$}O~O![,PO~O},RO![XO~O%k%tO~O!u,WO~Oe,]O~Ob,^O(O#nO(QTO(TUO!Z)RP~Oe%{O~O%g!QO(O&WO~P=RO[,cO`,bO~OPYOQYOSfOdzOeyOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO!fuO!iZO!lYO!mYO!nYO!pvO!uxO!y]O%e}O(QTO(TUO([VO(j[O(yiO~O![!eO!r!gO$V!kO(O!dO~P!EkO`,bOa%lO'u%lO~OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!u!hO$V!kO(O!dO(QTO(TUO([VO(j[O(yiO~Oa,hO!rwO#t!OO%i!OO%j!OO%k!OO~P!HTO!i&lO~O&Y,nO~O![,pO~O&k,rO&m,sOP&haQ&haS&haY&haa&had&hae&ham&hao&hap&haq&haw&hay&ha{&ha!P&ha!T&ha!U&ha![&ha!f&ha!i&ha!l&ha!m&ha!n&ha!p&ha!r&ha!u&ha!y&ha#t&ha$V&ha%e&ha%g&ha%i&ha%j&ha%k&ha%n&ha%p&ha%s&ha%t&ha%v&ha&S&ha&Y&ha&[&ha&^&ha&`&ha&c&ha&i&ha&o&ha&q&ha&s&ha&u&ha&w&ha's&ha(O&ha(Q&ha(T&ha([&ha(j&ha(y&ha!Z&ha&a&hab&ha&f&ha~O(O,xO~Oh!bX!Y!OX!Z!OX!d!OX!d!bX!i!bX#]!OX~O!Y!bX!Z!bX~P# ZO!d,}O#],|Oh(eX!Y#eX!Y(eX!Z#eX!Z(eX!d(eX!i(eX~Oh%WO!d-PO!i%cO!Y!^X!Z!^X~Op!nO!P!oO(QTO(TUO(`!mO~OP:kOQ:kOSfOd<fOe!iOmkOo:kOpkOqkOwkOy:kO{:kO!PWO!TkO!UkO![!eO!f:nO!iZO!l:kO!m:kO!n:kO!p:oO!r:rO!u!hO$V!kO(QTO(TUO([VO(j[O(y<dO~O(O;fO~P##_O!Y-TO!Z(dX~O!Z-VO~O!d,}O#],|O!Y#eX!Z#eX~O!Y-WO!Z(sX~O!Z-YO~O!`-ZO!a-ZO(P!lO~P#!|O!Z-^O~P'_Ok-aO![']O~O!V-fO~Op!xa!_!xa!`!xa!a!xa#Q!xa#R!xa#S!xa#T!xa#U!xa#X!xa#Y!xa(P!xa(Q!xa(T!xa(`!xa(j!xa~P!#ZO!m-kO#]-iO~PCSO!`-mO!a-mO(P!lO~PCrOa%lO#]-iO'u%lO~Oa%lO!d#vO#]-iO'u%lO~Oa%lO!d#vO!m-kO#]-iO'u%lO(m'mO~O'z'uO'{'uO'|-rO~Os-sO~O!V'Sa!Y'Sa~P!9mO!X-wO!V'SX!Y'SX~P%[O!Y(RO!V(ca~O!V(ca~PGfO!Y(YO!V(qa~O!P%fO!X-{O![%gO(O%eO!V'YX!Y'YX~O#]-}O!Y(oa!h(oaa(oa'u(oa~O!d#vO~P#+eO!Y(fO!h(na~O!P%fO![%gO#g.RO(O%eO~Om.WO!P%fO!X.TO![%gO!y]O#f.VO#g.TO(O%eO!Y']X!h']X~OR.[O!i#xO~Oh%WOk._O![']O%f.^O~Oa#`i!Y#`i'u#`i's#`i!V#`i!h#`is#`i![#`i%f#`i!d#`i~P!9mOk<pO}){O!P)|O(t%OO(u%QO~O#h#[aa#[a#]#[a'u#[a!Y#[a!h#[a![#[a!V#[a~P#.aO#h(ZXP(ZXR(ZX[(ZXa(ZXo(ZX!i(ZX!m(ZX#O(ZX#k(ZX#l(ZX#m(ZX#n(ZX#o(ZX#p(ZX#q(ZX#r(ZX#s(ZX#u(ZX#w(ZX#y(ZX#z(ZX'u(ZX([(ZX(m(ZX!h(ZX!V(ZX's(ZXs(ZX![(ZX%f(ZX!d(ZX~P!5zO!Y.lO!h(fX~P!9mO!h.oO~O!V.qO~OP$]OR#zO}#yO!P#{O!i#xO!m$]O([VO[#jia#jio#ji!Y#ji#O#ji#l#ji#m#ji#n#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji'u#ji(m#ji(t#ji(u#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#k#ji~P#1|O#k$OO~P#1|OP$]OR#zO}#yO!P#{O!i#xO!m$]O#k$OO#l$PO#m$PO#n$PO([VO[#jia#ji!Y#ji#O#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji'u#ji(m#ji(t#ji(u#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~Oo#ji~P#4kOo$QO~P#4kOP$]OR#zOo$QO}#yO!P#{O!i#xO!m$]O#k$OO#l$PO#m$PO#n$PO#o$RO([VOa#ji!Y#ji#u#ji#w#ji#y#ji#z#ji'u#ji(m#ji(t#ji(u#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O[#ji#O#ji#p#ji#q#ji#r#ji#s#ji~P#7YO[$cO#O$SO#p$SO#q$SO#r$bO#s$SO~P#7YOP$]OR#zO[$cOo$QO}#yO!P#{O!i#xO!m$]O#O$SO#k$OO#l$PO#m$PO#n$PO#o$RO#p$SO#q$SO#r$bO#s$SO#u$TO([VO(u#}Oa#ji!Y#ji#y#ji#z#ji'u#ji(m#ji(t#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#w$VO~P#:WO#w#ji~P#:WOP$]OR#zO[$cOo$QO}#yO!P#{O!i#xO!m$]O#O$SO#k$OO#l$PO#m$PO#n$PO#o$RO#p$SO#q$SO#r$bO#s$SO#u$TO([VOa#ji!Y#ji#y#ji#z#ji'u#ji(m#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#w#ji(t#ji(u#ji~P#<uO#w$VO(t#|O(u#}O~P#<uOP$]OR#zO[$cOo$QO}#yO!P#{O!i#xO!m$]O#O$SO#k$OO#l$PO#m$PO#n$PO#o$RO#p$SO#q$SO#r$bO#s$SO#u$TO#w$VO#y$XO([VO(t#|O(u#}O~Oa#ji!Y#ji#z#ji'u#ji(m#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~P#?jOP]XR]X[]Xo]X}]X!P]X!i]X!m]X#O]X#P]X#]]X#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X([]X(m]X(t]X(u]X!Y]X!Z]X~O#}]X~P#BQOP$]OR#zO[;SOo:vO}#yO!P#{O!i#xO!m$]O#O:xO#k:tO#l:uO#m:uO#n:uO#o:wO#p:xO#q:xO#r;RO#s:xO#u:yO#w:{O#y:}O#z;OO([VO(m$ZO(t#|O(u#}O~O#}.sO~P#D_O#P$dO#];TO$P;TO#}(bX!Z(bX~P! YOa'`a!Y'`a'u'`a's'`a!h'`a!V'`as'`a!['`a%f'`a!d'`a~P!9mOP#jiR#ji[#jia#jio#ji!Y#ji!i#ji!m#ji#O#ji#k#ji#l#ji#m#ji#n#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji'u#ji([#ji(m#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~P#.aO!Y.wOg(kX~P!0pOg.yO~Oa$Oi!Y$Oi'u$Oi's$Oi!V$Oi!h$Ois$Oi![$Oi%f$Oi!d$Oi~P!9mO$[.zO$^.zO~O$[.{O$^.{O~O!d)dO#].|O![$bX$Y$bX$[$bX$^$bX$e$bX~O!X.}O~O![)gO$Y/PO$[)fO$^)fO$e/QO~O!Y;PO!Z(aX~P#D_O!Z/RO~O!d)dO$e(vX~O$e/TO~Ou)vO(])wO(^/WO~O!V/[O~P!&_O(t%OOk%^a}%^a!P%^a(u%^a!Y%^a#]%^a~Og%^a#}%^a~P#L{O(u%QOk%`a}%`a!P%`a(t%`a!Y%`a#]%`a~Og%`a#}%`a~P#MnO!YfX!dfX!hfX!h$xX(mfX~P!/gO!X/eO!Y(YO(O/dO!V(qP!V({P~P!1_Oo*oO!_*mO!`*fO!a*fO!i*^O#X*nO%]*iO(P!lO~Op'XO!P/fO!X+UO!Z*lO(QTO(TUO(`;cO!Z(sP~P$ XO!h/gO~P#.aO!Y/hO!d#vO(m'mO!h(zX~O!h/mO~O!P%fO!X*ZO![%gO(O%eO!h(zP~O#h/oO~O!V$xX!Y$xX!d%PX~P!/gO!Y/pO!V({X~P#.aO!d/rO~O!V/tO~Oh%WOo/xO!d#vO!i%cO(m'mO~O(O/zO~O!d+cO~Oa%lO!Y0OO'u%lO~O!Z0QO~P!5RO!`0RO!a0RO(P!lO(`!mO~O!P0TO(`!mO~O#X0UO~Og%^a!Y%^a#]%^a#}%^a~P!0pOg%`a!Y%`a#]%`a#}%`a~P!0pO(O&WOg'iX!Y'iX~O!Y*uOg(Xa~Og0_O~OR0`O}0`O!P0aO#P$dOkza(tza(uza!Yza#]za~Ogza#}za~P$&zO}){O!P)|Ok$qa(t$qa(u$qa!Y$qa#]$qa~Og$qa#}$qa~P$'sO}){O!P)|Ok$sa(t$sa(u$sa!Y$sa#]$sa~Og$sa#}$sa~P$(fO#h0dO~Og%Ra!Y%Ra#]%Ra#}%Ra~P!0pO!d#vO~O#h0gO~O!Y+WOa)Pa'u)Pa~OR#zO}#yO!P#{O!i#xO([VOP!oi[!oio!oi!Y!oi!m!oi#O!oi#k!oi#l!oi#m!oi#n!oi#o!oi#p!oi#q!oi#r!oi#s!oi#u!oi#w!oi#y!oi#z!oi(m!oi(t!oi(u!oi~Oa!oi'u!oi's!oi!V!oi!h!ois!oi![!oi%f!oi!d!oi~P$*TOh%WOo$uOp$tOq$tOw%YOy%ZO{;YO!P$|O![$}O!f<jO!i$yO#g;`O$V%_O$r;[O$t;^O$w%`O(QTO(TUO([$vO(t%OO(u%QO~Om0pO(O0oO~P$,kO!d+cOa(Wa![(Wa'u(Wa!Y(Wa~O#h0vO~O[]X!YfX!ZfX~O!Y0wO!Z)TX~O!Z0yO~O[0zO~Ob0|O(O+kO(QTO(TUO~O![%}O(O%eO`'qX!Y'qX~O!Y+pO`)Sa~O!h1PO~P!9mO[1SO~O`1TO~O#]1WO~Ok1ZO![$}O~O(`(xO!Z)QP~Oh%WOk1dO![1aO%f1cO~O[1nO!Y1lO!Z)RX~O!Z1oO~O`1qOa%lO'u%lO~O(O#nO(QTO(TUO~O#P$dO#]$eO$P$eOP(bXR(bX[(bXo(bX}(bX!P(bX!Y(bX!i(bX!m(bX#O(bX#k(bX#l(bX#m(bX#n(bX#o(bX#p(bX#q(bX#r(bX#u(bX#w(bX#y(bX#z(bX([(bX(m(bX(t(bX(u(bX~O#s1tO&W1uOa(bX~P$2RO#]$eO#s1tO&W1uO~Oa1wO~P%[Oa1yO~O&a1|OP&_iQ&_iS&_iY&_ia&_id&_ie&_im&_io&_ip&_iq&_iw&_iy&_i{&_i!P&_i!T&_i!U&_i![&_i!f&_i!i&_i!l&_i!m&_i!n&_i!p&_i!r&_i!u&_i!y&_i#t&_i$V&_i%e&_i%g&_i%i&_i%j&_i%k&_i%n&_i%p&_i%s&_i%t&_i%v&_i&S&_i&Y&_i&[&_i&^&_i&`&_i&c&_i&i&_i&o&_i&q&_i&s&_i&u&_i&w&_i's&_i(O&_i(Q&_i(T&_i([&_i(j&_i(y&_i!Z&_ib&_i&f&_i~Ob2SO!Z2QO&f2RO~P`O![XO!i2UO~O&m,sOP&hiQ&hiS&hiY&hia&hid&hie&him&hio&hip&hiq&hiw&hiy&hi{&hi!P&hi!T&hi!U&hi![&hi!f&hi!i&hi!l&hi!m&hi!n&hi!p&hi!r&hi!u&hi!y&hi#t&hi$V&hi%e&hi%g&hi%i&hi%j&hi%k&hi%n&hi%p&hi%s&hi%t&hi%v&hi&S&hi&Y&hi&[&hi&^&hi&`&hi&c&hi&i&hi&o&hi&q&hi&s&hi&u&hi&w&hi's&hi(O&hi(Q&hi(T&hi([&hi(j&hi(y&hi!Z&hi&a&hib&hi&f&hi~O!V2[O~O!Y!^a!Z!^a~P#D_Op!nO!P!oO!X2bO(`!mO!Y'TX!Z'TX~P@YO!Y-TO!Z(da~O!Y'ZX!Z'ZX~P!8uO!Y-WO!Z(sa~O!Z2iO~P'_Oa%lO#]2rO'u%lO~Oa%lO!d#vO#]2rO'u%lO~Oa%lO!d#vO!m2vO#]2rO'u%lO(m'mO~Oa%lO'u%lO~P!9mO!Y$`Os$oa~O!V'Si!Y'Si~P!9mO!Y(RO!V(ci~O!Y(YO!V(qi~O!V(ri!Y(ri~P!9mO!Y(oi!h(oia(oi'u(oi~P!9mO#]2xO!Y(oi!h(oia(oi'u(oi~O!Y(fO!h(ni~O!P%fO![%gO!y]O#f2}O#g2|O(O%eO~O!P%fO![%gO#g2|O(O%eO~Ok3UO![']O%f3TO~Oh%WOk3UO![']O%f3TO~O#h%^aP%^aR%^a[%^aa%^ao%^a!i%^a!m%^a#O%^a#k%^a#l%^a#m%^a#n%^a#o%^a#p%^a#q%^a#r%^a#s%^a#u%^a#w%^a#y%^a#z%^a'u%^a([%^a(m%^a!h%^a!V%^a's%^as%^a![%^a%f%^a!d%^a~P#L{O#h%`aP%`aR%`a[%`aa%`ao%`a!i%`a!m%`a#O%`a#k%`a#l%`a#m%`a#n%`a#o%`a#p%`a#q%`a#r%`a#s%`a#u%`a#w%`a#y%`a#z%`a'u%`a([%`a(m%`a!h%`a!V%`a's%`as%`a![%`a%f%`a!d%`a~P#MnO#h%^aP%^aR%^a[%^aa%^ao%^a!Y%^a!i%^a!m%^a#O%^a#k%^a#l%^a#m%^a#n%^a#o%^a#p%^a#q%^a#r%^a#s%^a#u%^a#w%^a#y%^a#z%^a'u%^a([%^a(m%^a!h%^a!V%^a's%^a#]%^as%^a![%^a%f%^a!d%^a~P#.aO#h%`aP%`aR%`a[%`aa%`ao%`a!Y%`a!i%`a!m%`a#O%`a#k%`a#l%`a#m%`a#n%`a#o%`a#p%`a#q%`a#r%`a#s%`a#u%`a#w%`a#y%`a#z%`a'u%`a([%`a(m%`a!h%`a!V%`a's%`a#]%`as%`a![%`a%f%`a!d%`a~P#.aO#hzaPza[zaazaoza!iza!mza#Oza#kza#lza#mza#nza#oza#pza#qza#rza#sza#uza#wza#yza#zza'uza([za(mza!hza!Vza'szasza![za%fza!dza~P$&zO#h$qaP$qaR$qa[$qaa$qao$qa!i$qa!m$qa#O$qa#k$qa#l$qa#m$qa#n$qa#o$qa#p$qa#q$qa#r$qa#s$qa#u$qa#w$qa#y$qa#z$qa'u$qa([$qa(m$qa!h$qa!V$qa's$qas$qa![$qa%f$qa!d$qa~P$'sO#h$saP$saR$sa[$saa$sao$sa!i$sa!m$sa#O$sa#k$sa#l$sa#m$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#u$sa#w$sa#y$sa#z$sa'u$sa([$sa(m$sa!h$sa!V$sa's$sas$sa![$sa%f$sa!d$sa~P$(fO#h%RaP%RaR%Ra[%Raa%Rao%Ra!Y%Ra!i%Ra!m%Ra#O%Ra#k%Ra#l%Ra#m%Ra#n%Ra#o%Ra#p%Ra#q%Ra#r%Ra#s%Ra#u%Ra#w%Ra#y%Ra#z%Ra'u%Ra([%Ra(m%Ra!h%Ra!V%Ra's%Ra#]%Ras%Ra![%Ra%f%Ra!d%Ra~P#.aOa#`q!Y#`q'u#`q's#`q!V#`q!h#`qs#`q![#`q%f#`q!d#`q~P!9mO!X3^O!Y'UX!h'UX~P%[O!Y.lO!h(fa~O!Y.lO!h(fa~P!9mO!V3aO~O#}!ka!Z!ka~PKPO#}!ga!Y!ga!Z!ga~P#D_O#}!oa!Z!oa~P!<TO#}!qa!Z!qa~P!>kOg'XX!Y'XX~P!+hO!Y.wOg(ka~OSfO![3uO$c3vO~O!Z3zO~Os3{O~P#.aOa$lq!Y$lq'u$lq's$lq!V$lq!h$lqs$lq![$lq%f$lq!d$lq~P!9mO!V3|O~P#.aO}){O!P)|O(u%QOk'ea(t'ea!Y'ea#]'ea~Og'ea#}'ea~P%+]O}){O!P)|Ok'ga(t'ga(u'ga!Y'ga#]'ga~Og'ga#}'ga~P%,OO(m$ZO~P#.aO!VfX!V$xX!YfX!Y$xX!d%PX#]fX~P!/gO(O;lO~P!1_OmkO(O4OO~P.iO!P%fO!X4QO![%gO(O%eO!Y'aX!h'aX~O!Y/hO!h(za~O!Y/hO!d#vO!h(za~O!Y/hO!d#vO(m'mO!h(za~Og$zi!Y$zi#]$zi#}$zi~P!0pO!X4YO!V'cX!Y'cX~P!3^O!Y/pO!V({a~O!Y/pO!V({a~P#.aO!d#vO#s4bO~Oo4eO!d#vO(m'mO~O(t%OOk%^i}%^i!P%^i(u%^i!Y%^i#]%^i~Og%^i#}%^i~P%0^O(u%QOk%`i}%`i!P%`i(t%`i!Y%`i#]%`i~Og%`i#}%`i~P%1POg(Yi!Y(Yi~P!0pO#]4lOg(Yi!Y(Yi~P!0pO!h4oO~Oa$mq!Y$mq'u$mq's$mq!V$mq!h$mqs$mq![$mq%f$mq!d$mq~P!9mO!V4sO~O!Y4tO![(|X~P#.aOa$xX![$xX%Z]X'u$xX!Y$xX~P!/gO%Z4wOalXklX}lX!PlX![lX'ulX(tlX(ulX!YlX~O%Z4wO~Ob4}O%g5OO(O+kO(QTO(TUO!Y'pX!Z'pX~O!Y0wO!Z)Ta~O[5SO~O`5TO~Oa%lO'u%lO~P#.aO![$}O~P#.aO!Y5]O#]5_O!Z)QX~O!Z5`O~Op!nO!P5aO!_!yO!`!vO!a!vO!y:lO#Q!pO#R!pO#S!pO#T!pO#U!pO#X5fO#Y!zO(P!lO(QTO(TUO(`!mO(j!sO~O!Z5eO~P%6ZOk5kO![1aO%f5jO~Oh%WOk5kO![1aO%f5jO~Ob5rO(O#nO(QTO(TUO!Y'oX!Z'oX~O!Y1lO!Z)Ra~O(QTO(TUO(`5tO~O`5xO~O#s5{O&W5|O~PMlO!h5}O~P%[Oa6PO~Oa6PO~P%[Ob2SO!Z6UO&f2RO~P`O!d6WO~O!d6YOh(ei!Y(ei!Z(ei!d(ei!i(ei~O!Y#ei!Z#ei~P#D_O#]6ZO!Y#ei!Z#ei~O!Y!^i!Z!^i~P#D_Oa%lO#]6dO'u%lO~Oa%lO!d#vO#]6dO'u%lO~O!Y(oq!h(oqa(oq'u(oq~P!9mO!Y(fO!h(nq~O!P%fO![%gO#g6kO(O%eO~O![']O%f6nO~Ok6rO![']O%f6nO~O#h'eaP'eaR'ea['eaa'eao'ea!i'ea!m'ea#O'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#s'ea#u'ea#w'ea#y'ea#z'ea'u'ea(['ea(m'ea!h'ea!V'ea's'eas'ea!['ea%f'ea!d'ea~P%+]O#h'gaP'gaR'ga['gaa'gao'ga!i'ga!m'ga#O'ga#k'ga#l'ga#m'ga#n'ga#o'ga#p'ga#q'ga#r'ga#s'ga#u'ga#w'ga#y'ga#z'ga'u'ga(['ga(m'ga!h'ga!V'ga's'gas'ga!['ga%f'ga!d'ga~P%,OO#h$ziP$ziR$zi[$zia$zio$zi!Y$zi!i$zi!m$zi#O$zi#k$zi#l$zi#m$zi#n$zi#o$zi#p$zi#q$zi#r$zi#s$zi#u$zi#w$zi#y$zi#z$zi'u$zi([$zi(m$zi!h$zi!V$zi's$zi#]$zis$zi![$zi%f$zi!d$zi~P#.aO#h%^iP%^iR%^i[%^ia%^io%^i!i%^i!m%^i#O%^i#k%^i#l%^i#m%^i#n%^i#o%^i#p%^i#q%^i#r%^i#s%^i#u%^i#w%^i#y%^i#z%^i'u%^i([%^i(m%^i!h%^i!V%^i's%^is%^i![%^i%f%^i!d%^i~P%0^O#h%`iP%`iR%`i[%`ia%`io%`i!i%`i!m%`i#O%`i#k%`i#l%`i#m%`i#n%`i#o%`i#p%`i#q%`i#r%`i#s%`i#u%`i#w%`i#y%`i#z%`i'u%`i([%`i(m%`i!h%`i!V%`i's%`is%`i![%`i%f%`i!d%`i~P%1PO!Y'Ua!h'Ua~P!9mO!Y.lO!h(fi~O#}#`i!Y#`i!Z#`i~P#D_OP$]OR#zO}#yO!P#{O!i#xO!m$]O([VO[#jio#ji#O#ji#l#ji#m#ji#n#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji#}#ji(m#ji(t#ji(u#ji!Y#ji!Z#ji~O#k#ji~P%HpO#k:tO~P%HpOP$]OR#zO}#yO!P#{O!i#xO!m$]O#k:tO#l:uO#m:uO#n:uO([VO[#ji#O#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji#}#ji(m#ji(t#ji(u#ji!Y#ji!Z#ji~Oo#ji~P%JxOo:vO~P%JxOP$]OR#zOo:vO}#yO!P#{O!i#xO!m$]O#k:tO#l:uO#m:uO#n:uO#o:wO([VO#u#ji#w#ji#y#ji#z#ji#}#ji(m#ji(t#ji(u#ji!Y#ji!Z#ji~O[#ji#O#ji#p#ji#q#ji#r#ji#s#ji~P%MQO[;SO#O:xO#p:xO#q:xO#r;RO#s:xO~P%MQOP$]OR#zO[;SOo:vO}#yO!P#{O!i#xO!m$]O#O:xO#k:tO#l:uO#m:uO#n:uO#o:wO#p:xO#q:xO#r;RO#s:xO#u:yO([VO(u#}O#y#ji#z#ji#}#ji(m#ji(t#ji!Y#ji!Z#ji~O#w:{O~P& iO#w#ji~P& iOP$]OR#zO[;SOo:vO}#yO!P#{O!i#xO!m$]O#O:xO#k:tO#l:uO#m:uO#n:uO#o:wO#p:xO#q:xO#r;RO#s:xO#u:yO([VO#y#ji#z#ji#}#ji(m#ji!Y#ji!Z#ji~O#w#ji(t#ji(u#ji~P&#qO#w:{O(t#|O(u#}O~P&#qOP$]OR#zO[;SOo:vO}#yO!P#{O!i#xO!m$]O#O:xO#k:tO#l:uO#m:uO#n:uO#o:wO#p:xO#q:xO#r;RO#s:xO#u:yO#w:{O#y:}O([VO(t#|O(u#}O~O#z#ji#}#ji(m#ji!Y#ji!Z#ji~P&&POa#{y!Y#{y'u#{y's#{y!V#{y!h#{ys#{y![#{y%f#{y!d#{y~P!9mOk<qO}){O!P)|O(t%OO(u%QO~OP#jiR#ji[#jio#ji!i#ji!m#ji#O#ji#k#ji#l#ji#m#ji#n#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji#}#ji([#ji(m#ji!Y#ji!Z#ji~P&(tO#P$dOP(ZXR(ZX[(ZXk(ZXo(ZX}(ZX!P(ZX!i(ZX!m(ZX#O(ZX#k(ZX#l(ZX#m(ZX#n(ZX#o(ZX#p(ZX#q(ZX#r(ZX#s(ZX#u(ZX#w(ZX#y(ZX#z(ZX#}(ZX([(ZX(m(ZX(t(ZX(u(ZX!Y(ZX!Z(ZX~O#}$Oi!Y$Oi!Z$Oi~P#D_O#}!oi!Z!oi~P$*TOg'Xa!Y'Xa~P!0pO!Z7UO~O!Y'`a!Z'`a~P#D_OP]XR]X[]Xo]X}]X!P]X!V]X!Y]X!i]X!m]X#O]X#P]X#]]X#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X([]X(m]X(t]X(u]X~O!d%WX#s%WX~P&-}O!d#vO(m'mO!Y'aa!h'aa~O!Y/hO!h(zi~O!Y/hO!d#vO!h(zi~Og$zq!Y$zq#]$zq#}$zq~P!0pO!V'ca!Y'ca~P#.aO!d7]O~O!Y/pO!V({i~P#.aO!Y/pO!V({i~O!V7aO~O!d#vO#s7fO~Oo7gO!d#vO(m'mO~O}){O!P)|O(u%QOk'fa(t'fa!Y'fa#]'fa~Og'fa#}'fa~P&2mO}){O!P)|Ok'ha(t'ha(u'ha!Y'ha#]'ha~Og'ha#}'ha~P&3`O!V7iO~Og$|q!Y$|q#]$|q#}$|q~P!0pOa$my!Y$my'u$my's$my!V$my!h$mys$my![$my%f$my!d$my~P!9mO!d6YO~O!Y4tO![(|a~O![']OP$SaR$Sa[$Sao$Sa}$Sa!P$Sa!Y$Sa!i$Sa!m$Sa#O$Sa#k$Sa#l$Sa#m$Sa#n$Sa#o$Sa#p$Sa#q$Sa#r$Sa#s$Sa#u$Sa#w$Sa#y$Sa#z$Sa([$Sa(m$Sa(t$Sa(u$Sa~O%f6nO~P&5iOa#`y!Y#`y'u#`y's#`y!V#`y!h#`ys#`y![#`y%f#`y!d#`y~P!9mO[7nO~Ob7pO(O+kO(QTO(TUO~O!Y0wO!Z)Ti~O`7tO~O(`(xO!Y'lX!Z'lX~O!Y5]O!Z)Qa~O!Z7}O~P%6ZOp!nO!P8OO(QTO(TUO(`!mO(j!sO~O![1aO~O![1aO%f8QO~Ok8TO![1aO%f8QO~O[8YO!Y'oa!Z'oa~O!Y1lO!Z)Ri~O!h8^O~O!h8_O~O!h8bO~O!h8bO~P%[Oa8dO~O!d8eO~O!h8fO~O!Y(ri!Z(ri~P#D_Oa%lO#]8nO'u%lO~O!Y(oy!h(oya(oy'u(oy~P!9mO!Y(fO!h(ny~O%f8qO~P&5iO![']O%f8qO~O#h$zqP$zqR$zq[$zqa$zqo$zq!Y$zq!i$zq!m$zq#O$zq#k$zq#l$zq#m$zq#n$zq#o$zq#p$zq#q$zq#r$zq#s$zq#u$zq#w$zq#y$zq#z$zq'u$zq([$zq(m$zq!h$zq!V$zq's$zq#]$zqs$zq![$zq%f$zq!d$zq~P#.aO#h'faP'faR'fa['faa'fao'fa!i'fa!m'fa#O'fa#k'fa#l'fa#m'fa#n'fa#o'fa#p'fa#q'fa#r'fa#s'fa#u'fa#w'fa#y'fa#z'fa'u'fa(['fa(m'fa!h'fa!V'fa's'fas'fa!['fa%f'fa!d'fa~P&2mO#h'haP'haR'ha['haa'hao'ha!i'ha!m'ha#O'ha#k'ha#l'ha#m'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#u'ha#w'ha#y'ha#z'ha'u'ha(['ha(m'ha!h'ha!V'ha's'has'ha!['ha%f'ha!d'ha~P&3`O#h$|qP$|qR$|q[$|qa$|qo$|q!Y$|q!i$|q!m$|q#O$|q#k$|q#l$|q#m$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#u$|q#w$|q#y$|q#z$|q'u$|q([$|q(m$|q!h$|q!V$|q's$|q#]$|qs$|q![$|q%f$|q!d$|q~P#.aO!Y'Ui!h'Ui~P!9mO#}#`q!Y#`q!Z#`q~P#D_O(t%OOP%^aR%^a[%^ao%^a!i%^a!m%^a#O%^a#k%^a#l%^a#m%^a#n%^a#o%^a#p%^a#q%^a#r%^a#s%^a#u%^a#w%^a#y%^a#z%^a#}%^a([%^a(m%^a!Y%^a!Z%^a~Ok%^a}%^a!P%^a(u%^a~P&FgO(u%QOP%`aR%`a[%`ao%`a!i%`a!m%`a#O%`a#k%`a#l%`a#m%`a#n%`a#o%`a#p%`a#q%`a#r%`a#s%`a#u%`a#w%`a#y%`a#z%`a#}%`a([%`a(m%`a!Y%`a!Z%`a~Ok%`a}%`a!P%`a(t%`a~P&HkOk<qO}){O!P)|O(u%QO~P&FgOk<qO}){O!P)|O(t%OO~P&HkOR0`O}0`O!P0aO#P$dOPza[zakzaoza!iza!mza#Oza#kza#lza#mza#nza#oza#pza#qza#rza#sza#uza#wza#yza#zza#}za([za(mza(tza(uza!Yza!Zza~O}){O!P)|OP$qaR$qa[$qak$qao$qa!i$qa!m$qa#O$qa#k$qa#l$qa#m$qa#n$qa#o$qa#p$qa#q$qa#r$qa#s$qa#u$qa#w$qa#y$qa#z$qa#}$qa([$qa(m$qa(t$qa(u$qa!Y$qa!Z$qa~O}){O!P)|OP$saR$sa[$sak$sao$sa!i$sa!m$sa#O$sa#k$sa#l$sa#m$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#u$sa#w$sa#y$sa#z$sa#}$sa([$sa(m$sa(t$sa(u$sa!Y$sa!Z$sa~OP%RaR%Ra[%Rao%Ra!i%Ra!m%Ra#O%Ra#k%Ra#l%Ra#m%Ra#n%Ra#o%Ra#p%Ra#q%Ra#r%Ra#s%Ra#u%Ra#w%Ra#y%Ra#z%Ra#}%Ra([%Ra(m%Ra!Y%Ra!Z%Ra~P&(tO#}$lq!Y$lq!Z$lq~P#D_O#}$mq!Y$mq!Z$mq~P#D_O!Z9OO~O#}9PO~P!0pO!d#vO!Y'ai!h'ai~O!d#vO(m'mO!Y'ai!h'ai~O!Y/hO!h(zq~O!V'ci!Y'ci~P#.aO!Y/pO!V({q~O!V9VO~P#.aO!V9VO~Og(Yy!Y(Yy~P!0pO!Y'ja!['ja~P#.aOa%Yq![%Yq'u%Yq!Y%Yq~P#.aO[9[O~O!Y0wO!Z)Tq~O#]9`O!Y'la!Z'la~O!Y5]O!Z)Qi~P#D_O![1aO%f9dO~O(QTO(TUO(`9iO~O!Y1lO!Z)Rq~O!h9lO~O!h9mO~O!h9nO~O!h9nO~P%[O#]9qO!Y#ey!Z#ey~O!Y#ey!Z#ey~P#D_O%f9vO~P&5iO![']O%f9vO~O#}#{y!Y#{y!Z#{y~P#D_OP$ziR$zi[$zio$zi!i$zi!m$zi#O$zi#k$zi#l$zi#m$zi#n$zi#o$zi#p$zi#q$zi#r$zi#s$zi#u$zi#w$zi#y$zi#z$zi#}$zi([$zi(m$zi!Y$zi!Z$zi~P&(tO}){O!P)|O(u%QOP'eaR'ea['eak'eao'ea!i'ea!m'ea#O'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#s'ea#u'ea#w'ea#y'ea#z'ea#}'ea(['ea(m'ea(t'ea!Y'ea!Z'ea~O}){O!P)|OP'gaR'ga['gak'gao'ga!i'ga!m'ga#O'ga#k'ga#l'ga#m'ga#n'ga#o'ga#p'ga#q'ga#r'ga#s'ga#u'ga#w'ga#y'ga#z'ga#}'ga(['ga(m'ga(t'ga(u'ga!Y'ga!Z'ga~O(t%OOP%^iR%^i[%^ik%^io%^i}%^i!P%^i!i%^i!m%^i#O%^i#k%^i#l%^i#m%^i#n%^i#o%^i#p%^i#q%^i#r%^i#s%^i#u%^i#w%^i#y%^i#z%^i#}%^i([%^i(m%^i(u%^i!Y%^i!Z%^i~O(u%QOP%`iR%`i[%`ik%`io%`i}%`i!P%`i!i%`i!m%`i#O%`i#k%`i#l%`i#m%`i#n%`i#o%`i#p%`i#q%`i#r%`i#s%`i#u%`i#w%`i#y%`i#z%`i#}%`i([%`i(m%`i(t%`i!Y%`i!Z%`i~O#}$my!Y$my!Z$my~P#D_O#}#`y!Y#`y!Z#`y~P#D_O!d#vO!Y'aq!h'aq~O!Y/hO!h(zy~O!V'cq!Y'cq~P#.aO!V:PO~P#.aO!Y0wO!Z)Ty~O!Y5]O!Z)Qq~O![1aO%f:WO~O!h:ZO~O%f:`O~P&5iOP$zqR$zq[$zqo$zq!i$zq!m$zq#O$zq#k$zq#l$zq#m$zq#n$zq#o$zq#p$zq#q$zq#r$zq#s$zq#u$zq#w$zq#y$zq#z$zq#}$zq([$zq(m$zq!Y$zq!Z$zq~P&(tO}){O!P)|O(u%QOP'faR'fa['fak'fao'fa!i'fa!m'fa#O'fa#k'fa#l'fa#m'fa#n'fa#o'fa#p'fa#q'fa#r'fa#s'fa#u'fa#w'fa#y'fa#z'fa#}'fa(['fa(m'fa(t'fa!Y'fa!Z'fa~O}){O!P)|OP'haR'ha['hak'hao'ha!i'ha!m'ha#O'ha#k'ha#l'ha#m'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#u'ha#w'ha#y'ha#z'ha#}'ha(['ha(m'ha(t'ha(u'ha!Y'ha!Z'ha~OP$|qR$|q[$|qo$|q!i$|q!m$|q#O$|q#k$|q#l$|q#m$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#u$|q#w$|q#y$|q#z$|q#}$|q([$|q(m$|q!Y$|q!Z$|q~P&(tOg%b!Z!Y%b!Z#]%b!Z#}%b!Z~P!0pO!Y'lq!Z'lq~P#D_O!Y#e!Z!Z#e!Z~P#D_O#h%b!ZP%b!ZR%b!Z[%b!Za%b!Zo%b!Z!Y%b!Z!i%b!Z!m%b!Z#O%b!Z#k%b!Z#l%b!Z#m%b!Z#n%b!Z#o%b!Z#p%b!Z#q%b!Z#r%b!Z#s%b!Z#u%b!Z#w%b!Z#y%b!Z#z%b!Z'u%b!Z([%b!Z(m%b!Z!h%b!Z!V%b!Z's%b!Z#]%b!Zs%b!Z![%b!Z%f%b!Z!d%b!Z~P#.aOP%b!ZR%b!Z[%b!Zo%b!Z!i%b!Z!m%b!Z#O%b!Z#k%b!Z#l%b!Z#m%b!Z#n%b!Z#o%b!Z#p%b!Z#q%b!Z#r%b!Z#s%b!Z#u%b!Z#w%b!Z#y%b!Z#z%b!Z#}%b!Z([%b!Z(m%b!Z!Y%b!Z!Z%b!Z~P&(tOs(aX~P1qO}%pO~P!(oO(P!lO~P!(oO!VfX!YfX#]fX~P&-}OP]XR]X[]Xo]X}]X!P]X!Y]X!YfX!i]X!m]X#O]X#P]X#]]X#]fX#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X([]X(m]X(t]X(u]X~O!dfX!h]X!hfX(mfX~P'CvOP:kOQ:kOSfOd<fOe!iOmkOo:kOpkOqkOwkOy:kO{:kO!PWO!TkO!UkO![XO!f:nO!iZO!l:kO!m:kO!n:kO!p:oO!r:rO!u!hO$V!kO(O)YO(QTO(TUO([VO(j[O(y<dO~O!Y;PO!Z$oa~Oh%WOm%XOo$uOp$tOq$tOw%YOy%ZO{;ZO!P$|O![$}O!f<kO!i$yO#g;aO$V%_O$r;]O$t;_O$w%`O(O(rO(QTO(TUO([$vO(t%OO(u%QO~O#t)`O~P'HlOo!bX(m!bX~P# ZO!Z]X!ZfX~P'CvO!VfX!V$xX!YfX!Y$xX#]fX~P!/gO#h:sO~O!d#vO#h:sO~O#];TO~O#s:xO~O#];dO!Y(rX!Z(rX~O#];TO!Y(pX!Z(pX~O#h;eO~Og;gO~P!0pO#h;mO~O#h;nO~O!d#vO#h;oO~O!d#vO#h;eO~O#};pO~P#D_O#h;qO~O#h;rO~O#h;wO~O#h;xO~O#h;yO~O#h;zO~O#};{O~P!0pO#};|O~P!0pO#P#Q#R#T#U#X#f#g#r(y$r$t$w%Z%e%f%g%n%p%s%t%v%x~'yT#l!U'w(P#mp#k#no}'x$['x(O$^(`~",goto:"$2y)XPPPPPP)YPP)]P)nP+O/PPPPP5wPP6_PP<U?kP@OP@OPPP@OPBOP@OP@OP@OPBSPBXPBvPGoPPPGsPPPPGsJuPPPJ{KwPGsPGsPPNVGsPPPGsPGsP!!^GsP!%s!&x!'RP!'u!'y!'u!+VPPPPPPP!+v!&xPP!,W!-QP!/tGsGs!/y!3U!7l!7l!;jPPP!;rGsPPPPPPPPPPP!?QP!@_PPGs!ApPGsPGsGsGsGsGsPGs!CSP!F]P!IbP!If!Ip!It!ItP!FYP!Ix!IxP!L}P!MRGsGs!MX#!]BS@OP@OP@O@OP##i@O@O#%s@O#(c@O#*g@O@O#+V#-c#-c#-h#-q#-c#-zP#-cP@O#.d@O#2T@O@O5wPPP#5zPPP#6e#6eP#6eP#6{#6ePP#7RP#6xP#6x#7f#6x#8Q#8W5t)]#8Z)]P#8b#8b#8bP)]P)]P)]P)]PP)]P#8h#8kP#8k)]P#8oP#8rP)]P)]P)]P)]P)]P)])]PP#8x#9O#9Z#9a#9g#9m#9s#:R#:X#:c#:i#:s#:y#;Z#;a#<R#<e#<k#<q#=P#=f#?T#?c#?j#AO#A^#Bx#CW#C^#Cd#Cj#Ct#Cz#DQ#D[#Dn#DtPPPPPPPPPP#DzPPPPPPP#Eo#Hv#JV#J^#JfPPP$ lP$ u$$m$+V$+Y$+]$+x$+{$,S$,[P$,b$,eP$-R$-V$-}$/]$/b$/xPP$/}$0T$0XP$0[$0`$0d$1Y$1q$2Y$2^$2a$2d$2j$2m$2q$2uR!|RoqOXst!Z#d%k&o&q&r&t,k,p1|2PY!vQ']-]1a5dQ%rvQ%zyQ&R|Q&g!VS'T!e-TQ'c!iS'i!r!yU*f$}*W*kQ+i%{Q+v&TQ,[&aQ-Z'[Q-e'dQ-m'jQ0R*mQ1k,]R;b:o%QdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$`$e%k%r&P&h&k&o&q&r&t&x'Q'_'o(P(R(X(`(t(v(z)z+R+V,h,k,p-a-i-w-}.l.s/f0a0g0v1d1t1u1w1y1|2P2R2r2x3^5a5k5{5|6P6d8O8T8d8nS#q]:l!r)[$[$n'U)o,|-P.}2b3u5_6Z9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gU*y%[;Y;ZQ+n%}Q,^&dQ,e&lQ0m+aQ0q+cQ0|+oQ1s,cQ3Q._Q4}0wQ5r1lQ6p3UQ7p5OR8t6r'OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(P(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gt!nQ!r!v!y!z'T'[']'i'j'k-T-Z-]-m1a5d5f$z$ti#v$b$c$d$y$|%P%R%]%^%b)v)|*O*Q*S*V*]*c*s*t+`+c+z+}.^.w/]/e/o/p/r0V0X0d1W1Z1c3T3}4Y4b4l4t4w5j6n7]7f8Q8q9P9d9v:W:`;R;S;U;V;W;X;[;];^;_;`;a;h;i;j;k;m;n;q;r;s;t;u;v;w;x;{;|<d<l<m<p<qQ&U|Q'R!eU'X%g*W-WQ+n%}Q,^&dQ0c*|Q0|+oQ1R+uQ1r,bQ1s,cQ4}0wQ5W1TQ5r1lQ5u1nQ5v1qQ7p5OQ7s5TQ8]5xQ9_7tR9j8YrnOXst!V!Z#d%k&f&o&q&r&t,k,p1|2PR,`&h&x^OPXYstuvwz!Z!`!g!j!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<f<g[#]WZ#W#Z'U(P!b%hm#h#i#l$y%c%f(Y(d(e(f*V*Z*^+T+U+W,g,}-{.R.S.T.V/e/h2U2|2}4Q6Y6kQ%uxQ%yyS&O|&TQ&[!TQ'`!hQ'b!iQ(m#sS+h%z%{Q+l%}Q,V&_Q,Z&aS-d'c'dQ.a(nQ0u+iQ0{+oQ0}+pQ1Q+tQ1f,WS1j,[,]Q2n-eQ4|0wQ5Q0zQ5V1SQ5q1kQ7o5OQ7r5SQ9Z7nR:R9[!O${i$d%P%R%]%^%b*O*Q*]*s*t.w/o0V0X0d3}4l9P<d<l<m!S%wy!i!u%y%z%{'S'b'c'd'h'r*e+h+i-Q-d-e-l/y0u2g2n2u4dQ+b%uQ+{&XQ,O&YQ,Y&aQ.`(mQ1e,VU1i,Z,[,]Q3V.aQ5l1fS5p1j1kQ8X5q#[<h#v$b$c$y$|)v)|*S*V*c+`+c+z+}.^/]/e/p/r1W1Z1c3T4Y4b4t4w5j6n7]7f8Q8q9d9v:W:`;U;W;[;^;`;h;j;m;q;s;u;w;{<p<qo<i;R;S;V;X;];_;a;i;k;n;r;t;v;x;|W%Ui%W*u<dS&X!Q&fQ&Y!RQ&Z!SR+y&V${%Ti#v$b$c$d$y$|%P%R%]%^%b)v)|*O*Q*S*V*]*c*s*t+`+c+z+}.^.w/]/e/o/p/r0V0X0d1W1Z1c3T3}4Y4b4l4t4w5j6n7]7f8Q8q9P9d9v:W:`;R;S;U;V;W;X;[;];^;_;`;a;h;i;j;k;m;n;q;r;s;t;u;v;w;x;{;|<d<l<m<p<qT)w$v)xV*y%[;Y;ZW'X!e%g*W-WS(y#y#zQ+]%pQ+s&QS.Y(i(jQ1[,PQ4m0`R7x5]'OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(P(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<g$i$_c#Y#e%o%q%s(O(U(p(u(})O)P)Q)R)S)T)U)V)W)X)Z)])c)m+^+r-R-p-u-z-|.k.n.r.t.u.v/X0e2]2`2p2w3]3b3c3d3e3f3g3h3i3j3k3l3m3n3q3r3y4q4z6]6c6h6w6x7R7S7z8h8l8v8|8}9s:T:[:m<ZT#TV#U'PkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(P(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gQ'V!eR2c-Tv!nQ!e!r!v!y!z'T'[']'i'j'k-T-Z-]-m1a5d5fU*e$}*W*kS/y*f*mQ0S*nQ1^,RQ4d0RR4g0UnqOXst!Z#d%k&o&q&r&t,k,p1|2PQ&v!^Q's!xS(o#u:sQ+f%xQ,T&[Q,U&^Q-b'aQ-o'lS.j(t;eS0f+R;oQ0s+gQ1`,SQ2T,rQ2V,sQ2_-OQ2l-cQ2o-gS4r0g;yQ4x0tS4{0v;zQ6[2aQ6`2mQ6e2tQ7m4yQ8i6^Q8j6aQ8m6fR9p8f$d$^c#Y#e%q%s(O(U(p(u(})O)P)Q)R)S)T)U)V)W)X)Z)])c)m+^+r-R-p-u-z-|.k.n.r.u.v/X0e2]2`2p2w3]3b3c3d3e3f3g3h3i3j3k3l3m3n3q3r3y4q4z6]6c6h6w6x7R7S7z8h8l8v8|8}9s:T:[:m<ZS(k#p'fQ({#zS+[%o.tS.Z(j(lR3O.['OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(P(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gS#q]:lQ&q!XQ&r!YQ&t![Q&u!]R1{,nQ'^!hQ+_%uQ-`'`S.](m+bQ2j-_W3S.`.a0l0nQ6_2kW6l3P3R3V4vU8p6m6o6qU9u8r8s8uS:^9t9wQ:f:_R:i:gU!wQ']-]T5b1a5d!Q_OXZ`st!V!Z#d#h%c%k&f&h&o&q&r&t(f,k,p.S1|2P]!pQ!r']-]1a5dT#q]:l%[{OPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$`$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(P(R(X(`(t(v(z)z+R+V+a,h,k,p-a-i-w-}._.l.s/f0a0g0v1d1t1u1w1y1|2P2R2r2x3U3^5a5k5{5|6P6d6r8O8T8d8nS(y#y#zS.Y(i(j!s<Q$[$n'U)o,|-P.}2b3u5_6Z9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gU$fd)[,eS(l#p'fU*r%S(s3pU0b*x.f6}Q4v0mQ6m3QQ8s6pR9w8tm!tQ!r!v!y!z']'i'j'k-]-m1a5d5fQ'q!uS(b#g1vS-k'h'tQ/k*YQ/w*eQ2v-nQ4U/lS4_/x0SQ7X4PS7d4e4gQ9R7YR9Y7gQ#wbQ'p!uS(a#g1vS(c#m+QQ+S%dQ+d%vQ+j%|U-j'h'q'tQ.O(bQ/j*YQ/v*eQ/|*hQ0r+eQ1g,XS2s-k-nQ2{.WS4T/k/lS4^/w0SQ4a/{Q4c/}Q5n1hQ6g2vQ7W4PQ7[4US7`4_4gQ7e4fQ8V5oS9Q7X7YQ9U7aQ9W7dQ9g8WQ9}9RQ:O9VQ:Q9YQ:Y9hQ:b:PQ<T<OQ<`<XR<a<YV!wQ']-]%[aOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$`$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(P(R(X(`(t(v(z)z+R+V+a,h,k,p-a-i-w-}._.l.s/f0a0g0v1d1t1u1w1y1|2P2R2r2x3U3^5a5k5{5|6P6d6r8O8T8d8nS#wz!j!r;}$[$n'U)o,|-P.}2b3u5_6Z9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gR<T<f%[bOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$`$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(P(R(X(`(t(v(z)z+R+V+a,h,k,p-a-i-w-}._.l.s/f0a0g0v1d1t1u1w1y1|2P2R2r2x3U3^5a5k5{5|6P6d6r8O8T8d8nQ%dj!S%vy!i!u%y%z%{'S'b'c'd'h'r*e+h+i-Q-d-e-l/y0u2g2n2u4dS%|z!jQ+e%wQ,X&aW1h,Y,Z,[,]U5o1i1j1kS8W5p5qQ9h8X!r<O$[$n'U)o,|-P.}2b3u5_6Z9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gQ<X<eR<Y<f%OeOPXYstuvw!Z!`!g!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$`$e%k%r&P&h&k&o&q&r&t&x'Q'_'o(R(X(`(t(v(z)z+R+V+a,h,k,p-a-i-w-}._.l.s/f0a0g0v1d1t1u1w1y1|2P2R2r2x3U3^5a5k5{5|6P6d6r8O8T8d8nY#bWZ#W#Z(P!b%hm#h#i#l$y%c%f(Y(d(e(f*V*Z*^+T+U+W,g,}-{.R.S.T.V/e/h2U2|2}4Q6Y6kQ,f&l!p<P$[$n)o,|-P.}2b3u5_6Z9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gR<S'UU'Y!e%g*WR2e-W%QdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$`$e%k%r&P&h&k&o&q&r&t&x'Q'_'o(P(R(X(`(t(v(z)z+R+V,h,k,p-a-i-w-}.l.s/f0a0g0v1d1t1u1w1y1|2P2R2r2x3^5a5k5{5|6P6d8O8T8d8n!r)[$[$n'U)o,|-P.}2b3u5_6Z9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gQ,e&lQ0m+aQ3Q._Q6p3UR8t6r!b$Uc#Y%o(O(U(p(u)W)X)])c+r-p-u-z-|.k.n/X0e2p2w3]3n4q4z6c6h6w8l9s:m!P:z)Z)m-R.t2]2`3b3l3m3q3y6]6x7R7S7z8h8v8|8}:T:[<Z!f$Wc#Y%o(O(U(p(u)T)U)W)X)])c+r-p-u-z-|.k.n/X0e2p2w3]3n4q4z6c6h6w8l9s:m!T:|)Z)m-R.t2]2`3b3i3j3l3m3q3y6]6x7R7S7z8h8v8|8}:T:[<Z!^$[c#Y%o(O(U(p(u)])c+r-p-u-z-|.k.n/X0e2p2w3]3n4q4z6c6h6w8l9s:mQ3}/cz<g)Z)m-R.t2]2`3b3q3y6]6x7R7S7z8h8v8|8}:T:[<ZQ<l<nR<m<o'OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(P(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gS$oh$pR3v.|'VgOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n$p%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(P(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.|.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gT$kf$qQ$ifS)f$l)jR)r$qT$jf$qT)h$l)j'VhOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$`$e$n$p%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(P(R(X(`(t(v(z)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.l.s.|.}/f0a0g0v1d1t1u1w1y1|2P2R2b2r2x3U3^3u5_5a5k5{5|6P6Z6d6r8O8T8d8n9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<gT$oh$pQ$rhR)q$p%[jOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Y$`$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(P(R(X(`(t(v(z)z+R+V+a,h,k,p-a-i-w-}._.l.s/f0a0g0v1d1t1u1w1y1|2P2R2r2x3U3^5a5k5{5|6P6d6r8O8T8d8n!s<e$[$n'U)o,|-P.}2b3u5_6Z9`9q:k:n:o:r:s:t:u:v:w:x:y:z:{:|:};O;P;T;b;d;e;g;o;p;y;z<g#elOPXZst!Z!`!o#S#d#o#{$n%k&h&k&l&o&q&r&t&x'Q'_(z)o+V+a,h,k,p-a._.}/f0a1d1t1u1w1y1|2P2R3U3u5a5k5{5|6P6r8O8T8d!O%Si$d%P%R%]%^%b*O*Q*]*s*t.w/o0V0X0d3}4l9P<d<l<m#[(s#v$b$c$y$|)v)|*S*V*c+`+c+z+}.^/]/e/p/r1W1Z1c3T4Y4b4t4w5j6n7]7f8Q8q9d9v:W:`;U;W;[;^;`;h;j;m;q;s;u;w;{<p<qQ*}%`Q/Y){o3p;R;S;V;X;];_;a;i;k;n;r;t;v;x;|!O$zi$d%P%R%]%^%b*O*Q*]*s*t.w/o0V0X0d3}4l9P<d<l<mQ*_${U*h$}*W*kQ+O%aQ/}*i#[<V#v$b$c$y$|)v)|*S*V*c+`+c+z+}.^/]/e/p/r1W1Z1c3T4Y4b4t4w5j6n7]7f8Q8q9d9v:W:`;U;W;[;^;`;h;j;m;q;s;u;w;{<p<qn<W;R;S;V;X;];_;a;i;k;n;r;t;v;x;|Q<[<hQ<]<iQ<^<jR<_<k!O%Si$d%P%R%]%^%b*O*Q*]*s*t.w/o0V0X0d3}4l9P<d<l<m#[(s#v$b$c$y$|)v)|*S*V*c+`+c+z+}.^/]/e/p/r1W1Z1c3T4Y4b4t4w5j6n7]7f8Q8q9d9v:W:`;U;W;[;^;`;h;j;m;q;s;u;w;{<p<qo3p;R;S;V;X;];_;a;i;k;n;r;t;v;x;|noOXst!Z#d%k&o&q&r&t,k,p1|2PS*b$|*VQ,y&{Q,z&}R4X/p$z%Ti#v$b$c$d$y$|%P%R%]%^%b)v)|*O*Q*S*V*]*c*s*t+`+c+z+}.^.w/]/e/o/p/r0V0X0d1W1Z1c3T3}4Y4b4l4t4w5j6n7]7f8Q8q9P9d9v:W:`;R;S;U;V;W;X;[;];^;_;`;a;h;i;j;k;m;n;q;r;s;t;u;v;w;x;{;|<d<l<m<p<qQ+|&YQ1Y,OQ5Z1XR7w5[V*j$}*W*kU*j$}*W*kT5c1a5dU/{*g/f5aT4f0T8OQ+d%vQ/|*hQ0r+eQ1g,XQ5n1hQ8V5oQ9g8WR:Y9h!O%Pi$d%P%R%]%^%b*O*Q*]*s*t.w/o0V0X0d3}4l9P<d<l<mr*O$w)a*P*q+P/n0Z0[3s4V4p7V7h9|<U<b<cS0V*p0W#[;U#v$b$c$y$|)v)|*S*V*c+`+c+z+}.^/]/e/p/r1W1Z1c3T4Y4b4t4w5j6n7]7f8Q8q9d9v:W:`;U;W;[;^;`;h;j;m;q;s;u;w;{<p<qn;V;R;S;V;X;];_;a;i;k;n;r;t;v;x;|!^;h(q)_*X*a.b.e.i/U/Z/c/s0k1V1X3Y4W4[5Y5[6s6v7^7b7j7l9T9X:a<n<o`;i3o6y6|7Q8w9x9{:jS;s.d3ZT;t6{8z!O%Ri$d%P%R%]%^%b*O*Q*]*s*t.w/o0V0X0d3}4l9P<d<l<mv*Q$w)a*R*p+P/_/n0Z0[3s4V4h4p7V7h9|<U<b<cS0X*q0Y#[;W#v$b$c$y$|)v)|*S*V*c+`+c+z+}.^/]/e/p/r1W1Z1c3T4Y4b4t4w5j6n7]7f8Q8q9d9v:W:`;U;W;[;^;`;h;j;m;q;s;u;w;{<p<qn;X;R;S;V;X;];_;a;i;k;n;r;t;v;x;|!b;j(q)_*X*a.c.d.i/U/Z/c/s0k1V1X3W3Y4W4[5Y5[6s6t6v7^7b7j7l9T9X:a<n<od;k3o6z6{7Q8w8x9x9y9{:jS;u.e3[T;v6|8{rnOXst!V!Z#d%k&f&o&q&r&t,k,p1|2PQ&c!UR,h&lrnOXst!V!Z#d%k&f&o&q&r&t,k,p1|2PR&c!UQ,Q&ZR1U+ysnOXst!V!Z#d%k&f&o&q&r&t,k,p1|2PQ1b,VS5i1e1fU8P5g5h5lS9c8R8SS:U9b9eQ:c:VR:h:dQ&j!VR,a&fR5u1nS&O|&TR0}+pQ&o!WR,k&pR,q&uT1},p2PR,u&vQ,t&vR2W,uQ'v!{R-q'vSsOtQ#dXT%ns#dQ#OTR'x#OQ#RUR'z#RQ)x$vR/V)xQ#UVR'|#UQ#XWU(S#X(T-xQ(T#YR-x(UQ-U'VR2d-UQ.m(uS3_.m3`R3`.nQ-]']R2h-]Y!rQ']-]1a5dR'g!rQ.x)aR3t.xU#_W%f*VU(Z#_([-yQ([#`R-y(VQ-X'YR2f-Xt`OXst!V!Z#d%k&f&h&o&q&r&t,k,p1|2PS#hZ%cU#r`#h.SR.S(fQ(g#jQ.P(cW.X(g.P2y6iQ2y.QR6i2zQ)j$lR/O)jQ$phR)p$pQ$acU)^$a-t;QQ-t:mR;Q)mQ/i*YW4R/i4S7Z9SU4S/j/k/lS7Z4T4UR9S7[$X)}$w(q)_)a*X*a*p*q*z*{+P.d.e.g.h.i/U/Z/_/a/c/n/s0Z0[0k1V1X3W3X3Y3o3s4V4W4[4h4j4p5Y5[6s6t6u6v6{6|7O7P7Q7V7^7b7h7j7l8w8x8y9T9X9x9y9z9{9|:a:j<U<b<c<n<oQ/q*aU4Z/q4]7_Q4]/sR7_4[S*k$}*WR0P*kr*P$w)a*p*q+P/n0Z0[3s4V4p7V7h9|<U<b<c!^.b(q)_*X*a.d.e.i/U/Z/c/s0k1V1X3Y4W4[5Y5[6s6v7^7b7j7l9T9X:a<n<oU/`*P.b6ya6y3o6{6|7Q8w9x9{:jQ0W*pQ3Z.dU4i0W3Z8zR8z6{v*R$w)a*p*q+P/_/n0Z0[3s4V4h4p7V7h9|<U<b<c!b.c(q)_*X*a.d.e.i/U/Z/c/s0k1V1X3W3Y4W4[5Y5[6s6t6v7^7b7j7l9T9X:a<n<oU/b*R.c6ze6z3o6{6|7Q8w8x9x9y9{:jQ0Y*qQ3[.eU4k0Y3[8{R8{6|Q*v%VR0^*vQ4u0kR7k4uQ+X%iR0j+XQ5^1[S7y5^9aR9a7zQ,S&[R1_,SQ5d1aR7|5dQ1m,^S5s1m8ZR8Z5uQ0x+lW5P0x5R7q9]Q5R0{Q7q5QR9]7rQ+q&OR1O+qQ2P,pR6T2PYrOXst#dQ&s!ZQ+Z%kQ,j&oQ,l&qQ,m&rQ,o&tQ1z,kS1},p2PR6S1|Q%mpQ&w!_Q&z!aQ&|!bQ'O!cQ'n!uQ+Y%jQ+f%xQ+x&UQ,`&jQ,w&yW-h'h'p'q'tQ-o'lQ0O*jQ0s+gS1p,a,dQ2X,vQ2Y,yQ2Z,zQ2o-gW2q-j-k-n-pQ4x0tQ5U1RQ5X1VQ5m1gQ5w1rQ6R1{U6b2p2s2vQ6e2tQ7m4yQ7u5WQ7v5YQ7{5cQ8U5nQ8[5vS8k6c6gQ8m6fQ9^7sQ9f8VQ9k8]Q9r8lQ:S9_Q:X9gQ:]9sR:e:YQ%xyQ'a!iQ'l!uU+g%y%z%{Q-O'SU-c'b'c'dS-g'h'rQ/u*eS0t+h+iQ2a-QS2m-d-eQ2t-lQ4`/yQ4y0uQ6^2gQ6a2nQ6f2uR7c4dS$xi<dR*w%WU%Vi%W<dR0]*uQ$wiS(q#v+cS)_$b$cQ)a$dQ*X$yS*a$|*VQ*p%PQ*q%RQ*z%]Q*{%^Q+P%bQ.d;UQ.e;WQ.g;[Q.h;^Q.i;`Q/U)vS/Z)|/]Q/_*OQ/a*QQ/c*SQ/n*]S/s*c/eQ0Z*sQ0[*th0k+`.^1c3T5j6n8Q8q9d9v:W:`Q1V+zQ1X+}Q3W;hQ3X;jQ3Y;mS3o;R;SQ3s.wQ4V/oQ4W/pQ4[/rQ4h0VQ4j0XQ4p0dQ5Y1WQ5[1ZQ6s;qQ6t;sQ6u;uQ6v;wQ6{;VQ6|;XQ7O;]Q7P;_Q7Q;aQ7V3}Q7^4YQ7b4bQ7h4lQ7j4tQ7l4wQ8w;nQ8x;iQ8y;kQ9T7]Q9X7fQ9x;rQ9y;tQ9z;vQ9{;xQ9|9PQ:a;{Q:j;|Q<U<dQ<b<lQ<c<mQ<n<pR<o<qQ*x%[Q.f;YR6};ZnpOXst!Z#d%k&o&q&r&t,k,p1|2PQ!fPS#fZ#oQ&y!`U'e!o5a8OQ'{#SQ(|#{Q)n$nS,d&h&kQ,i&lQ,v&xQ,{'QQ-_'_Q.p(zQ/S)oS0h+V/fQ0n+aQ1x,hQ2k-aQ3R._Q3x.}Q4n0aQ5h1dQ5y1tQ5z1uQ6O1wQ6Q1yQ6V2RQ6q3UQ7T3uQ8S5kQ8`5{Q8a5|Q8c6PQ8u6rQ9e8TR9o8d#YcOPXZst!Z!`!o#d#o#{%k&h&k&l&o&q&r&t&x'Q'_(z+V+a,h,k,p-a._/f0a1d1t1u1w1y1|2P2R3U5a5k5{5|6P6r8O8T8dQ#YWQ#eYQ%ouQ%qvS%sw!gS(O#W(RQ(U#ZQ(p#uQ(u#xQ(}$OQ)O$PQ)P$QQ)Q$RQ)R$SQ)S$TQ)T$UQ)U$VQ)V$WQ)W$XQ)X$YQ)Z$[Q)]$`Q)c$eW)m$n)o.}3uQ+^%rQ+r&PS-R'U2bQ-p'oS-u(P-wQ-z(XQ-|(`Q.k(tQ.n(vQ.r:kQ.t:nQ.u:oQ.v:rQ/X)zQ0e+RQ2],|Q2`-PQ2p-iQ2w-}Q3].lQ3b:sQ3c:tQ3d:uQ3e:vQ3f:wQ3g:xQ3h:yQ3i:zQ3j:{Q3k:|Q3l:}Q3m;OQ3n.sQ3q;TQ3r;bQ3y;PQ4q0gQ4z0vQ6];dQ6c2rQ6h2xQ6w3^Q6x;eQ7R;gQ7S;oQ7z5_Q8h6ZQ8l6dQ8v;pQ8|;yQ8};zQ9s8nQ:T9`Q:[9qQ:m#SR<Z<gR#[WR'W!el!tQ!r!v!y!z']'i'j'k-]-m1a5d5fS'S!e-TS-Q'T'[R2g-ZR(w#xQ!fQT-[']-]]!qQ!r']-]1a5dQ#p]R'f:lR)b$dY!uQ']-]1a5dQ'h!rS'r!v!yS't!z5fS-l'i'jQ-n'kR2u-mT#kZ%cS#jZ%cS%im,gU(c#h#i#lS.Q(d(eQ.U(fQ0i+WQ2z.RU2{.S.T.VS6j2|2}R8o6kd#^W#W#Z%f(P(Y*V+T-{/er#gZm#h#i#l%c(d(e(f+W.R.S.T.V2|2}6kS*Y$y*^Q/l*ZQ1v,gQ2^,}Q4P/hQ6X2UQ7Y4QQ8g6YT<R'U+UV#aW%f*VU#`W%f*VS(Q#W(YU(V#Z+T/eS-S'U+UT-v(P-{V'Z!e%g*WQ$lfR)t$qT)i$l)jR3w.|T*[$y*^T*d$|*VQ0l+`Q3P.^Q5g1cQ6o3TQ8R5jQ8r6nQ9b8QQ9t8qQ:V9dQ:_9vQ:d:WR:g:`nqOXst!Z#d%k&o&q&r&t,k,p1|2PQ&i!VR,`&ftmOXst!U!V!Z#d%k&f&o&q&r&t,k,p1|2PR,g&lT%jm,gR1],PR,_&dQ&S|R+w&TR+m%}T&m!W&pT&n!W&pT2O,p2P",nodeNames:"⚠ ArithOp ArithOp ?. JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewTarget new NewExpression ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression TypeArgList CompareOp < declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies in const CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression InstantiationExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast ArrowFunction TypeParamList SequenceExpression InstantiationExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",maxTerm:376,context:pb,nodeProps:[["isolate",-8,5,6,14,34,36,48,50,52,""],["group",-26,9,17,19,65,204,208,212,213,215,218,221,231,233,239,241,243,245,248,254,260,262,264,266,268,270,271,"Statement",-34,13,14,29,32,33,39,48,51,52,54,59,67,69,73,77,79,81,82,107,108,117,118,135,138,140,141,142,143,144,146,147,166,167,169,"Expression",-23,28,30,34,38,40,42,171,173,175,176,178,179,180,182,183,184,186,187,188,198,200,202,203,"Type",-3,85,100,106,"ClassItem"],["openedBy",23,"<",35,"InterpolationStart",53,"[",57,"{",70,"(",159,"JSXStartCloseTag"],["closedBy",24,">",37,"InterpolationEnd",47,"]",58,"}",71,")",164,"JSXEndTag"]],propSources:[xb],skippedNodes:[0,5,6,274],repeatNodeCount:37,tokenData:"$Fq07[R!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#:O!R![#<_![!]#I_!]!^#Jk!^!_#Ku!_!`$![!`!a$$v!a!b$*T!b!c$,r!c!}Er!}#O$-|#O#P$/W#P#Q$4o#Q#R$5y#R#SEr#S#T$7W#T#o$8b#o#p$<r#p#q$=h#q#r$>x#r#s$@U#s$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$I|Er$I|$I}$Dk$I}$JO$Dk$JO$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr(n%d_$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$h&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$h&j(U!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(U!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$h&j(RpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(RpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Rp(U!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z07[+rq$h&j(Rp(U!b'w0/lOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z07[.ST(S#S$h&j'x0/lO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c07[.n_$h&j(Rp(U!b'x0/lOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)3p/x`$h&j!m),Q(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW1V`#u(Ch$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW2d_#u(Ch$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At3l_(Q':f$h&j(U!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$h&j(U!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$h&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$c`$h&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$c``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$c`$h&j(U!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(U!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$c`(U!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k%9[:hh$h&j(Rp(U!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__WS$h&j(Rp(U!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]WS$h&j(U!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXWS$h&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSWSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWWS(U!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]WS$h&j(RpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWWS(RpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYWS(Rp(U!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S%9[C}i$h&j(j%1l(Rp(U!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr%9[EoP;=`<%lCr07[FRk$h&j(Rp(U!b$[#t(O,2j(`$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$h&j(Rp(U!b$[#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv07[JPP;=`<%lEr(KWJ_`$h&j(Rp(U!b#m(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWKl_$h&j$P(Ch(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,#xLva(u+JY$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWNW`$h&j#y(Ch(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At! c_(T';W$h&j(RpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$h&j(RpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$h&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$c`$h&j(RpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(RpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$c`(RpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b/5|!'t_!i/.^$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&U!)O_!h!Lf$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z-!n!*[b$h&j(Rp(U!b(P%&f#n(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW!+o`$h&j(Rp(U!b#k(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;x!,|`$h&j(Rp(U!bo+4YOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,$U!.Z_!Y+Jf$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!/ec$h&j(Rp(U!b}.2^OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!0ya$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!2Z_!X!L^$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!3eg$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!5Vg$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!6wc$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!8_c$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!9uf$h&j(Rp(U!b#l(ChOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcxz!;Zz{#-}{!P!;Z!P!Q#/d!Q!^!;Z!^!_#(i!_!`#7S!`!a#8i!a!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z?O!;fb$h&j(Rp(U!b!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z>^!<w`$h&j(U!b!U7`OY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eq!Q!^!<n!^!_!Gr!_!}!<n!}#O!KS#O#P!Dy#P#o!<n#o#p!Gr#p;'S!<n;'S;=`!L]<%lO!<n<z!>Q^$h&j!U7`OY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@c!_!}!=y!}#O!CW#O#P!Dy#P#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!?Td$h&j!U7`O!^&c!_#W&c#W#X!>|#X#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#k!>|#k#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&c7`!@hX!U7`OY!@cZ!P!@c!P!Q!AT!Q!}!@c!}#O!Ar#O#P!Bq#P;'S!@c;'S;=`!CQ<%lO!@c7`!AYW!U7`#W#X!AT#Z#[!AT#]#^!AT#a#b!AT#g#h!AT#i#j!AT#j#k!AT#m#n!AT7`!AuVOY!ArZ#O!Ar#O#P!B[#P#Q!@c#Q;'S!Ar;'S;=`!Bk<%lO!Ar7`!B_SOY!ArZ;'S!Ar;'S;=`!Bk<%lO!Ar7`!BnP;=`<%l!Ar7`!BtSOY!@cZ;'S!@c;'S;=`!CQ<%lO!@c7`!CTP;=`<%l!@c<z!C][$h&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#O!CW#O#P!DR#P#Q!=y#Q#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DWX$h&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DvP;=`<%l!CW<z!EOX$h&jOY!=yYZ&cZ!^!=y!^!_!@c!_#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!EnP;=`<%l!=y>^!Ezl$h&j(U!b!U7`OY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#W&}#W#X!Eq#X#Z&}#Z#[!Eq#[#]&}#]#^!Eq#^#a&}#a#b!Eq#b#g&}#g#h!Eq#h#i&}#i#j!Eq#j#k!Eq#k#m&}#m#n!Eq#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}8r!GyZ(U!b!U7`OY!GrZw!Grwx!@cx!P!Gr!P!Q!Hl!Q!}!Gr!}#O!JU#O#P!Bq#P;'S!Gr;'S;=`!J|<%lO!Gr8r!Hse(U!b!U7`OY'}Zw'}x#O'}#P#W'}#W#X!Hl#X#Z'}#Z#[!Hl#[#]'}#]#^!Hl#^#a'}#a#b!Hl#b#g'}#g#h!Hl#h#i'}#i#j!Hl#j#k!Hl#k#m'}#m#n!Hl#n;'S'};'S;=`(f<%lO'}8r!JZX(U!bOY!JUZw!JUwx!Arx#O!JU#O#P!B[#P#Q!Gr#Q;'S!JU;'S;=`!Jv<%lO!JU8r!JyP;=`<%l!JU8r!KPP;=`<%l!Gr>^!KZ^$h&j(U!bOY!KSYZ&cZw!KSwx!CWx!^!KS!^!_!JU!_#O!KS#O#P!DR#P#Q!<n#Q#o!KS#o#p!JU#p;'S!KS;'S;=`!LV<%lO!KS>^!LYP;=`<%l!KS>^!L`P;=`<%l!<n=l!Ll`$h&j(Rp!U7`OY!LcYZ&cZr!Lcrs!=ys!P!Lc!P!Q!Mn!Q!^!Lc!^!_# o!_!}!Lc!}#O#%P#O#P!Dy#P#o!Lc#o#p# o#p;'S!Lc;'S;=`#&Y<%lO!Lc=l!Mwl$h&j(Rp!U7`OY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#W(r#W#X!Mn#X#Z(r#Z#[!Mn#[#](r#]#^!Mn#^#a(r#a#b!Mn#b#g(r#g#h!Mn#h#i(r#i#j!Mn#j#k!Mn#k#m(r#m#n!Mn#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r8Q# vZ(Rp!U7`OY# oZr# ors!@cs!P# o!P!Q#!i!Q!}# o!}#O#$R#O#P!Bq#P;'S# o;'S;=`#$y<%lO# o8Q#!pe(Rp!U7`OY)rZr)rs#O)r#P#W)r#W#X#!i#X#Z)r#Z#[#!i#[#])r#]#^#!i#^#a)r#a#b#!i#b#g)r#g#h#!i#h#i)r#i#j#!i#j#k#!i#k#m)r#m#n#!i#n;'S)r;'S;=`*Z<%lO)r8Q#$WX(RpOY#$RZr#$Rrs!Ars#O#$R#O#P!B[#P#Q# o#Q;'S#$R;'S;=`#$s<%lO#$R8Q#$vP;=`<%l#$R8Q#$|P;=`<%l# o=l#%W^$h&j(RpOY#%PYZ&cZr#%Prs!CWs!^#%P!^!_#$R!_#O#%P#O#P!DR#P#Q!Lc#Q#o#%P#o#p#$R#p;'S#%P;'S;=`#&S<%lO#%P=l#&VP;=`<%l#%P=l#&]P;=`<%l!Lc?O#&kn$h&j(Rp(U!b!U7`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#W%Z#W#X#&`#X#Z%Z#Z#[#&`#[#]%Z#]#^#&`#^#a%Z#a#b#&`#b#g%Z#g#h#&`#h#i%Z#i#j#&`#j#k#&`#k#m%Z#m#n#&`#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z9d#(r](Rp(U!b!U7`OY#(iZr#(irs!Grsw#(iwx# ox!P#(i!P!Q#)k!Q!}#(i!}#O#+`#O#P!Bq#P;'S#(i;'S;=`#,`<%lO#(i9d#)th(Rp(U!b!U7`OY*gZr*grs'}sw*gwx)rx#O*g#P#W*g#W#X#)k#X#Z*g#Z#[#)k#[#]*g#]#^#)k#^#a*g#a#b#)k#b#g*g#g#h#)k#h#i*g#i#j#)k#j#k#)k#k#m*g#m#n#)k#n;'S*g;'S;=`+Z<%lO*g9d#+gZ(Rp(U!bOY#+`Zr#+`rs!JUsw#+`wx#$Rx#O#+`#O#P!B[#P#Q#(i#Q;'S#+`;'S;=`#,Y<%lO#+`9d#,]P;=`<%l#+`9d#,cP;=`<%l#(i?O#,o`$h&j(Rp(U!bOY#,fYZ&cZr#,frs!KSsw#,fwx#%Px!^#,f!^!_#+`!_#O#,f#O#P!DR#P#Q!;Z#Q#o#,f#o#p#+`#p;'S#,f;'S;=`#-q<%lO#,f?O#-tP;=`<%l#,f?O#-zP;=`<%l!;Z07[#.[b$h&j(Rp(U!b'y0/l!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z07[#/o_$h&j(Rp(U!bT0/lOY#/dYZ&cZr#/drs#0nsw#/dwx#4Ox!^#/d!^!_#5}!_#O#/d#O#P#1p#P#o#/d#o#p#5}#p;'S#/d;'S;=`#6|<%lO#/d06j#0w]$h&j(U!bT0/lOY#0nYZ&cZw#0nwx#1px!^#0n!^!_#3R!_#O#0n#O#P#1p#P#o#0n#o#p#3R#p;'S#0n;'S;=`#3x<%lO#0n05W#1wX$h&jT0/lOY#1pYZ&cZ!^#1p!^!_#2d!_#o#1p#o#p#2d#p;'S#1p;'S;=`#2{<%lO#1p0/l#2iST0/lOY#2dZ;'S#2d;'S;=`#2u<%lO#2d0/l#2xP;=`<%l#2d05W#3OP;=`<%l#1p01O#3YW(U!bT0/lOY#3RZw#3Rwx#2dx#O#3R#O#P#2d#P;'S#3R;'S;=`#3r<%lO#3R01O#3uP;=`<%l#3R06j#3{P;=`<%l#0n05x#4X]$h&j(RpT0/lOY#4OYZ&cZr#4Ors#1ps!^#4O!^!_#5Q!_#O#4O#O#P#1p#P#o#4O#o#p#5Q#p;'S#4O;'S;=`#5w<%lO#4O00^#5XW(RpT0/lOY#5QZr#5Qrs#2ds#O#5Q#O#P#2d#P;'S#5Q;'S;=`#5q<%lO#5Q00^#5tP;=`<%l#5Q05x#5zP;=`<%l#4O01p#6WY(Rp(U!bT0/lOY#5}Zr#5}rs#3Rsw#5}wx#5Qx#O#5}#O#P#2d#P;'S#5};'S;=`#6v<%lO#5}01p#6yP;=`<%l#5}07[#7PP;=`<%l#/d)3h#7ab$h&j$P(Ch(Rp(U!b!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;ZAt#8vb$Y#t$h&j(Rp(U!b!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z'Ad#:Zp$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#U%Z#U#V#?i#V#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#d#Bq#d#l%Z#l#m#Es#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#<jk$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#>j_$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#?rd$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#A]f$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Bzc$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Dbe$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#E|g$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Gpi$h&j(Rp(U!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x#Il_!d$b$h&j#})Lv(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#Jv_al$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f#LS^h#)`#O-<U(Rp(U!b(y7`OY*gZr*grs'}sw*gwx)rx!P*g!P!Q#MO!Q!^*g!^!_#Mt!_!`$ f!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#MXX$j&j(Rp(U!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El#M}Z#o(Ch(Rp(U!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#Np!`#O*g#P;'S*g;'S;=`+Z<%lO*g(El#NyX$P(Ch(Rp(U!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El$ oX#p(Ch(Rp(U!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g*)x$!ga#]*!Y$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$#l!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(K[$#w_#h(Cl$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x$%Vag!*r#p(Ch$e#|$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$&[!`!a$'f!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$&g_#p(Ch$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$'qa#o(Ch$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$(v!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$)R`#o(Ch$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(Kd$*`a(m(Ct$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!a%Z!a!b$+e!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$+p`$h&j#z(Ch(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`$,}_!y$Ip$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f$.X_!P0,v$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$/]Z$h&jO!^$0O!^!_$0f!_#i$0O#i#j$0k#j#l$0O#l#m$2^#m#o$0O#o#p$0f#p;'S$0O;'S;=`$4i<%lO$0O(n$0VT_#S$h&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$0kO_#S(n$0p[$h&jO!Q&c!Q![$1f![!^&c!_!c&c!c!i$1f!i#T&c#T#Z$1f#Z#o&c#o#p$3|#p;'S&c;'S;=`&w<%lO&c(n$1kZ$h&jO!Q&c!Q![$2^![!^&c!_!c&c!c!i$2^!i#T&c#T#Z$2^#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$2cZ$h&jO!Q&c!Q![$3U![!^&c!_!c&c!c!i$3U!i#T&c#T#Z$3U#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3ZZ$h&jO!Q&c!Q![$0O![!^&c!_!c&c!c!i$0O!i#T&c#T#Z$0O#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$4PR!Q![$4Y!c!i$4Y#T#Z$4Y#S$4]S!Q![$4Y!c!i$4Y#T#Z$4Y#q#r$0f(n$4lP;=`<%l$0O#1[$4z_!V#)l$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$6U`#w(Ch$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;p$7c_$h&j(Rp(U!b([+4QOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$8qk$h&j(Rp(U!b(O,2j$^#t(`$I[OY%ZYZ&cZr%Zrs&}st%Ztu$8buw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$8b![!^%Z!^!_*g!_!c%Z!c!}$8b!}#O%Z#O#P&c#P#R%Z#R#S$8b#S#T%Z#T#o$8b#o#p*g#p$g%Z$g;'S$8b;'S;=`$<l<%lO$8b+d$:qk$h&j(Rp(U!b$^#tOY%ZYZ&cZr%Zrs&}st%Ztu$:fuw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$:f![!^%Z!^!_*g!_!c%Z!c!}$:f!}#O%Z#O#P&c#P#R%Z#R#S$:f#S#T%Z#T#o$:f#o#p*g#p$g%Z$g;'S$:f;'S;=`$<f<%lO$:f+d$<iP;=`<%l$:f07[$<oP;=`<%l$8b#Jf$<{X![#Hb(Rp(U!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g,#x$=sa(t+JY$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$+e#q;'S%Z;'S;=`+a<%lO%Z(Kd$?V_!Z(Cds`$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z?O$@a_!n7`$h&j(Rp(U!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$Aq|$h&j(Rp(U!b'w0/l$[#t(O,2j(`$I[OX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr07[$D|k$h&j(Rp(U!b'x0/l$[#t(O,2j(`$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",tokenizers:[gb,bb,yb,2,3,4,5,6,7,8,9,10,11,12,13,14,mb,new Jr("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOu~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!R~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(^~~",141,335),new Jr("j~RQYZXz{^~^O'{~~aP!P!Qd~iO'|~~",25,318)],topRules:{Script:[0,7],SingleExpression:[1,272],SingleClassItem:[2,273]},dialects:{jsx:0,ts:14769},dynamicPrecedences:{77:1,79:1,91:1,167:1,196:1},specialized:[{term:322,get:n=>Sb[n]||-1},{term:338,get:n=>wb[n]||-1},{term:92,get:n=>Qb[n]||-1}],tokenPrec:14793}),cu=[Se("function ${name}(${params}) {\n	${}\n}",{label:"function",detail:"definition",type:"keyword"}),Se("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n	${}\n}",{label:"for",detail:"loop",type:"keyword"}),Se("for (let ${name} of ${collection}) {\n	${}\n}",{label:"for",detail:"of loop",type:"keyword"}),Se("do {\n	${}\n} while (${})",{label:"do",detail:"loop",type:"keyword"}),Se("while (${}) {\n	${}\n}",{label:"while",detail:"loop",type:"keyword"}),Se(`try {
	\${}
} catch (\${error}) {
	\${}
}`,{label:"try",detail:"/ catch block",type:"keyword"}),Se("if (${}) {\n	${}\n}",{label:"if",detail:"block",type:"keyword"}),Se(`if (\${}) {
	\${}
} else {
	\${}
}`,{label:"if",detail:"/ else block",type:"keyword"}),Se(`class \${name} {
	constructor(\${params}) {
		\${}
	}
}`,{label:"class",detail:"definition",type:"keyword"}),Se('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),Se('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],Pb=cu.concat([Se("interface ${name} {\n	${}\n}",{label:"interface",detail:"definition",type:"keyword"}),Se("type ${name} = ${type}",{label:"type",detail:"definition",type:"keyword"}),Se("enum ${name} {\n	${}\n}",{label:"enum",detail:"definition",type:"keyword"})]),Va=new Rp,fu=new Set(["Script","Block","FunctionExpression","FunctionDeclaration","ArrowFunction","MethodDeclaration","ForStatement"]);function wi(n){return(e,t)=>{let i=e.node.getChild("VariableDefinition");return i&&t(i,n),!0}}const $b=["FunctionDeclaration"],vb={FunctionDeclaration:wi("function"),ClassDeclaration:wi("class"),ClassExpression:()=>!0,EnumDeclaration:wi("constant"),TypeAliasDeclaration:wi("type"),NamespaceDeclaration:wi("namespace"),VariableDefinition(n,e){n.matchContext($b)||e(n,"variable")},TypeDefinition(n,e){e(n,"type")},__proto__:null};function uu(n,e){let t=Va.get(e);if(t)return t;let i=[],s=!0;function r(o,l){let a=n.sliceString(o.from,o.to);i.push({label:a,type:l})}return e.cursor(se.IncludeAnonymous).iterate(o=>{if(s)s=!1;else if(o.name){let l=vb[o.name];if(l&&l(o,r)||fu.has(o.name))return!1}else if(o.to-o.from>8192){for(let l of uu(n,o.node))i.push(l);return!1}}),Va.set(e,i),i}const Ba=/^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/,du=["TemplateString","String","RegExp","LineComment","BlockComment","VariableDefinition","TypeDefinition","Label","PropertyDefinition","PropertyName","PrivatePropertyDefinition","PrivatePropertyName",".","?."];function Zb(n){let e=re(n.state).resolveInner(n.pos,-1);if(du.indexOf(e.name)>-1)return null;let t=e.name=="VariableName"||e.to-e.from<20&&Ba.test(n.state.sliceDoc(e.from,e.to));if(!t&&!n.explicit)return null;let i=[];for(let s=e;s;s=s.parent)fu.has(s.name)&&(i=i.concat(uu(n.state.doc,s)));return{options:i,from:t?e.from:n.pos,validFor:Ba}}const Xt=Ui.define({name:"javascript",parser:kb.configure({props:[So.add({IfStatement:Xi({except:/^\s*({|else\b)/}),TryStatement:Xi({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:Hp,SwitchBody:n=>{let e=n.textAfter,t=/^\s*\}/.test(e),i=/^\s*(case|default)\b/.test(e);return n.baseIndent+(t?0:i?1:2)*n.unit},Block:Fp({closing:"}"}),ArrowFunction:n=>n.baseIndent+n.unit,"TemplateString BlockComment":()=>null,"Statement Property":Xi({except:/^{/}),JSXElement(n){let e=/^\s*<\//.test(n.textAfter);return n.lineIndent(n.node.from)+(e?0:n.unit)},JSXEscape(n){let e=/\s*\}/.test(n.textAfter);return n.lineIndent(n.node.from)+(e?0:n.unit)},"JSXOpenTag JSXSelfClosingTag"(n){return n.column(n.node.from)+n.unit}}),Qo.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType":Xc,BlockComment(n){return{from:n.from+2,to:n.to-2}}})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),Ou={test:n=>/^JSX/.test(n.name),facet:Tc({commentTokens:{block:{open:"{/*",close:"*/}"}}})},Cb=Xt.configure({dialect:"ts"},"typescript"),Tb=Xt.configure({dialect:"jsx",props:[yo.add(n=>n.isTop?[Ou]:void 0)]}),Rb=Xt.configure({dialect:"jsx ts",props:[yo.add(n=>n.isTop?[Ou]:void 0)]},"typescript");let pu=n=>({label:n,type:"keyword"});const mu="break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(pu),Ab=mu.concat(["declare","implements","private","protected","public"].map(pu));function Mb(n={}){let e=n.jsx?n.typescript?Rb:Tb:n.typescript?Cb:Xt,t=n.typescript?Pb.concat(Ab):cu.concat(mu);return new Ac(e,[Xt.data.of({autocomplete:T0(du,Hf(t))}),Xt.data.of({autocomplete:Zb}),n.jsx?Yb:[]])}function Wb(n){for(;;){if(n.name=="JSXOpenTag"||n.name=="JSXSelfClosingTag"||n.name=="JSXFragmentTag")return n;if(n.name=="JSXEscape"||!n.parent)return null;n=n.parent}}function Ua(n,e,t=n.length){for(let i=e==null?void 0:e.firstChild;i;i=i.nextSibling)if(i.name=="JSXIdentifier"||i.name=="JSXBuiltin"||i.name=="JSXNamespacedName"||i.name=="JSXMemberExpression")return n.sliceString(i.from,Math.min(i.to,t));return""}const Xb=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),Yb=v.inputHandler.of((n,e,t,i,s)=>{if((Xb?n.composing:n.compositionStarted)||n.state.readOnly||e!=t||i!=">"&&i!="/"||!Xt.isActiveAt(n.state,e,-1))return!1;let r=s(),{state:o}=r,l=o.changeByRange(a=>{var h;let{head:c}=a,f=re(o).resolveInner(c-1,-1),u;if(f.name=="JSXStartTag"&&(f=f.parent),!(o.doc.sliceString(c-1,c)!=i||f.name=="JSXAttributeValue"&&f.to>c)){if(i==">"&&f.name=="JSXFragmentTag")return{range:a,changes:{from:c,insert:"</>"}};if(i=="/"&&f.name=="JSXStartCloseTag"){let d=f.parent,O=d.parent;if(O&&d.from==c-2&&((u=Ua(o.doc,O.firstChild,c))||((h=O.firstChild)===null||h===void 0?void 0:h.name)=="JSXFragmentTag")){let m=`${u}>`;return{range:y.cursor(c+m.length,-1),changes:{from:c,insert:m}}}}else if(i==">"){let d=Wb(f);if(d&&d.name=="JSXOpenTag"&&!/^\/?>|^<\//.test(o.doc.sliceString(c,c+2))&&(u=Ua(o.doc,d,c)))return{range:a,changes:{from:c,insert:`</${u}>`}}}}return{range:a}});return l.changes.empty?!1:(n.dispatch([r,o.update(l,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}),Db={class:"w-100% flex gap-8px"},qb=Na({name:"CodeMirror",__name:"code-mirror",props:{modelValue:{default:""},lang:{default:"js"},height:{default:"auto"},fontSize:{default:"13px"},readonly:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},placeholder:{}},emits:["update:modelValue"],setup(n,{emit:e}){const t=n,i=e,s=ku(),r=Qi(t.modelValue),{bool:o,setTrue:l}=Pu(),a=Ct(()=>{switch(t.lang){case"json":return ja();case"js":return Mb();default:return ja()}}),h=Ct(()=>({"&":{border:`1px solid ${s.darkMode?"#0000":"rgb(224, 224, 230)"}`,borderRadius:"6px",fontFamily:"system-ui",backgroundColor:"rgba(255, 255, 255, 0.1)"},".cm-line":{height:"21px",fontSize:t.fontSize,fontFamily:"monospace"},".cm-lineNumbers":{fontFamily:"system-ui",marginLeft:"5px",borderColor:"#0000",color:"rgba(255, 255, 255, 0.82)"},".cm-activeLineGutter":{backgroundColor:s.darkMode?"#262626":"#a1a1a1"},".cm-activeLine ":{backgroundColor:"#0000"},".cm-gutters":{borderTopLeftRadius:"6px",borderBottomLeftRadius:"6px",backgroundColor:s.darkMode?"#262626":"#a1a1a1"},".cm-content":{padding:"5px 5px 5px 0"},".cm-scroller":{height:t.height,overflowY:"auto",overflowX:"hidden"},".cm-placeholder":{color:"rgba(255, 255, 255, 0.38)"},"&.cm-focused":{outline:"none",borderRadius:"6px",border:"1px solid #3388ff",boxShadow:"0 0 0 2px rgba(19, 102, 255, 0.2)",zIndex:"100"}}));return nr(()=>r.value,()=>{if(t.lang==="json")try{const c=JSON.parse(r.value);typeof c=="object"&&(r.value=JSON.stringify(c))}catch{}i("update:modelValue",r.value)}),(c,f)=>{const u=Au,d=$u,O=vu;return _a(),La("div",Db,[pi(Vt($a),{modelValue:r.value,"onUpdate:modelValue":f[0]||(f[0]=m=>r.value=m),class:"snial-code-mirror w-100%",readonly:c.readonly,disabled:c.disabled,theme:h.value,placeholder:c.placeholder,basic:"","line-number":!1,lang:a.value,extensions:[Vt(Aa)]},null,8,["modelValue","readonly","disabled","theme","placeholder","lang","extensions"]),pi(d,{onClick:Vt(l)},{default:Eo(()=>[pi(u,{class:"text-18px"})]),_:1},8,["onClick"]),pi(O,{show:Vt(o),"onUpdate:show":f[2]||(f[2]=m=>Zu(o)?o.value=m:null),class:"max-w-90% w-600px",preset:"card",title:"编辑器",bordered:!1},{default:Eo(()=>[pi(Vt($a),{modelValue:r.value,"onUpdate:modelValue":f[1]||(f[1]=m=>r.value=m),class:"w-100%",readonly:c.readonly,disabled:c.disabled,theme:{...h.value,".cm-scroller":{height:"300px",overflowY:"auto",overflowX:"hidden"}},placeholder:c.placeholder,basic:"","line-number":!1,lang:a.value,extensions:[Vt(Aa)]},null,8,["modelValue","readonly","disabled","theme","placeholder","lang","extensions"])]),_:1},8,["show"])])}}}),Vb=Cu(qb,[["__scopeId","data-v-66eb7133"]]);export{Vb as _,Mb as j,Aa as o,$a as s};
