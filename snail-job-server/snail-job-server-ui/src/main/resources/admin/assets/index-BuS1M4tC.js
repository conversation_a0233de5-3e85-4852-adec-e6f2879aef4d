import{_}from"./log-drawer-E5RMkPe-.js";import{d as y,k as f,u as k,r as c,a as g,$ as e,b as v,o as $,f as I,l as T}from"./index-DOt4wG7_.js";import"./CollapseItem-B_nLIGZ5.js";const b={class:"h-full"},h=y({name:"log",__name:"index",setup(j){const o=f(),{routerPushByKey:i}=k(),t=c(o.query.type),a=c(),{taskBatchId:s,jobId:r,taskId:n,groupName:u,uniqueId:l}=o.query;function p(){["job","retry"].includes(t.value)||i("404"),t.value==="job"&&(a.value={taskBatchId:s,jobId:r,id:n}),t.value==="retry"&&(a.value={groupName:u,uniqueId:l})}p();const d=g(()=>t.value==="job"?`${e("common.systemTaskType.job")+e("page.log.title")} ------ JobId: ${r}, TaskId: ${n}, TaskBatchId: ${s}`:t.value==="retry"?`${e("common.systemTaskType.retry")+e("page.log.title")} ------ ${e("page.retryTask.groupName")}: ${u}, ${e("page.retryTask.title")}: ${l}`:e("page.log.title"));return(x,B)=>{const m=_;return $(),v("div",b,[I(m,{drawer:!1,title:d.value,type:t.value,"task-data":a.value},null,8,["title","type","task-data"])])}}}),D=T(h,[["__scopeId","data-v-2c34e8d9"]]);export{D as default};
