import{a as $,b as A}from"./log-drawer-8m3MM8ed.js";import{aR as E,b as I,o as _,e as v,d as M,k as O,r as l,i as U,cz as W,c as f,w as o,f as a,aO as q,a7 as L,g as m,B as j,x as F,t as G,_ as H,as as J,h as K,l as P}from"./index-DszOiTqy.js";import{u as Q,_ as X}from"./workflow.vue_vue_type_style_index_0_lang-C4ENNJsT.js";import{g as Y}from"./workflow-Cv1bSPMZ.js";import"./CollapseItem-Bt1g2lFv.js";import"./job-task-list-table.vue_vue_type_script_setup_true_lang-CXQFTYju.js";import"./table-CFZNYiHm.js";import"./Grid-DRtX9b3J.js";import"./job-C7tym23E.js";import"./detail-drawer-C9rZTAe5.js";import"./DescriptionsItem-CAGm-guL.js";import"./dynamic-input.vue_vue_type_script_setup_true_lang-DblGJAZk.js";import"./DynamicInput-BpjQPmr6.js";import"./code-mirror-D67swqKe.js";import"./cron-input.vue_vue_type_style_index_0_lang-Bk__aEhb.js";import"./notify-2iuonMav.js";import"./group-BF221ykj.js";const Z={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"};function ee(h,s){return _(),I("svg",Z,s[0]||(s[0]=[v("path",{fill:"currentColor","fill-rule":"evenodd",d:"M2.93 11.2c.072-4.96 4.146-8.95 9.149-8.95a9.16 9.16 0 0 1 7.814 4.357a.75.75 0 0 1-1.277.786a7.66 7.66 0 0 0-6.537-3.643c-4.185 0-7.575 3.328-7.648 7.448l.4-.397a.75.75 0 0 1 1.057 1.065l-1.68 1.666a.75.75 0 0 1-1.056 0l-1.68-1.666A.75.75 0 1 1 2.528 10.8zm16.856-.733a.75.75 0 0 1 1.055 0l1.686 1.666a.75.75 0 1 1-1.054 1.067l-.41-.405c-.07 4.965-4.161 8.955-9.18 8.955a9.2 9.2 0 0 1-7.842-4.356a.75.75 0 1 1 1.277-.788a7.7 7.7 0 0 0 6.565 3.644c4.206 0 7.61-3.333 7.68-7.453l-.408.403a.75.75 0 1 1-1.055-1.067z","clip-rule":"evenodd"},null,-1)]))}const oe=E({name:"solar-refresh-outline",render:ee}),te={class:"flex-center"},ne={class:"flex-center gap-8px"},ae=M({name:"workflow_form_batch",__name:"index",setup(h){const s=Q(),S=O(),g=l(!1),k=String(S.query.id),u=l({}),r=l(0),p=l(),w=new AbortController,t=l(!0),x=()=>{t.value=!0,w.abort(),clearTimeout(p.value),p.value=void 0},y=()=>{t.value||w.abort(),x(),u.value={},s.clear()},i=async()=>{g.value=!0;const{data:n,error:e}=await Y(k);e?(e==null?void 0:e.code)!=="ERR_CANCELED"&&y():(u.value=n,t.value=!(n.workflowBatchStatus&&[1,2].includes(n.workflowBatchStatus))||r.value===0,!t.value&&r.value!==0&&(clearTimeout(p.value),p.value=setTimeout(i,r.value*1e3))),g.value=!1},b=async n=>{if(n===-1){t.value&&(t.value=!1,await i());return}if(r.value=n,n===0){x();return}t.value=!1,await i()};U(()=>{s.clear(),s.setType(2),s.setId(k),i()}),W(()=>{y()});const B=l([{label:"Auto(off)",key:0},{label:"1s",key:1},{label:"5s",key:5},{label:"10s",key:10},{label:"30s",key:30},{label:"1m",key:60},{label:"5m",key:300}]);return(n,e)=>{const N=oe,C=H,T=j,d=L,R=q,V=$,z=A,D=J;return _(),f(K(X),{modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=c=>u.value=c),spinning:!1,disabled:"",onRefresh:e[2]||(e[2]=c=>i())},{buttons:o(()=>[v("div",te,[a(R,{trigger:"hover",width:"trigger",options:B.value,onSelect:b},{default:o(()=>[a(d,{placement:"left"},{trigger:o(()=>[a(T,{dashed:"",class:F(["w-136px",t.value?"mr-16px":"mr-42px"]),onClick:e[0]||(e[0]=c=>b(-1))},{icon:o(()=>[v("div",ne,[a(N,{class:"text-18px"}),m(" "+G(B.value.filter(c=>c.key===r.value)[0].label)+" ",1),a(C,{icon:"material-symbols:expand-more-rounded"})])]),_:1},8,["class"])]),default:o(()=>[e[3]||(e[3]=m(" 自动刷新频率 "))]),_:1})]),_:1},8,["options"]),t.value?(_(),f(d,{key:0,placement:"top"},{trigger:o(()=>[a(V,{class:"text-26px color-success"})]),default:o(()=>[e[4]||(e[4]=m(" 流程批次加载完成 "))]),_:1})):(_(),f(d,{key:1},{trigger:o(()=>[a(D,{size:"small"},{icon:o(()=>[a(z)]),_:1})]),default:o(()=>[e[5]||(e[5]=m(" 流程批次正在加载 "))]),_:1}))])]),_:1},8,["modelValue"])}}}),be=P(ae,[["__scopeId","data-v-dfdd102d"]]);export{be as default};
