import{d as l,u as m,a as u,b as _,o as d,e as x,f as o,_ as f,w as y,g as B,t as h,h as t,$ as v,B as g}from"./index-DszOiTqy.js";const k={class:"size-full min-h-520px flex-col-center gap-24px overflow-hidden"},N={class:"flex text-400px text-primary"},C=l({name:"ExceptionBase",__name:"exception-base",props:{type:{}},setup(n){const s=n,{routerPushByKey:a}=m(),c={403:"no-permission",404:"not-found",500:"service-error"},r=u(()=>c[s.type]);return($,e)=>{const i=f,p=g;return d(),_("div",k,[x("div",N,[o(i,{"local-icon":r.value},null,8,["local-icon"])]),o(p,{type:"primary",onClick:e[0]||(e[0]=b=>t(a)("root"))},{default:y(()=>[B(h(t(v)("common.backToHome")),1)]),_:1})])}}});export{C as _};
