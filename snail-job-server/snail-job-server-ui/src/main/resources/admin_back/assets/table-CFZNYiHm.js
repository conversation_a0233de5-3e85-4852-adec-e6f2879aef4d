import{d as V,j as A,k as N}from"./Grid-DRtX9b3J.js";import{d as q,aj as M,dZ as $,C as H,r as C,e9 as U,ea as W,eb as Y,af as K,n as L,cB as E,a as O,ao as X,L as Z,$ as z,q as J,aq as Q}from"./index-DszOiTqy.js";const ee=Object.assign(Object.assign({},N),W),oe=q({__GRID_ITEM__:!0,name:"FormItemGridItem",alias:["FormItemGi"],props:ee,setup(){const a=C(null);return{formItemInstRef:a,validate:(...t)=>{const{value:f}=a;if(f)return f.validate(...t)},restoreValidation:()=>{const{value:t}=a;t&&t.restoreValidation()}}},render(){return M(V,$(this.$.vnode.props||{},A),{default:()=>{const a=$(this.$props,U);return M(H,Object.assign({ref:"formItemInstRef"},a),this.$slots)}})}});function ae(a){const{loading:m,startLoading:y,endLoading:t}=Y(),{bool:f,setBool:h}=K(),{apiFn:w,apiParams:d,transformer:S,immediate:b=!0,getColumnChecks:_,getColumns:I}=a,g=L(E({...d})),s=C(a.columns()),P=C([]),i=C(_(a.columns())),p=O(()=>I(s.value,i.value));function D(){s.value=a.columns();const u=new Map(i.value.map(c=>[c.key,c.checked])),k=_(s.value);i.value=k.map(c=>({...c,checked:u.get(c.key)??c.checked}))}async function v(){var e;y();const u=T(g),k=await w(u),c=S(k);P.value=c.data,h(c.data.length===0),await((e=a.onFetched)==null?void 0:e.call(a,c)),t()}function T(u){const k={};return Object.entries(u).forEach(([c,e])=>{e!=null&&(k[c]=e)}),k}function r(u){Object.assign(g,u)}function j(){Object.assign(g,E(d))}return b&&(a.searchParams&&r(a.searchParams),v()),{loading:m,empty:f,data:P,columns:p,columnChecks:i,reloadColumns:D,getData:v,searchParams:g,updateSearchParams:r,resetSearchParams:j}}function re(a){const m=X(),y=Z(),t=O(()=>y.isMobile),{apiFn:f,apiParams:h,immediate:w}=a,d=a.showTotal||!0,S="__selection__",b="__expand__",{loading:_,empty:I,data:g,columns:s,columnChecks:P,reloadColumns:i,getData:p,searchParams:D,updateSearchParams:v,resetSearchParams:T}=ae({apiFn:f,apiParams:h,searchParams:a.searchParams,columns:a.columns,transformer:e=>{const{data:l=[],page:n=1,size:x=10,total:o=0}=e.data||{},F=x<=0?10:x;return{data:l.map((G,R)=>({...G,index:(n-1)*F+R+1})),pageNum:n,pageSize:F,total:o}},getColumnChecks:e=>{const l=[];return e.forEach(n=>{B(n)?l.push({key:n.key,title:n.title,checked:!0}):n.type==="selection"?l.push({key:S,title:z("common.check"),checked:!0}):n.type==="expand"&&l.push({key:b,title:z("common.expandColumn"),checked:!0})}),l},getColumns:(e,l)=>{const n=new Map;return e.forEach(o=>{B(o)?n.set(o.key,o):o.type==="selection"?n.set(S,o):o.type==="expand"&&n.set(b,o),t.value&&o.fixed&&(o.fixed=void 0)}),l.filter(o=>o.checked).map(o=>n.get(o.key))},onFetched:async e=>{const{pageNum:l,pageSize:n,total:x}=e;u({page:l,pageSize:n,itemCount:x})},immediate:w}),r=L({page:1,pageSize:10,showSizePicker:!0,itemCount:0,pageSizes:[10,15,20,25,30],onUpdatePage:async e=>{r.page=e,v({page:e,size:r.pageSize}),p()},onUpdatePageSize:async e=>{r.pageSize=e,r.page=1,v({page:r.page,size:e}),p()},prefix:e=>z("datatable.itemCount",{total:e.itemCount})}),j=O(()=>({...r,pageSlot:t.value?3:9,prefix:!t.value&&d?r.prefix:void 0}));function u(e){Object.assign(r,e)}async function k(e=1){u({page:e}),v({page:e,size:r.pageSize}),await p()}return m.run(()=>{J(()=>y.locale,()=>{i()})}),Q(()=>{m.stop()}),{loading:_,empty:I,data:g,columns:s,columnChecks:P,reloadColumns:i,pagination:r,mobilePagination:j,updatePagination:u,getData:p,getDataByPage:k,searchParams:D,updateSearchParams:v,resetSearchParams:()=>{T(),p()}}}function ce(a,m){const{bool:y,setTrue:t,setFalse:f}=K(),h=C("add");function w(){h.value="add",t()}const d=C(null);function S(s){h.value="edit";const P=a.value.find(i=>i.id===s)||null;d.value=E(P),t()}function b(s){var i;h.value="add";const P=a.value.find(p=>p.id===s)||null;d.value=E(P),(i=d.value)==null||delete i.id,t()}const _=C([]);async function I(){var s;(s=window.$message)==null||s.success(z("common.deleteSuccess")),_.value=[],await m()}async function g(){var s;(s=window.$message)==null||s.success(z("common.deleteSuccess")),await m()}return{drawerVisible:y,openDrawer:t,closeDrawer:f,operateType:h,handleAdd:w,editingData:d,handleEdit:S,handleCopy:b,checkedRowKeys:_,onBatchDeleted:I,onDeleted:g}}function B(a){return!!a.key}export{oe as _,ce as a,re as u};
