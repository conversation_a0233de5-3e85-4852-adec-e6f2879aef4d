import{bp as ee,b5 as x,c2 as te,d as ne,aj as u,c3 as ae,c4 as le,c5 as oe,D as se,c6 as ie,c7 as R,U as re,c8 as ue,c9 as de,ca as ce,b8 as me,ba as fe,r as m,bc as he,bd as ve,bb as V,cb as pe,a as v,cc as ge,be,bU as Se,cd as Te,ce as ye,bf as f}from"./index-DszOiTqy.js";const Ce=ee([x("auto-complete",`
 z-index: auto;
 position: relative;
 display: inline-flex;
 width: 100%;
 `),x("auto-complete-menu",`
 margin: 4px 0;
 box-shadow: var(--n-menu-box-shadow);
 `,[te({originalTransition:"background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier)"})])]);function Fe(t){return t.map(A)}function A(t){var o,a;return typeof t=="string"?{label:t,value:t}:t.type==="group"?{type:"group",label:(o=t.label)!==null&&o!==void 0?o:t.name,value:(a=t.value)!==null&&a!==void 0?a:t.name,key:t.key||t.name,children:t.children.map(l=>A(l))}:t}const Re=Object.assign(Object.assign({},V.props),{to:R.propTo,menuProps:Object,append:Boolean,bordered:{type:Boolean,default:void 0},clearable:{type:Boolean,default:void 0},defaultValue:{type:String,default:null},loading:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},placeholder:String,placement:{type:String,default:"bottom-start"},value:String,blurAfterSelect:Boolean,clearAfterSelect:Boolean,getShow:Function,showEmpty:Boolean,inputProps:Object,renderOption:Function,renderLabel:Function,size:String,options:{type:Array,default:()=>[]},zIndex:Number,status:String,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onSelect:[Function,Array],onBlur:[Function,Array],onFocus:[Function,Array],onInput:[Function,Array]}),we=ne({name:"AutoComplete",props:Re,slots:Object,setup(t){const{mergedBorderedRef:o,namespaceRef:a,mergedClsPrefixRef:s,inlineThemeDisabled:l}=me(t),h=fe(t),{mergedSizeRef:M,mergedDisabledRef:z,mergedStatusRef:P}=h,T=m(null),p=m(null),y=m(t.defaultValue),j=he(t,"value"),g=ve(j,y),d=m(!1),C=m(!1),_=V("AutoComplete","-auto-complete",Ce,pe,t,s),w=v(()=>Fe(t.options)),k=v(()=>{const{getShow:e}=t;return e?e(g.value||""):!!g.value}),D=v(()=>k.value&&d.value&&(t.showEmpty?!0:!!w.value.length)),E=v(()=>ge(w.value,Te("value","children")));function b(e){const{"onUpdate:value":n,onUpdateValue:i,onInput:r}=t,{nTriggerFormInput:S,nTriggerFormChange:Z}=h;i&&f(i,e),n&&f(n,e),r&&f(r,e),y.value=e,S(),Z()}function U(e){const{onSelect:n}=t,{nTriggerFormInput:i,nTriggerFormChange:r}=h;n&&f(n,e),i(),r()}function $(e){const{onBlur:n}=t,{nTriggerFormBlur:i}=h;n&&f(n,e),i()}function N(e){const{onFocus:n}=t,{nTriggerFormFocus:i}=h;n&&f(n,e),i()}function L(){C.value=!0}function K(){window.setTimeout(()=>{C.value=!1},0)}function H(e){var n,i,r;switch(e.key){case"Enter":if(!C.value){const S=(n=p.value)===null||n===void 0?void 0:n.getPendingTmNode();S&&(O(S.rawNode),e.preventDefault())}break;case"ArrowDown":(i=p.value)===null||i===void 0||i.next();break;case"ArrowUp":(r=p.value)===null||r===void 0||r.prev();break}}function O(e){(e==null?void 0:e.value)!==void 0&&(U(e.value),t.clearAfterSelect?b(null):e.label!==void 0&&b(t.append?`${g.value}${e.label}`:e.label),d.value=!1,t.blurAfterSelect&&Y())}function W(){b(null)}function q(e){d.value=!0,N(e)}function G(e){d.value=!1,$(e)}function J(e){d.value=!0,b(e)}function Q(e){O(e.rawNode)}function X(e){var n;!((n=T.value)===null||n===void 0)&&n.contains(ye(e))||(d.value=!1)}function Y(){var e,n;!((e=T.value)===null||e===void 0)&&e.contains(document.activeElement)&&((n=document.activeElement)===null||n===void 0||n.blur())}const B=v(()=>{const{common:{cubicBezierEaseInOut:e},self:{menuBoxShadow:n}}=_.value;return{"--n-menu-box-shadow":n,"--n-bezier":e}}),c=l?be("auto-complete",void 0,B,t):void 0,F=m(null),I={focus:()=>{var e;(e=F.value)===null||e===void 0||e.focus()},blur:()=>{var e;(e=F.value)===null||e===void 0||e.blur()}};return{focus:I.focus,blur:I.blur,inputInstRef:F,uncontrolledValue:y,mergedValue:g,isMounted:Se(),adjustedTo:R(t),menuInstRef:p,triggerElRef:T,treeMate:E,mergedSize:M,mergedDisabled:z,active:D,mergedStatus:P,handleClear:W,handleFocus:q,handleBlur:G,handleInput:J,handleToggle:Q,handleClickOutsideMenu:X,handleCompositionStart:L,handleCompositionEnd:K,handleKeyDown:H,mergedTheme:_,cssVars:l?void 0:B,themeClass:c==null?void 0:c.themeClass,onRender:c==null?void 0:c.onRender,mergedBordered:o,namespace:a,mergedClsPrefix:s}},render(){const{mergedClsPrefix:t}=this;return u("div",{class:`${t}-auto-complete`,ref:"triggerElRef",onKeydown:this.handleKeyDown,onCompositionstart:this.handleCompositionStart,onCompositionend:this.handleCompositionEnd},u(ae,null,{default:()=>[u(le,null,{default:()=>{const o=this.$slots.default;if(o)return oe("default",o,{handleInput:this.handleInput,handleFocus:this.handleFocus,handleBlur:this.handleBlur,value:this.mergedValue});const{mergedTheme:a}=this;return u(se,{ref:"inputInstRef",status:this.mergedStatus,theme:a.peers.Input,themeOverrides:a.peerOverrides.Input,bordered:this.mergedBordered,value:this.mergedValue,placeholder:this.placeholder,size:this.mergedSize,disabled:this.mergedDisabled,clearable:this.clearable,loading:this.loading,inputProps:this.inputProps,onClear:this.handleClear,onFocus:this.handleFocus,onUpdateValue:this.handleInput,onBlur:this.handleBlur},{suffix:()=>{var s,l;return(l=(s=this.$slots).suffix)===null||l===void 0?void 0:l.call(s)},prefix:()=>{var s,l;return(l=(s=this.$slots).prefix)===null||l===void 0?void 0:l.call(s)}})}}),u(ie,{show:this.active,to:this.adjustedTo,containerClass:this.namespace,zIndex:this.zIndex,teleportDisabled:this.adjustedTo===R.tdkey,placement:this.placement,width:"target"},{default:()=>u(re,{name:"fade-in-scale-up-transition",appear:this.isMounted},{default:()=>{var o;if((o=this.onRender)===null||o===void 0||o.call(this),!this.active)return null;const{menuProps:a}=this;return ue(u(ce,Object.assign({},a,{clsPrefix:t,ref:"menuInstRef",theme:this.mergedTheme.peers.InternalSelectMenu,themeOverrides:this.mergedTheme.peerOverrides.InternalSelectMenu,"auto-pending":!0,class:[`${t}-auto-complete-menu`,this.themeClass,a==null?void 0:a.class],style:[a==null?void 0:a.style,this.cssVars],treeMate:this.treeMate,multiple:!1,renderLabel:this.renderLabel,renderOption:this.renderOption,size:"medium",onToggle:this.handleToggle}),{empty:()=>{var s,l;return(l=(s=this.$slots).empty)===null||l===void 0?void 0:l.call(s)}}),[[de,this.handleClickOutsideMenu,void 0,{capture:!0}]])}})})]}))}});export{we as _};
