import{d,r as o,q as r,c as v,o as N,ad as b,f as g,h as c,bC as y,$ as S,aZ as h}from"./index-DszOiTqy.js";import{f as L}from"./retry-scene-D93Q9xil.js";const R=d({name:"SelectScene",__name:"select-scene",props:{groupName:{},clearable:{type:Boolean,default:!1}},emits:["update:value"],setup(u,{emit:p}){const m=p,a=u,n=o([]),e=o("");async function i(t){if(a.groupName){const l=await L({groupName:t});n.value=l.data.map(s=>s.sceneName)}else e.value="",n.value=[]}r(()=>a.groupName,()=>{i(a.groupName)}),r(()=>e.value,()=>{m("update:value",e.value)});const f=t=>g(h,null,{default:()=>[t.label]});return(t,l)=>{const s=b;return N(),v(s,{value:e.value,"onUpdate:value":l[0]||(l[0]=_=>e.value=_),placeholder:c(S)("page.retryTask.form.sceneName"),options:c(y)(n.value),clearable:a.clearable,"render-label":f,filterable:""},null,8,["value","placeholder","options","clearable"])}}});export{R as _};
