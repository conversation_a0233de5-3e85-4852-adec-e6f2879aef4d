import{a as B,b as I}from"./search-form-BMevxHlE.js";import{d as S,a0 as W,a1 as z,c as F,o as R,w as k,f as t,h as e,$ as o,L as M,b as $,ag as w,G as j,bk as C,Z as y,g as N,a3 as T,F as D,ai as O}from"./index-DszOiTqy.js";import{d as U}from"./dashboard-CKwc71eo.js";import{_ as V,u as G,a as K}from"./table-CFZNYiHm.js";import{_ as q}from"./select-group.vue_vue_type_script_setup_true_lang-zcRIDdKa.js";import{b as A}from"./Grid-DRtX9b3J.js";import"./CollapseItem-Bt1g2lFv.js";import"./group-BF221ykj.js";const E=S({name:"PodsSearch",__name:"pods-search",props:{model:{required:!0},modelModifiers:{}},emits:W(["reset","search"],["update:model"]),setup(d,{emit:b}){const c=b,l=z(d,"model");function h(){c("reset")}function i(){c("search")}return(x,g)=>{const u=V,v=B;return R(),F(v,{model:l.value,onSearch:i,onReset:h},{default:k(()=>[t(u,{span:"24 s:12 m:6",label:e(o)("page.pods.groupName"),path:"groupName",class:"pr-24px"},{default:k(()=>[t(q,{value:l.value.groupName,"onUpdate:value":g[0]||(g[0]=p=>l.value.groupName=p),placeholder:e(o)("page.pods.form.groupName"),clearable:""},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])}}}),H={class:"min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"};function P(d){return typeof d=="function"||Object.prototype.toString.call(d)==="[object Object]"&&!O(d)}const ae=S({name:"pods",__name:"index",setup(d){const b=M(),{columns:c,columnChecks:l,data:h,getData:i,loading:x,mobilePagination:g,searchParams:u,resetSearchParams:v}=G({apiFn:U,apiParams:{page:1,size:10,groupName:null},columns:()=>[{key:"hostId",title:o("page.pods.hostId"),align:"left",resizable:!0,width:150,minWidth:150,maxWidth:200},{key:"nodeType",title:o("page.pods.nodeType"),align:"center",width:80,render:n=>{if(n.nodeType===null)return null;const a={1:"info",2:"primary"},r=o(C[n.nodeType]);return t(y,{type:a[n.nodeType]},P(r)?r:{default:()=>[r]})}},{key:"groupName",title:o("page.pods.groupName"),align:"left",width:120,resizable:!0,minWidth:120,maxWidth:200},{key:"hostIp",title:o("page.pods.hostIp"),align:"left",width:120},{key:"hostPort",title:o("page.pods.hostPort"),align:"left",width:80},{key:"consumerBuckets",title:o("page.pods.consumerBuckets"),align:"left",width:300,resizable:!0,minWidth:120,maxWidth:400,render:n=>{if(n.nodeType===null)return null;const a=f=>{var s;const m=f?(s=n.consumerBuckets)==null?void 0:s.slice(0,f):n.consumerBuckets;return m==null?void 0:m.map(_=>t(y,{type:"error",key:_,class:"m-1 justify-center"},P(_)?_:{default:()=>[_]}))},r=()=>t(y,{type:"info"},{default:()=>[n.contextPath??"/"]});return n.nodeType===1?t(T,null,[N("Path: "),r()]):t(T,null,[t("span",null,[N("Bucket: ")]),a(10),t(D,{trigger:"hover"},{trigger:()=>t(y,{type:"error"},{default:()=>[N("...")]}),default:()=>t("div",{class:"grid grid-cols-16"},[a()])})])}},{key:"updateDt",title:o("page.pods.updateDt"),align:"left",width:130}]}),{checkedRowKeys:p}=K(h,i);return(n,a)=>{const r=I,f=A,m=j;return R(),$("div",H,[t(E,{model:e(u),"onUpdate:model":a[0]||(a[0]=s=>w(u)?u.value=s:null),onReset:e(v),onSearch:e(i)},null,8,["model","onReset","onSearch"]),t(m,{title:e(o)("page.pods.title"),bordered:!1,size:"small","header-class":"view-card-header",class:"sm:flex-1-hidden card-wrapper"},{"header-extra":k(()=>[t(r,{columns:e(l),"onUpdate:columns":a[1]||(a[1]=s=>w(l)?l.value=s:null),"disabled-delete":e(p).length===0,loading:e(x),"show-add":!1,"show-delete":!1,onRefresh:e(i)},null,8,["columns","disabled-delete","loading","onRefresh"])]),default:k(()=>[t(f,{"checked-row-keys":e(p),"onUpdate:checkedRowKeys":a[2]||(a[2]=s=>w(p)?p.value=s:null),columns:e(c),data:e(h),"flex-height":!e(b).isMobile,"scroll-x":962,loading:e(x),remote:"","row-key":s=>s.hostId,pagination:e(g),class:"sm:h-full"},null,8,["checked-row-keys","columns","data","flex-height","loading","row-key","pagination"])]),_:1},8,["title"])])}}});export{ae as default};
