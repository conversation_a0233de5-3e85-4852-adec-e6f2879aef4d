import{d as l,k as u,r as i,i as f,c as d,o as c,h as _}from"./index-DszOiTqy.js";import{u as k,_ as v}from"./workflow.vue_vue_type_style_index_0_lang-C4ENNJsT.js";import{h as g}from"./workflow-Cv1bSPMZ.js";import"./job-task-list-table.vue_vue_type_script_setup_true_lang-CXQFTYju.js";import"./table-CFZNYiHm.js";import"./Grid-DRtX9b3J.js";import"./job-C7tym23E.js";import"./detail-drawer-C9rZTAe5.js";import"./DescriptionsItem-CAGm-guL.js";import"./log-drawer-8m3MM8ed.js";import"./CollapseItem-Bt1g2lFv.js";import"./dynamic-input.vue_vue_type_script_setup_true_lang-DblGJAZk.js";import"./DynamicInput-BpjQPmr6.js";import"./code-mirror-D67swqKe.js";import"./cron-input.vue_vue_type_style_index_0_lang-Bk__aEhb.js";import"./notify-2iuonMav.js";import"./group-BF221ykj.js";const j=l({name:"workflow_form_detail",__name:"index",setup(w){const e=k(),s=u(),t=i(!1),a=String(s.query.id),r=i({}),m=async()=>{t.value=!0;const{data:n,error:o}=await g(a);o||(r.value=n),t.value=!1};return f(()=>{e.clear(),e.setType(1),e.setId(a),m()}),(n,o)=>(c(),d(_(v),{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=p=>r.value=p),spinning:t.value,disabled:""},null,8,["modelValue","spinning"]))}});export{j as default};
