package com.aizuda.snailjob.template.datasource.persistence.mapper;

import com.aizuda.snailjob.template.datasource.persistence.po.Job;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-24
 */
@Mapper
public interface JobMapper extends BaseMapper<Job> {

    int updateBatchNextTriggerAtById(@Param("list") List<Job> list);

}
