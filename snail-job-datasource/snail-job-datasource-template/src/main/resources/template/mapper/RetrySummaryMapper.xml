<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aizuda.snailjob.template.datasource.persistence.mapper.RetrySummaryMapper">

    <select id="selectRetryTaskList"
            resultType="com.aizuda.snailjob.template.datasource.persistence.dataobject.DashboardRetryLineResponseDO$Task">
        SELECT group_name                                              AS groupName,
               SUM(CASE WHEN (scene_status = 1) THEN 1 ELSE 0 END)     AS run,
               COUNT(*)                                                AS total
          FROM sj_retry_scene_config
        ${ew.customSqlSegment}
         GROUP BY namespace_id, group_name
         ORDER BY group_name
    </select>

    <select id="selectRetryTaskListCount" resultType="long">
        SELECT COUNT(DISTINCT group_name)
          FROM sj_retry_scene_config
        ${ew.customSqlSegment}
    </select>

</mapper>