<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aizuda.snailjob.template.datasource.persistence.mapper.JobLogMessageMapper">

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sj_job_log_message (namespace_id, group_name, job_id, task_batch_id, task_id,
                                        log_num, message, create_dt, real_time)
        VALUES
            <foreach collection="list" item="item" separator=",">
                (
                    #{item.namespaceId},
                    #{item.groupName},
                    #{item.jobId},
                    #{item.taskBatchId},
                    #{item.taskId},
                    #{item.logNum},
                    #{item.message},
                    #{item.createDt},
                    #{item.realTime}
                )
            </foreach>
    </insert>

</mapper>
