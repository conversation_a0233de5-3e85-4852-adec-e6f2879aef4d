<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aizuda.snailjob.template.datasource.persistence.mapper.RetryDeadLetterMapper">

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sj_retry_dead_letter (namespace_id, group_name, group_id, scene_name, scene_id, idempotent_id, biz_no,
                                          executor_name, args_str, ext_attrs, create_dt)
        VALUES
            <foreach collection="list" item="item" separator=",">
                (
                    #{item.namespaceId,jdbcType=VARCHAR},
                    #{item.groupName,jdbcType=VARCHAR},
                    #{item.groupId,jdbcType=BIGINT},
                    #{item.sceneName,jdbcType=VARCHAR},
                    #{item.sceneId,jdbcType=BIGINT},
                    #{item.idempotentId,jdbcType=VARCHAR},
                    #{item.bizNo,jdbcType=VARCHAR},
                    #{item.executorName,jdbcType=VARCHAR},
                    #{item.argsStr,jdbcType=VARCHAR},
                    #{item.extAttrs,jdbcType=VARCHAR},
                    #{item.createDt,jdbcType=TIMESTAMP}
                )
            </foreach>
    </insert>

</mapper>
