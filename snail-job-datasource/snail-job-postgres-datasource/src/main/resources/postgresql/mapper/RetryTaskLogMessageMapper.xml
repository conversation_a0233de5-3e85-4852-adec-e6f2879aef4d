<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aizuda.snailjob.template.datasource.persistence.mapper.RetryTaskLogMessageMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sj_retry_task_log_message (namespace_id, group_name, retry_task_id, retry_id, log_num, message,
                                            create_dt, real_time)
        VALUES
            <foreach collection="list" item="item" separator=",">
                (
                    #{item.namespaceId},
                    #{item.groupName},
                    #{item.retryTaskId},
                    #{item.retryId},
                    #{item.logNum},
                    #{item.message},
                    #{item.createDt},
                    #{item.realTime}
                )
            </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        UPDATE sj_retry_task_log_message rt,
                (
                    <foreach collection="list" item="item" index="index" separator="UNION ALL">
                        SELECT
                            #{item.id} AS id,
                            #{item.message} AS message,
                            #{item.logNum} AS log_num
                    </foreach>
                ) tt
           SET rt.message = tt.message,
               rt.log_num = tt.log_num,
               rt.update_dt = CURRENT_TIMESTAMP
         WHERE rt.id = tt.id
    </update>

</mapper>
