<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aizuda.snailjob.template.datasource.persistence.mapper.RetryMapper">

    <!-- 定义批量新增的 SQL 映射 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sj_retry (namespace_id, group_name, group_id, scene_name, scene_id,
        idempotent_id, biz_no, executor_name, args_str, ext_attrs,
        next_trigger_at, task_type, retry_status, create_dt, bucket_index, parent_id, deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.namespaceId}, #{item.groupName}, #{item.groupId}, #{item.sceneName}, #{item.sceneId}, #{item.idempotentId},
            #{item.bizNo},#{item.executorName}, #{item.argsStr}, #{item.extAttrs}, #{item.nextTriggerAt}, #{item.taskType},
            #{item.retryStatus}, #{item.createDt}, #{item.bucketIndex}, #{item.parentId}, #{item.deleted}
            )
        </foreach>
    </insert>

    <update id="updateBatchNextTriggerAtById" parameterType="java.util.List">
        UPDATE sj_retry AS rt
           SET next_trigger_at = tt.next_trigger_at,
               update_dt = CURRENT_TIMESTAMP
          FROM (
                <foreach collection="list" item="item" index="index" separator="UNION ALL">
                    SELECT
                        #{item.id}              AS id,
                        #{item.nextTriggerAt}   AS next_trigger_at
                </foreach>
                ) AS tt
          WHERE rt.id = tt.id
    </update>
</mapper>
