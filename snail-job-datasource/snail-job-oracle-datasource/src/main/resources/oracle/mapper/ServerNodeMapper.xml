<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aizuda.snailjob.template.datasource.persistence.mapper.ServerNodeMapper">

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        INSERT INTO sj_server_node (namespace_id, group_name, host_id, host_ip, host_port,
                                    expire_at, node_type, ext_attrs, create_dt)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT
                #{item.namespaceId,jdbcType=VARCHAR},
                #{item.groupName,jdbcType=VARCHAR},
                #{item.hostId,jdbcType=VARCHAR},
                #{item.hostIp,jdbcType=VARCHAR},
                #{item.hostPort,jdbcType=INTEGER},
                #{item.expireAt,jdbcType=TIMESTAMP},
                #{item.nodeType,jdbcType=TINYINT},
                #{item.extAttrs,jdbcType=VARCHAR},
                #{item.createDt,jdbcType=TIMESTAMP}
            FROM DUAL
        </foreach>
    </insert>

    <update id="updateBatchExpireAt" parameterType="java.util.List">
        <foreach collection="list" item="item" open="BEGIN" separator=";" close=";END;">
            UPDATE sj_server_node
               SET expire_at = #{item.expireAt},
                   update_dt = CURRENT_TIMESTAMP
             WHERE host_id = #{item.hostId}
               AND host_ip = #{item.hostIp}
        </foreach>
    </update>

</mapper>
