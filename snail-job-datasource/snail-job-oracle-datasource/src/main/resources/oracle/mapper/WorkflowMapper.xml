<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aizuda.snailjob.template.datasource.persistence.mapper.WorkflowMapper">

    <update id="updateBatchNextTriggerAtById" parameterType="java.util.List">
        <foreach collection="list" item="item" open="BEGIN" separator=";" close=";END;">
            UPDATE sj_workflow
               SET next_trigger_at = #{item.nextTriggerAt},
                   update_dt = CURRENT_TIMESTAMP
             WHERE id = #{item.id}
        </foreach>
    </update>

</mapper>
