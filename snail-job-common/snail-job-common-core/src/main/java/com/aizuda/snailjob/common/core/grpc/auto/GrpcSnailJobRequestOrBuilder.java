// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: snail_job_grpc_service.proto

package com.aizuda.snailjob.common.core.grpc.auto;

public interface GrpcSnailJobRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:GrpcSnailJobRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 reqId = 1;</code>
   * @return The reqId.
   */
  long getReqId();

  /**
   * <code>.Metadata metadata = 2;</code>
   * @return Whether the metadata field is set.
   */
  boolean hasMetadata();
  /**
   * <code>.Metadata metadata = 2;</code>
   * @return The metadata.
   */
  com.aizuda.snailjob.common.core.grpc.auto.Metadata getMetadata();
  /**
   * <code>.Metadata metadata = 2;</code>
   */
  com.aizuda.snailjob.common.core.grpc.auto.MetadataOrBuilder getMetadataOrBuilder();

  /**
   * <code>string body = 3;</code>
   * @return The body.
   */
  java.lang.String getBody();
  /**
   * <code>string body = 3;</code>
   * @return The bytes for body.
   */
  com.google.protobuf.ByteString
      getBodyBytes();
}
