package com.aizuda.snailjob.common.log.level;

/**
 * 日志等级
 *
 * <AUTHOR>
 */
public enum Level {
    /**
     * 'ALL' log level.
     */
    ALL,
    /**
     * 'TRACE' log level.
     */
    TRACE,
    /**
     * 'DEBUG' log level.
     */
    DEBUG,
    /**
     * 'INFO' log level.
     */
    INFO,
    /**
     * 'WARN' log level.
     */
    WARN,
    /**
     * 'ERROR' log level.
     */
    ERROR,
    /**
     * 'FATAL' log level.
     */
    FATAL,
    /**
     * 'OFF' log.
     */
    OFF
}
